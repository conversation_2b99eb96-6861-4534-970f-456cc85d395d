{"version": 3, "sources": ["../../maath/dist/objectSpread2-284232a6.esm.js", "../../maath/dist/isNativeReflectConstruct-5594d075.esm.js", "../../maath/dist/matrix-baa530bf.esm.js", "../../maath/dist/triangle-b62b9067.esm.js", "../../maath/dist/misc-7d870b3c.esm.js", "../../maath/dist/vector2-d2bf51f1.esm.js", "../../maath/dist/vector3-0a088b7f.esm.js", "../../maath/dist/buffer-d2a4726c.esm.js", "../../maath/dist/classCallCheck-9098b006.esm.js", "../../maath/dist/index-43782085.esm.js", "../../maath/dist/easing-3be59c6d.esm.js", "../../maath/dist/geometry-982366ff.esm.js", "../../maath/dist/three-eb2ad8c0.esm.js", "../../three/examples/jsm/postprocessing/Pass.js", "../../n8ao/dist/src/N8AOPass.js", "../../n8ao/dist/src/FullScreenTriangle.js", "../../n8ao/dist/src/EffectShader.js", "../../n8ao/dist/src/EffectCompositer.js", "../../n8ao/dist/src/PoissionBlur.js", "../../n8ao/dist/src/DepthDownSample.js", "../../n8ao/dist/src/N8AOPostPass.js", "../../n8ao/dist/src/BlueNoise.js", "../../n8ao/dist/src/compat.js", "../../@react-three/postprocessing/src/Selection.tsx", "../../@react-three/postprocessing/src/EffectComposer.tsx", "../../@react-three/postprocessing/src/util.tsx", "../../@react-three/postprocessing/src/effects/DepthOfField.tsx", "../../@react-three/postprocessing/src/effects/Autofocus.tsx", "../../@react-three/postprocessing/src/effects/LensFlare.tsx", "../../@react-three/postprocessing/src/effects/Bloom.tsx", "../../@react-three/postprocessing/src/effects/BrightnessContrast.tsx", "../../@react-three/postprocessing/src/effects/ChromaticAberration.tsx", "../../@react-three/postprocessing/src/effects/ColorAverage.tsx", "../../@react-three/postprocessing/src/effects/ColorDepth.tsx", "../../@react-three/postprocessing/src/effects/Depth.tsx", "../../@react-three/postprocessing/src/effects/DotScreen.tsx", "../../@react-three/postprocessing/src/effects/Glitch.tsx", "../../@react-three/postprocessing/src/effects/GodRays.tsx", "../../@react-three/postprocessing/src/effects/Grid.tsx", "../../@react-three/postprocessing/src/effects/HueSaturation.tsx", "../../@react-three/postprocessing/src/effects/Noise.tsx", "../../@react-three/postprocessing/src/effects/Outline.tsx", "../../@react-three/postprocessing/src/effects/Pixelation.tsx", "../../@react-three/postprocessing/src/effects/ScanlineEffect.tsx", "../../@react-three/postprocessing/src/effects/SelectiveBloom.tsx", "../../@react-three/postprocessing/src/effects/Sepia.tsx", "../../@react-three/postprocessing/src/effects/SSAO.tsx", "../../@react-three/postprocessing/src/effects/SMAA.tsx", "../../@react-three/postprocessing/src/effects/FXAA.tsx", "../../@react-three/postprocessing/src/effects/Ramp.tsx", "../../@react-three/postprocessing/src/effects/Texture.tsx", "../../@react-three/postprocessing/src/effects/ToneMapping.tsx", "../../@react-three/postprocessing/src/effects/Vignette.tsx", "../../@react-three/postprocessing/src/effects/ShockWave.tsx", "../../@react-three/postprocessing/src/effects/LUT.tsx", "../../@react-three/postprocessing/src/effects/TiltShift.tsx", "../../@react-three/postprocessing/src/effects/TiltShift2.tsx", "../../@react-three/postprocessing/src/effects/ASCII.tsx", "../../@react-three/postprocessing/src/effects/Water.tsx", "../../@react-three/postprocessing/src/effects/N8AO.tsx"], "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nexport { _objectSpread2 as _, _defineProperty as a };\n", "function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON>olean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport { _setPrototypeOf as _, _isNativeReflectConstruct as a };\n", "import { Matrix3 } from 'three';\n\n/**\n *\n * @param terms\n *\n * | a b |\n * | c d |\n *\n * @returns {number} determinant\n */\n\nfunction determinant2() {\n  for (var _len = arguments.length, terms = new Array(_len), _key = 0; _key < _len; _key++) {\n    terms[_key] = arguments[_key];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3];\n  return a * d - b * c;\n}\n/**\n *\n * @param terms\n *\n * | a b c |\n * | d e f |\n * | g h i |\n *\n * @returns {number} determinant\n */\n\nfunction determinant3() {\n  for (var _len2 = arguments.length, terms = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    terms[_key2] = arguments[_key2];\n  }\n\n  var a = terms[0],\n      b = terms[1],\n      c = terms[2],\n      d = terms[3],\n      e = terms[4],\n      f = terms[5],\n      g = terms[6],\n      h = terms[7],\n      i = terms[8];\n  return a * e * i + b * f * g + c * d * h - c * e * g - b * d * i - a * f * h;\n}\n/**\n *\n * @param terms\n *\n * | a b c g |\n * | h i j k |\n * | l m n o |\n *\n * @returns {number} determinant\n */\n\nfunction determinant4() {\n  for (var _len3 = arguments.length, terms = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n    terms[_key3] = arguments[_key3];\n  }\n\n  terms[0];\n      terms[1];\n      terms[2];\n      terms[3];\n      terms[4];\n      terms[5];\n      terms[6];\n      terms[7];\n      terms[8];\n      terms[9];\n      terms[10];\n      terms[11];\n      terms[12];\n      terms[13];\n      terms[14]; // TODO\n}\n/**\n *\n * Get the determinant of matrix m without row r and col c\n *\n * @param {matrix} m Starter matrix\n * @param r row to remove\n * @param c col to remove\n *\n *     | a b c |\n * m = | d e f |\n *     | g h i |\n *\n * getMinor(m, 1, 1) would result in this determinant\n *\n * | a c |\n * | g i |\n *\n * @returns {number} determinant\n */\n\nfunction getMinor(matrix, r, c) {\n  var _matrixTranspose = matrix.clone().transpose();\n\n  var x = [];\n  var l = _matrixTranspose.elements.length;\n  var n = Math.sqrt(l);\n\n  for (var i = 0; i < l; i++) {\n    var element = _matrixTranspose.elements[i];\n    var row = Math.floor(i / n);\n    var col = i % n;\n\n    if (row !== r - 1 && col !== c - 1) {\n      x.push(element);\n    }\n  }\n\n  return determinant3.apply(void 0, x);\n}\n/**\n *\n */\n\nfunction matrixSum3(m1, m2) {\n  var sum = [];\n  var m1Array = m1.toArray();\n  var m2Array = m2.toArray();\n\n  for (var i = 0; i < m1Array.length; i++) {\n    sum[i] = m1Array[i] + m2Array[i];\n  }\n\n  return new Matrix3().fromArray(sum);\n}\n\nvar matrix = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  determinant2: determinant2,\n  determinant3: determinant3,\n  determinant4: determinant4,\n  getMinor: getMinor,\n  matrixSum3: matrixSum3\n});\n\nexport { matrixSum3 as a, determinant2 as b, determinant4 as c, determinant3 as d, getMinor as g, matrix as m };\n", "import { a as _isNativeReflectConstruct, _ as _setPrototypeOf } from './isNativeReflectConstruct-5594d075.esm.js';\nimport { Vector2, Matrix4 } from 'three';\nimport { d as determinant3, g as getMinor } from './matrix-baa530bf.esm.js';\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}\n\n/**\n *\n * @param point\n *\n * @param triangle\n *\n * @returns {boolean} true if the point is in the triangle\n *\n * TODO: Find explainer\n */\nfunction isPointInTriangle(point, triangle) {\n  var _triangle$ = _slicedToArray(triangle[0], 2),\n      ax = _triangle$[0],\n      ay = _triangle$[1];\n\n  var _triangle$2 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$2[0],\n      by = _triangle$2[1];\n\n  var _triangle$3 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$3[0],\n      cy = _triangle$3[1];\n\n  var _point = _slicedToArray(point, 2),\n      px = _point[0],\n      py = _point[1]; // TODO Sub with static calc\n\n\n  var matrix = new Matrix4(); // prettier-ignore\n\n  matrix.set(ax, ay, ax * ax + ay * ay, 1, bx, by, bx * bx + by * by, 1, cx, cy, cx * cx + cy * cy, 1, px, py, px * px + py * py, 1);\n  return matrix.determinant() <= 0;\n}\nfunction triangleDeterminant(triangle) {\n  var _triangle$4 = _slicedToArray(triangle[0], 2),\n      x1 = _triangle$4[0],\n      y1 = _triangle$4[1];\n\n  var _triangle$5 = _slicedToArray(triangle[1], 2),\n      x2 = _triangle$5[0],\n      y2 = _triangle$5[1];\n\n  var _triangle$6 = _slicedToArray(triangle[2], 2),\n      x3 = _triangle$6[0],\n      y3 = _triangle$6[1]; // prettier-ignore\n\n\n  return determinant3(x1, y1, 1, x2, y2, 1, x3, y3, 1);\n}\n/**\n * Uses triangle area determinant to check if 3 points are collinear.\n * If they are, they can't make a triangle, so the determinant will be 0!\n *\n *      0     1     2\n * ─────■─────■─────■\n *\n *\n * Fun fact, you can use this same determinant to check the order of the points in the triangle\n *\n * NOTE: Should this use a buffer instead? NOTE: Should this use a buffer instead? [x0, y0, x1, y1, x2, y2]?\n *\n */\n\nfunction arePointsCollinear(points) {\n  return triangleDeterminant(points) === 0;\n} // TODO This is the same principle as the prev function, find a way to make it have sense\n\nfunction isTriangleClockwise(triangle) {\n  return triangleDeterminant(triangle) < 0;\n}\n/**\n \nThe circumcircle is a circle touching all the vertices of a triangle or polygon.\n\n             ┌───┐             \n             │ B │             \n             └───┘             \n           .───●───.           \n        ,─'   ╱ ╲   '─.        \n      ,'     ╱   ╲     `.      \n     ╱      ╱     ╲      ╲     \n    ;      ╱       ╲      :    \n    │     ╱         ╲     │    \n    │    ╱           ╲    │    \n    :   ╱             ╲   ;    \n     ╲ ╱               ╲ ╱     \n┌───┐ ●─────────────────● ┌───┐\n│ A │  `.             ,'  │ C │\n└───┘    '─.       ,─'    └───┘\n            `─────'                         \n */\n\n/**\n *\n * @param triangle\n *\n * @returns {number} circumcircle\n */\n// https://math.stackexchange.com/a/1460096\n\nfunction getCircumcircle(triangle) {\n  // TS-TODO the next few lines are ignored because the types aren't current to the change in vectors (that can now be iterated)\n  // @ts-ignore\n  var _triangle$7 = _slicedToArray(triangle[0], 2),\n      ax = _triangle$7[0],\n      ay = _triangle$7[1]; // @ts-ignore\n\n\n  var _triangle$8 = _slicedToArray(triangle[1], 2),\n      bx = _triangle$8[0],\n      by = _triangle$8[1]; // @ts-ignore\n\n\n  var _triangle$9 = _slicedToArray(triangle[2], 2),\n      cx = _triangle$9[0],\n      cy = _triangle$9[1];\n\n  if (arePointsCollinear(triangle)) return null; // points are collinear\n\n  var m = new Matrix4(); // prettier-ignore\n\n  m.set(1, 1, 1, 1, ax * ax + ay * ay, ax, ay, 1, bx * bx + by * by, bx, by, 1, cx * cx + cy * cy, cx, cy, 1);\n  var m11 = getMinor(m, 1, 1);\n  var m13 = getMinor(m, 1, 3);\n  var m12 = getMinor(m, 1, 2);\n  var m14 = getMinor(m, 1, 4);\n  var x0 = 0.5 * (m12 / m11);\n  var y0 = 0.5 * (m13 / m11);\n  var r2 = x0 * x0 + y0 * y0 + m14 / m11;\n  return {\n    x: Math.abs(x0) === 0 ? 0 : x0,\n    y: Math.abs(y0) === 0 ? 0 : -y0,\n    r: Math.sqrt(r2)\n  };\n} // https://stackoverflow.com/questions/39984709/how-can-i-check-wether-a-point-is-inside-the-circumcircle-of-3-points\n\nfunction isPointInCircumcircle(point, triangle) {\n  var _ref = Array.isArray(triangle[0]) ? triangle[0] : triangle[0].toArray(),\n      _ref2 = _slicedToArray(_ref, 2),\n      ax = _ref2[0],\n      ay = _ref2[1];\n\n  var _ref3 = Array.isArray(triangle[1]) ? triangle[1] : triangle[1].toArray(),\n      _ref4 = _slicedToArray(_ref3, 2),\n      bx = _ref4[0],\n      by = _ref4[1];\n\n  var _ref5 = Array.isArray(triangle[2]) ? triangle[2] : triangle[2].toArray(),\n      _ref6 = _slicedToArray(_ref5, 2),\n      cx = _ref6[0],\n      cy = _ref6[1];\n\n  var _point2 = _slicedToArray(point, 2),\n      px = _point2[0],\n      py = _point2[1];\n\n  if (arePointsCollinear(triangle)) throw new Error(\"Collinear points don't form a triangle\");\n  /**\n          | ax-px, ay-py, (ax-px)² + (ay-py)² |\n    det = | bx-px, by-py, (bx-px)² + (by-py)² |\n          | cx-px, cy-py, (cx-px)² + (cy-py)² |\n  */\n\n  var x1mpx = ax - px;\n  var aympy = ay - py;\n  var bxmpx = bx - px;\n  var bympy = by - py;\n  var cxmpx = cx - px;\n  var cympy = cy - py; // prettier-ignore\n\n  var d = determinant3(x1mpx, aympy, x1mpx * x1mpx + aympy * aympy, bxmpx, bympy, bxmpx * bxmpx + bympy * bympy, cxmpx, cympy, cxmpx * cxmpx + cympy * cympy); // if d is 0, the point is on C\n\n  if (d === 0) {\n    return true;\n  }\n\n  return !isTriangleClockwise(triangle) ? d > 0 : d < 0;\n} // From https://algorithmtutor.com/Computational-Geometry/Determining-if-two-consecutive-segments-turn-left-or-right/\n\nvar mv1 = new Vector2();\nvar mv2 = new Vector2();\n/**\n \n     ╱      ╲     \n    ╱        ╲    \n   ▕          ▏   \n                  \n right      left  \n\n * NOTE: Should this use a buffer instead? [x0, y0, x1, y1]?\n */\n\nfunction doThreePointsMakeARight(points) {\n  var _points$map = points.map(function (p) {\n    if (Array.isArray(p)) {\n      return _construct(Vector2, _toConsumableArray(p));\n    }\n\n    return p;\n  }),\n      _points$map2 = _slicedToArray(_points$map, 3),\n      p1 = _points$map2[0],\n      p2 = _points$map2[1],\n      p3 = _points$map2[2];\n\n  if (arePointsCollinear(points)) return false; // @ts-ignore\n\n  var p2p1 = mv1.subVectors(p2, p1); // @ts-ignore\n\n  var p3p1 = mv2.subVectors(p3, p1);\n  var cross = p3p1.cross(p2p1);\n  return cross > 0;\n}\n\nvar triangle = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  isPointInTriangle: isPointInTriangle,\n  triangleDeterminant: triangleDeterminant,\n  arePointsCollinear: arePointsCollinear,\n  isTriangleClockwise: isTriangleClockwise,\n  getCircumcircle: getCircumcircle,\n  isPointInCircumcircle: isPointInCircumcircle,\n  doThreePointsMakeARight: doThreePointsMakeARight\n});\n\nexport { _slicedToArray as _, _toConsumableArray as a, triangleDeterminant as b, arePointsCollinear as c, doThreePointsMakeARight as d, isTriangleClockwise as e, isPointInCircumcircle as f, getCircumcircle as g, isPointInTriangle as i, triangle as t };\n", "import { d as doThreePointsMakeARight, a as _toConsumableArray, _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Vector3, Matrix3 } from 'three';\nimport { a as matrixSum3 } from './matrix-baa530bf.esm.js';\n\n/**\n * Clamps a value between a range.\n */\nfunction clamp(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n} // Loops the value t, so that it is never larger than length and never smaller than 0.\n\nfunction repeat(t, length) {\n  return clamp(t - Math.floor(t / length) * length, 0, length);\n} // Calculates the shortest difference between two given angles.\n\n\nfunction deltaAngle(current, target) {\n  var delta = repeat(target - current, Math.PI * 2);\n  if (delta > Math.PI) delta -= Math.PI * 2;\n  return delta;\n}\n/**\n * Converts degrees to radians.\n */\n\nfunction degToRad(degrees) {\n  return degrees / 180 * Math.PI;\n}\n/**\n * Converts radians to degrees.\n */\n\nfunction radToDeg(radians) {\n  return radians * 180 / Math.PI;\n} // adapted from https://gist.github.com/stephanbogner/a5f50548a06bec723dcb0991dcbb0856 by https://twitter.com/st_phan\n\nfunction fibonacciOnSphere(buffer, _ref) {\n  var _ref$radius = _ref.radius,\n      radius = _ref$radius === void 0 ? 1 : _ref$radius;\n  var samples = buffer.length / 3;\n  var offset = 2 / samples;\n  var increment = Math.PI * (3 - 2.2360679775);\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var y = i * offset - 1 + offset / 2;\n    var distance = Math.sqrt(1 - Math.pow(y, 2));\n    var phi = i % samples * increment;\n    var x = Math.cos(phi) * distance;\n    var z = Math.sin(phi) * distance;\n    buffer[i] = x * radius;\n    buffer[i + 1] = y * radius;\n    buffer[i + 2] = z * radius;\n  }\n} // @ts-ignore\n\nfunction vectorEquals(a, b) {\n  var eps = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Number.EPSILON;\n  return Math.abs(a.x - b.x) < eps && Math.abs(a.y - b.y) < eps && Math.abs(a.z - b.z) < eps;\n}\n/**\n * Sorts vectors in lexicographic order, works with both v2 and v3\n *\n *  Use as:\n *  const sorted = arrayOfVectors.sort(lexicographicOrder)\n */\n// https://en.wikipedia.org/wiki/Lexicographic_order\n\nfunction lexicographic(a, b) {\n  if (a.x === b.x) {\n    // do a check to see if points is 3D,\n    // in which case add y eq check and sort by z\n    if (typeof a.z !== \"undefined\") {\n      if (a.y === b.y) {\n        return a.z - b.z;\n      }\n    }\n\n    return a.y - b.y;\n  }\n\n  return a.x - b.x;\n}\n/**\n * Convex Hull\n *\n * Returns an array of 2D Vectors representing the convex hull of a set of 2D Vectors\n */\n\n/**\n * Calculate the convex hull of a set of points\n */\n\nfunction convexHull(_points) {\n  var points = _points.sort(lexicographic); // put p1 and p2 in a list lUpper with p1 as the first point\n\n\n  var lUpper = [points[0], points[1]]; // for i <- 3 to n\n\n  for (var i = 2; i < points.length; i++) {\n    lUpper.push(points[i]); // while lUpper contains more than 2 points and the last three points in lUpper do not make a right turn\n\n    while (lUpper.length > 2 && doThreePointsMakeARight(_toConsumableArray(lUpper.slice(-3)))) {\n      // delete the middle of the last three points from lUpper\n      lUpper.splice(lUpper.length - 2, 1);\n    }\n  } // put pn and pn-1 in a list lLower with pn as the first point\n\n\n  var lLower = [points[points.length - 1], points[points.length - 2]]; // for (i <- n - 2 downto 1)\n\n  for (var _i = points.length - 3; _i >= 0; _i--) {\n    // append pi to lLower\n    lLower.push(points[_i]); // while lLower contains more than 2 points and the last three points in lLower do not make a right turn\n\n    while (lLower.length > 2 && doThreePointsMakeARight(_toConsumableArray(lLower.slice(-3)))) {\n      // delete the middle of the last three points from lLower\n      lLower.splice(lLower.length - 2, 1);\n    }\n  } // remove the first and last point from lLower to avoid duplication of the points where the upper and lower hull meet\n\n\n  lLower.splice(0, 1);\n  lLower.splice(lLower.length - 1, 1); // prettier-ignore\n\n  var c = [].concat(lUpper, lLower);\n  return c;\n}\nfunction remap(x, _ref2, _ref3) {\n  var _ref4 = _slicedToArray(_ref2, 2),\n      low1 = _ref4[0],\n      high1 = _ref4[1];\n\n  var _ref5 = _slicedToArray(_ref3, 2),\n      low2 = _ref5[0],\n      high2 = _ref5[1];\n\n  return low2 + (x - low1) * (high2 - low2) / (high1 - low1);\n}\n/**\n *\n * https://www.desmos.com/calculator/vsnmlaljdu\n *\n * Ease-in-out, goes to -Infinite before 0 and Infinite after 1\n *\n * @param t\n * @returns\n */\n\nfunction fade(t) {\n  return t * t * t * (t * (t * 6 - 15) + 10);\n}\n/**\n *\n * Returns the result of linearly interpolating between input A and input B by input T.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction lerp(v0, v1, t) {\n  return v0 * (1 - t) + v1 * t;\n}\n/**\n *\n * Returns the linear parameter that produces the interpolant specified by input T within the range of input A to input B.\n *\n * @param v0\n * @param v1\n * @param t\n * @returns\n */\n\nfunction inverseLerp(v0, v1, t) {\n  return (t - v0) / (v1 - v0);\n}\n/**\n *\n */\n\nfunction normalize(x, y, z) {\n  var m = Math.sqrt(x * x + y * y + z * z);\n  return [x / m, y / m, z / m];\n}\n/**\n *\n */\n\nfunction pointOnCubeToPointOnSphere(x, y, z) {\n  var x2 = x * x;\n  var y2 = y * y;\n  var z2 = z * z;\n  var nx = x * Math.sqrt(1 - (y2 + z2) / 2 + y2 * z2 / 3);\n  var ny = y * Math.sqrt(1 - (z2 + x2) / 2 + z2 * x2 / 3);\n  var nz = z * Math.sqrt(1 - (x2 + y2) / 2 + x2 * y2 / 3);\n  return [nx, ny, nz];\n} // https://math.stackexchange.com/questions/180418/calculate-rotation-matrix-to-align-vector-a-to-vector-b-in-3d\n\n/**\n * Give two unit vectors a and b, returns the transformation matrix that rotates a onto b.\n *\n * */\n\nfunction rotateVectorOnVector(a, b) {\n  var v = new Vector3().crossVectors(a, b);\n  var c = a.dot(b);\n  var i = new Matrix3().identity(); //  skew-symmetric cross-product matrix of 𝑣 https://en.wikipedia.org/wiki/Skew-symmetric_matrix\n  // prettier-ignore\n\n  var vx = new Matrix3().set(0, -v.z, v.y, v.z, 0, -v.x, -v.y, v.x, 0);\n  var vxsquared = new Matrix3().multiplyMatrices(vx, vx).multiplyScalar(1 / (1 + c));\n\n  var _final = matrixSum3(matrixSum3(i, vx), vxsquared);\n\n  return _final;\n} // calculate latitude and longitude (in radians) from point on unit sphere\n\nfunction pointToCoordinate(x, y, z) {\n  var lat = Math.asin(y);\n  var lon = Math.atan2(x, -z);\n  return [lat, lon];\n} // calculate point on unit sphere given latitude and logitude in radians\n\nfunction coordinateToPoint(lat, lon) {\n  var y = Math.sin(lat);\n  var r = Math.cos(lat);\n  var x = Math.sin(lon) * r;\n  var z = -Math.cos(lon) * r;\n  return [x, y, z];\n}\n/**\n * Given a plane and a segment, return the intersection point if it exists or null it doesn't.\n */\n\nfunction planeSegmentIntersection(plane, segment) {\n  var _segment = _slicedToArray(segment, 2),\n      a = _segment[0],\n      b = _segment[1];\n\n  var matrix = rotateVectorOnVector(plane.normal, new Vector3(0, 1, 0));\n  var t = inverseLerp(a.clone().applyMatrix3(matrix).y, b.clone().applyMatrix3(matrix).y, 0);\n  return new Vector3().lerpVectors(a, b, t);\n}\n/**\n * Given a plane and a point, return the distance.\n */\n\nfunction pointToPlaneDistance(p, plane) {\n  var d = plane.normal.dot(p); // TODO\n\n  return d;\n}\nfunction getIndexFrom3D(coords, sides) {\n  var _coords = _slicedToArray(coords, 3),\n      ix = _coords[0],\n      iy = _coords[1],\n      iz = _coords[2];\n\n  var _sides = _slicedToArray(sides, 2),\n      rx = _sides[0],\n      ry = _sides[1];\n\n  return iz * rx * ry + iy * rx + ix;\n}\nfunction get3DFromIndex(index, size) {\n  var _size = _slicedToArray(size, 2),\n      rx = _size[0],\n      ry = _size[1];\n\n  var a = rx * ry;\n  var z = index / a;\n  var b = index - a * z;\n  var y = b / rx;\n  var x = b % rx;\n  return [x, y, z];\n}\nfunction getIndexFrom2D(coords, size) {\n  return coords[0] + size[0] * coords[1];\n}\nfunction get2DFromIndex(index, columns) {\n  var x = index % columns;\n  var y = Math.floor(index / columns);\n  return [x, y];\n}\n\nvar misc = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  clamp: clamp,\n  deltaAngle: deltaAngle,\n  degToRad: degToRad,\n  radToDeg: radToDeg,\n  fibonacciOnSphere: fibonacciOnSphere,\n  vectorEquals: vectorEquals,\n  lexicographic: lexicographic,\n  convexHull: convexHull,\n  remap: remap,\n  fade: fade,\n  lerp: lerp,\n  inverseLerp: inverseLerp,\n  normalize: normalize,\n  pointOnCubeToPointOnSphere: pointOnCubeToPointOnSphere,\n  rotateVectorOnVector: rotateVectorOnVector,\n  pointToCoordinate: pointToCoordinate,\n  coordinateToPoint: coordinateToPoint,\n  planeSegmentIntersection: planeSegmentIntersection,\n  pointToPlaneDistance: pointToPlaneDistance,\n  getIndexFrom3D: getIndexFrom3D,\n  get3DFromIndex: get3DFromIndex,\n  getIndexFrom2D: getIndexFrom2D,\n  get2DFromIndex: get2DFromIndex\n});\n\nexport { degToRad as a, fibonacciOnSphere as b, clamp as c, deltaAngle as d, lexicographic as e, fade as f, convexHull as g, remap as h, inverseLerp as i, rotateVectorOnVector as j, pointToCoordinate as k, lerp as l, misc as m, normalize as n, coordinateToPoint as o, pointOnCubeToPointOnSphere as p, planeSegmentIntersection as q, radToDeg as r, pointToPlaneDistance as s, getIndexFrom3D as t, get3DFromIndex as u, vectorEquals as v, getIndexFrom2D as w, get2DFromIndex as x };\n", "/**\n *\n */\nfunction zero() {\n  return [0, 0];\n}\nfunction one() {\n  return [1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]));\n}\n\nvar vector2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, length as f, distance as g, lengthSqr as l, one as o, sub as s, vector2 as v, zero as z };\n", "/**\n *\n */\nfunction zero() {\n  return [0, 0, 0];\n}\nfunction one() {\n  return [1, 1, 1];\n}\nfunction add(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\nfunction addValue(a, n) {\n  return [a[0] + n, a[1] + n, a[2] + n];\n}\nfunction sub(a, b) {\n  return [a[0] - b[0], a[1] - b[1], a[2] - b[2]];\n}\nfunction subValue(a, n) {\n  return [a[0] - n, a[1] - n, a[2] - n];\n}\nfunction scale(a, n) {\n  return [a[0] * n, a[1] * n, a[2] * n];\n}\nfunction dot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nfunction cross(a, b) {\n  var x = a[1] * b[2] - a[2] * b[1];\n  var y = a[2] * b[0] - a[0] * b[2];\n  var z = a[0] * b[1] - a[1] * b[0];\n  return [x, y, z];\n}\n/**\n * Calculate the squared length of a vector.\n * Use this when comparing two vectors instead of length, as it's more efficient (no sqrt)\n */\n\nfunction lengthSqr(a) {\n  return a[0] * a[0] + a[1] * a[1] + a[2] * a[2];\n}\n/**\n * Calculate the length of a vector.\n * If you only need to compare lenghts, consider using the more efficient lengthSqr\n */\n\nfunction length(a) {\n  return Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2]);\n}\nfunction distance(a, b) {\n  return Math.sqrt((a[0] - b[0]) * (a[0] - b[0]) + (a[1] - b[1]) * (a[1] - b[1]) + (a[2] - b[2]) * (a[2] - b[2]));\n}\n\nvar vector3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  zero: zero,\n  one: one,\n  add: add,\n  addValue: addValue,\n  sub: sub,\n  subValue: subValue,\n  scale: scale,\n  dot: dot,\n  cross: cross,\n  lengthSqr: lengthSqr,\n  length: length,\n  distance: distance\n});\n\nexport { add as a, addValue as b, subValue as c, scale as d, dot as e, cross as f, length as g, distance as h, lengthSqr as l, one as o, sub as s, vector3 as v, zero as z };\n", "import { _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _slicedToArray } from './triangle-b62b9067.esm.js';\nimport { Quaternion, Vector3 } from 'three';\nimport { l as lerp$1 } from './misc-7d870b3c.esm.js';\nimport { z as zero, a as add$1 } from './vector2-d2bf51f1.esm.js';\nimport { a as add } from './vector3-0a088b7f.esm.js';\n\nfunction swizzle(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var swizzle = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : \"xyz\";\n  var o = {\n    x: 0,\n    y: 0,\n    z: 0\n  };\n\n  for (var _i = 0; _i < buffer.length; _i += stride) {\n    o.x = buffer[_i];\n    o.y = buffer[_i + 1];\n    o.z = buffer[_i + 2];\n\n    var _swizzle$split = swizzle.split(\"\"),\n        _swizzle$split2 = _slicedToArray(_swizzle$split, 3),\n        x = _swizzle$split2[0],\n        y = _swizzle$split2[1],\n        z = _swizzle$split2[2]; // TODO Fix this ugly type\n\n\n    buffer[_i] = o[x];\n    buffer[_i + 1] = o[y];\n\n    if (stride === 3) {\n      buffer[_i + 2] = o[z];\n    }\n  }\n\n  return buffer;\n}\n/**\n * @param buffer A stride 2 points buffer\n * @param valueGenerator A function that returns the value of the z axis at index i\n * @returns\n */\n\nfunction addAxis(buffer, size) {\n  var valueGenerator = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {\n    return Math.random();\n  };\n  var newSize = size + 1;\n  var newBuffer = new Float32Array(buffer.length / size * newSize);\n\n  for (var _i2 = 0; _i2 < buffer.length; _i2 += size) {\n    var _j = _i2 / size * newSize;\n\n    newBuffer[_j] = buffer[_i2];\n    newBuffer[_j + 1] = buffer[_i2 + 1];\n\n    if (size === 2) {\n      newBuffer[_j + 2] = valueGenerator(_j);\n    }\n\n    if (size === 3) {\n      newBuffer[_j + 2] = buffer[_i2 + 2];\n      newBuffer[_j + 3] = valueGenerator(_j);\n    }\n  }\n\n  return newBuffer;\n}\n/**\n * Lerps bufferA and bufferB into final\n *\n * @param bufferA\n * @param bufferB\n * @param final\n * @param t\n */\n\nfunction lerp(bufferA, bufferB, _final, t) {\n  for (var _i3 = 0; _i3 < bufferA.length; _i3++) {\n    _final[_i3] = lerp$1(bufferA[_i3], bufferB[_i3], t);\n  }\n} // TODO add stride\n// TODO Fix types & vectors\n\n/**\n *\n * Translate all points in the passed buffer by the passed translactionVector.\n *\n * @param buffer\n * @param translationVector\n * @returns\n */\n\nfunction translate(buffer, translationVector) {\n  var stride = translationVector.length;\n\n  for (var _i4 = 0; _i4 < buffer.length; _i4 += stride) {\n    buffer[_i4] += translationVector[0];\n    buffer[_i4 + 1] += translationVector[1];\n    buffer[_i4 + 2] += translationVector[2];\n  }\n\n  return buffer;\n} // TODO add stride\n// TODO remove quaternion & vector3 dependencies\n\nfunction rotate(buffer, rotation) {\n  var defaultRotation = {\n    center: [0, 0, 0],\n    q: new Quaternion().identity()\n  };\n  var v = new Vector3();\n\n  var _defaultRotation$rota = _objectSpread2(_objectSpread2({}, defaultRotation), rotation),\n      q = _defaultRotation$rota.q,\n      center = _defaultRotation$rota.center;\n\n  for (var _i5 = 0; _i5 < buffer.length; _i5 += 3) {\n    v.set(buffer[_i5] - center[0], buffer[_i5 + 1] - center[1], buffer[_i5 + 2] - center[2]);\n    v.applyQuaternion(q);\n    buffer[_i5] = v.x + center[0];\n    buffer[_i5 + 1] = v.y + center[1];\n    buffer[_i5 + 2] = v.z + center[1];\n  }\n\n  return buffer;\n}\nfunction map(buffer, stride, callback) {\n  for (var _i6 = 0, _j2 = 0; _i6 < buffer.length; _i6 += stride, _j2++) {\n    if (stride === 3) {\n      var res = callback([buffer[_i6], buffer[_i6 + 1], buffer[_i6 + 2]], _j2);\n      buffer.set(res, _i6);\n    } else {\n      buffer.set(callback([buffer[_i6], buffer[_i6 + 1]], _j2), _i6);\n    }\n  }\n\n  return buffer;\n}\n/**\n * Reduces passed buffer\n */\n\nfunction reduce(b, stride, callback, acc) {\n  for (var _i7 = 0, _j3 = 0; _i7 < b.length; _i7 += stride, _j3++) {\n    if (stride === 2) {\n      acc = callback(acc, [b[_i7], b[_i7 + 1]], _j3);\n    } else {\n      acc = callback(acc, [b[_i7], b[_i7 + 1], b[_i7 + 2]], _j3);\n    }\n  }\n\n  return acc;\n}\nfunction expand(b, stride, opts) {\n  var defaultExpandOptions = {\n    center: [0, 0, 0]\n  };\n\n  var _defaultExpandOptions = _objectSpread2(_objectSpread2({}, defaultExpandOptions), opts),\n      center = _defaultExpandOptions.center,\n      distance = _defaultExpandOptions.distance;\n\n  for (var _i8 = 0; _i8 < b.length; _i8 += stride) {\n    /**\n     * 1. translate to origin (subtract the scaling center)\n     * 2. scale by the correct amount (multiply by a constant)\n     * 2. translate from origin (add the scaling center)\n     */\n    b[_i8] = (b[_i8] - center[0]) * (1 + distance) + center[0];\n    b[_i8 + 1] = (b[_i8 + 1] - center[1]) * (1 + distance) + center[1];\n\n    if (stride === 3) {\n      b[_i8 + 2] = (b[_i8 + 2] - center[1]) * (1 + distance) + center[2];\n    }\n  }\n\n  return b;\n}\nfunction center(myBuffer, stride) {\n  return reduce(myBuffer, stride, function (acc, point) {\n    if (stride === 3) {\n      // some type hacking is necessary to avoid type errors going from [n, n] => [n, n, n]\n      // but it's not an actual problem, as this path would always get a v3\n      acc = add(acc, point);\n    } else {\n      acc = add$1(acc, point);\n    }\n\n    return acc;\n  }, zero());\n}\nfunction sort(myBuffer, stride, callback) {\n  // 1. make an array of the correct size\n  var indices = Int16Array.from({\n    length: myBuffer.length / stride\n  }, function (_, i) {\n    return i;\n  }); // 2. sort the indices array\n\n  indices.sort(function (a, b) {\n    var pa = myBuffer.slice(a * stride, a * stride + stride);\n    var pb = myBuffer.slice(b * stride, b * stride + stride);\n    return callback(pa, pb);\n  }); // 3. make a copy of the original array to fetch indices from\n\n  var prevBuffer = myBuffer.slice(0); // 4. mutate the passed array\n\n  for (var _i9 = 0; _i9 < indices.length; _i9++) {\n    var _j4 = indices[_i9];\n    myBuffer.set(prevBuffer.slice(_j4 * stride, _j4 * stride + stride), _i9 * 3);\n  }\n\n  return myBuffer;\n}\n\nvar buffer = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  swizzle: swizzle,\n  addAxis: addAxis,\n  lerp: lerp,\n  translate: translate,\n  rotate: rotate,\n  map: map,\n  reduce: reduce,\n  expand: expand,\n  center: center,\n  sort: sort\n});\n\nexport { addAxis as a, buffer as b, reduce as c, center as d, expand as e, sort as f, lerp as l, map as m, rotate as r, swizzle as s, translate as t };\n", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nexport { _classCallCheck as _ };\n", "import { a as _defineProperty, _ as _objectSpread2 } from './objectSpread2-284232a6.esm.js';\nimport { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { l as lerp, f as fade } from './misc-7d870b3c.esm.js';\n\n/*\n * A speed-improved perlin and simplex noise algorithms for 2D.\n *\n * Based on example code by <PERSON> (<EMAIL>).\n * Optimisations by <PERSON> (<EMAIL>).\n * Better rank ordering method by <PERSON> in 2012.\n * Converted to Javascript by <PERSON>.\n *\n * Version 2012-03-09\n *\n * This code was placed in the public domain by its original author,\n * <PERSON>. You may use it as you see fit, but\n * attribution is appreciated.\n *\n */\n\nvar Grad = function Grad(x, y, z) {\n  var _this = this;\n\n  _classCallCheck(this, Grad);\n\n  _defineProperty(this, \"dot2\", function (x, y) {\n    return _this.x * x + _this.y * y;\n  });\n\n  _defineProperty(this, \"dot3\", function (x, y, z) {\n    return _this.x * x + _this.y * y + _this.z * z;\n  });\n\n  this.x = x;\n  this.y = y;\n  this.z = z;\n};\n\nvar grad3 = [new Grad(1, 1, 0), new Grad(-1, 1, 0), new Grad(1, -1, 0), new Grad(-1, -1, 0), new Grad(1, 0, 1), new Grad(-1, 0, 1), new Grad(1, 0, -1), new Grad(-1, 0, -1), new Grad(0, 1, 1), new Grad(0, -1, 1), new Grad(0, 1, -1), new Grad(0, -1, -1)];\nvar p = [151, 160, 137, 91, 90, 15, 131, 13, 201, 95, 96, 53, 194, 233, 7, 225, 140, 36, 103, 30, 69, 142, 8, 99, 37, 240, 21, 10, 23, 190, 6, 148, 247, 120, 234, 75, 0, 26, 197, 62, 94, 252, 219, 203, 117, 35, 11, 32, 57, 177, 33, 88, 237, 149, 56, 87, 174, 20, 125, 136, 171, 168, 68, 175, 74, 165, 71, 134, 139, 48, 27, 166, 77, 146, 158, 231, 83, 111, 229, 122, 60, 211, 133, 230, 220, 105, 92, 41, 55, 46, 245, 40, 244, 102, 143, 54, 65, 25, 63, 161, 1, 216, 80, 73, 209, 76, 132, 187, 208, 89, 18, 169, 200, 196, 135, 130, 116, 188, 159, 86, 164, 100, 109, 198, 173, 186, 3, 64, 52, 217, 226, 250, 124, 123, 5, 202, 38, 147, 118, 126, 255, 82, 85, 212, 207, 206, 59, 227, 47, 16, 58, 17, 182, 189, 28, 42, 223, 183, 170, 213, 119, 248, 152, 2, 44, 154, 163, 70, 221, 153, 101, 155, 167, 43, 172, 9, 129, 22, 39, 253, 19, 98, 108, 110, 79, 113, 224, 232, 178, 185, 112, 104, 218, 246, 97, 228, 251, 34, 242, 193, 238, 210, 144, 12, 191, 179, 162, 241, 81, 51, 145, 235, 249, 14, 239, 107, 49, 192, 214, 31, 181, 199, 106, 157, 184, 84, 204, 176, 115, 121, 50, 45, 127, 4, 150, 254, 138, 236, 205, 93, 222, 114, 67, 29, 24, 72, 243, 141, 128, 195, 78, 66, 215, 61, 156, 180]; // To remove the need for index wrapping, double the permutation table length\n\nvar perm = new Array(512);\nvar gradP = new Array(512); // This isn't a very good seeding function, but it works ok. It supports 2^16\n// different seed values. Write something better if you need more seeds.\n\nvar seed = function seed(_seed) {\n  if (_seed > 0 && _seed < 1) {\n    // Scale the seed out\n    _seed *= 65536;\n  }\n\n  _seed = Math.floor(_seed);\n\n  if (_seed < 256) {\n    _seed |= _seed << 8;\n  }\n\n  for (var i = 0; i < 256; i++) {\n    var v;\n\n    if (i & 1) {\n      v = p[i] ^ _seed & 255;\n    } else {\n      v = p[i] ^ _seed >> 8 & 255;\n    }\n\n    perm[i] = perm[i + 256] = v;\n    gradP[i] = gradP[i + 256] = grad3[v % 12];\n  }\n};\nseed(0);\n/*\n  for(var i=0; i<256; i++) {\n    perm[i] = perm[i + 256] = p[i];\n    gradP[i] = gradP[i + 256] = grad3[perm[i] % 12];\n  }*/\n// Skewing and unskewing factors for 2, 3, and 4 dimensions\n\nvar F2 = 0.5 * (Math.sqrt(3) - 1);\nvar G2 = (3 - Math.sqrt(3)) / 6;\nvar F3 = 1 / 3;\nvar G3 = 1 / 6; // 2D simplex noise\n\nvar simplex2 = function simplex2(xin, yin) {\n  var n0, n1, n2; // Noise contributions from the three corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin) * F2; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var t = (i + j) * G2;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t; // For the 2D case, the simplex shape is an equilateral triangle.\n  // Determine which simplex we are in.\n\n  var i1, j1; // Offsets for second (middle) corner of simplex in (i,j) coords\n\n  if (x0 > y0) {\n    // lower triangle, XY order: (0,0)->(1,0)->(1,1)\n    i1 = 1;\n    j1 = 0;\n  } else {\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    i1 = 0;\n    j1 = 1;\n  } // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n  // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n  // c = (3-sqrt(3))/6\n\n\n  var x1 = x0 - i1 + G2; // Offsets for middle corner in (x,y) unskewed coords\n\n  var y1 = y0 - j1 + G2;\n  var x2 = x0 - 1 + 2 * G2; // Offsets for last corner in (x,y) unskewed coords\n\n  var y2 = y0 - 1 + 2 * G2; // Work out the hashed gradient indices of the three simplex corners\n\n  i &= 255;\n  j &= 255;\n  var gi0 = gradP[i + perm[j]];\n  var gi1 = gradP[i + i1 + perm[j + j1]];\n  var gi2 = gradP[i + 1 + perm[j + 1]]; // Calculate the contribution from the three corners\n\n  var t0 = 0.5 - x0 * x0 - y0 * y0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot2(x0, y0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.5 - x1 * x1 - y1 * y1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot2(x1, y1);\n  }\n\n  var t2 = 0.5 - x2 * x2 - y2 * y2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot2(x2, y2);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 70 * (n0 + n1 + n2);\n}; // 3D simplex noise\n\nvar simplex3 = function simplex3(xin, yin, zin) {\n  var n0, n1, n2, n3; // Noise contributions from the four corners\n  // Skew the input space to determine which simplex cell we're in\n\n  var s = (xin + yin + zin) * F3; // Hairy factor for 2D\n\n  var i = Math.floor(xin + s);\n  var j = Math.floor(yin + s);\n  var k = Math.floor(zin + s);\n  var t = (i + j + k) * G3;\n  var x0 = xin - i + t; // The x,y distances from the cell origin, unskewed.\n\n  var y0 = yin - j + t;\n  var z0 = zin - k + t; // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n  // Determine which simplex we are in.\n\n  var i1, j1, k1; // Offsets for second corner of simplex in (i,j,k) coords\n\n  var i2, j2, k2; // Offsets for third corner of simplex in (i,j,k) coords\n\n  if (x0 >= y0) {\n    if (y0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    } else if (x0 >= z0) {\n      i1 = 1;\n      j1 = 0;\n      k1 = 0;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 1;\n      j2 = 0;\n      k2 = 1;\n    }\n  } else {\n    if (y0 < z0) {\n      i1 = 0;\n      j1 = 0;\n      k1 = 1;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else if (x0 < z0) {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 0;\n      j2 = 1;\n      k2 = 1;\n    } else {\n      i1 = 0;\n      j1 = 1;\n      k1 = 0;\n      i2 = 1;\n      j2 = 1;\n      k2 = 0;\n    }\n  } // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n  // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n  // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n  // c = 1/6.\n\n\n  var x1 = x0 - i1 + G3; // Offsets for second corner\n\n  var y1 = y0 - j1 + G3;\n  var z1 = z0 - k1 + G3;\n  var x2 = x0 - i2 + 2 * G3; // Offsets for third corner\n\n  var y2 = y0 - j2 + 2 * G3;\n  var z2 = z0 - k2 + 2 * G3;\n  var x3 = x0 - 1 + 3 * G3; // Offsets for fourth corner\n\n  var y3 = y0 - 1 + 3 * G3;\n  var z3 = z0 - 1 + 3 * G3; // Work out the hashed gradient indices of the four simplex corners\n\n  i &= 255;\n  j &= 255;\n  k &= 255;\n  var gi0 = gradP[i + perm[j + perm[k]]];\n  var gi1 = gradP[i + i1 + perm[j + j1 + perm[k + k1]]];\n  var gi2 = gradP[i + i2 + perm[j + j2 + perm[k + k2]]];\n  var gi3 = gradP[i + 1 + perm[j + 1 + perm[k + 1]]]; // Calculate the contribution from the four corners\n\n  var t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0;\n\n  if (t0 < 0) {\n    n0 = 0;\n  } else {\n    t0 *= t0;\n    n0 = t0 * t0 * gi0.dot3(x0, y0, z0); // (x,y) of grad3 used for 2D gradient\n  }\n\n  var t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1;\n\n  if (t1 < 0) {\n    n1 = 0;\n  } else {\n    t1 *= t1;\n    n1 = t1 * t1 * gi1.dot3(x1, y1, z1);\n  }\n\n  var t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2;\n\n  if (t2 < 0) {\n    n2 = 0;\n  } else {\n    t2 *= t2;\n    n2 = t2 * t2 * gi2.dot3(x2, y2, z2);\n  }\n\n  var t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3;\n\n  if (t3 < 0) {\n    n3 = 0;\n  } else {\n    t3 *= t3;\n    n3 = t3 * t3 * gi3.dot3(x3, y3, z3);\n  } // Add contributions from each corner to get the final noise value.\n  // The result is scaled to return values in the interval [-1,1].\n\n\n  return 32 * (n0 + n1 + n2 + n3);\n}; // ##### Perlin noise stuff\n// 2D Perlin Noise\n\nvar perlin2 = function perlin2(x, y) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y); // Get relative xy coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255; // Calculate noise contributions from each of the four corners\n\n  var n00 = gradP[X + perm[Y]].dot2(x, y);\n  var n01 = gradP[X + perm[Y + 1]].dot2(x, y - 1);\n  var n10 = gradP[X + 1 + perm[Y]].dot2(x - 1, y);\n  var n11 = gradP[X + 1 + perm[Y + 1]].dot2(x - 1, y - 1); // Compute the fade curve value for x\n\n  var u = fade(x); // Interpolate the four results\n\n  return lerp(lerp(n00, n10, u), lerp(n01, n11, u), fade(y));\n}; // 3D Perlin Noise\n\nvar perlin3 = function perlin3(x, y, z) {\n  // Find unit grid cell containing point\n  var X = Math.floor(x),\n      Y = Math.floor(y),\n      Z = Math.floor(z); // Get relative xyz coordinates of point within that cell\n\n  x = x - X;\n  y = y - Y;\n  z = z - Z; // Wrap the integer cells at 255 (smaller integer period can be introduced here)\n\n  X = X & 255;\n  Y = Y & 255;\n  Z = Z & 255; // Calculate noise contributions from each of the eight corners\n\n  var n000 = gradP[X + perm[Y + perm[Z]]].dot3(x, y, z);\n  var n001 = gradP[X + perm[Y + perm[Z + 1]]].dot3(x, y, z - 1);\n  var n010 = gradP[X + perm[Y + 1 + perm[Z]]].dot3(x, y - 1, z);\n  var n011 = gradP[X + perm[Y + 1 + perm[Z + 1]]].dot3(x, y - 1, z - 1);\n  var n100 = gradP[X + 1 + perm[Y + perm[Z]]].dot3(x - 1, y, z);\n  var n101 = gradP[X + 1 + perm[Y + perm[Z + 1]]].dot3(x - 1, y, z - 1);\n  var n110 = gradP[X + 1 + perm[Y + 1 + perm[Z]]].dot3(x - 1, y - 1, z);\n  var n111 = gradP[X + 1 + perm[Y + 1 + perm[Z + 1]]].dot3(x - 1, y - 1, z - 1); // Compute the fade curve value for x, y, z\n\n  var u = fade(x);\n  var v = fade(y);\n  var w = fade(z); // Interpolate\n\n  return lerp(lerp(lerp(n000, n100, u), lerp(n001, n101, u), w), lerp(lerp(n010, n110, u), lerp(n011, n111, u), w), v);\n};\n\nvar noise = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  seed: seed,\n  simplex2: simplex2,\n  simplex3: simplex3,\n  perlin2: perlin2,\n  perlin3: perlin3\n});\n\nvar TAU = Math.PI * 2; // Credits @kchapelier https://github.com/kchapelier/wavefunctioncollapse/blob/master/example/lcg.js#L22-L30\n\nfunction normalizeSeed(seed) {\n  if (typeof seed === \"number\") {\n    seed = Math.abs(seed);\n  } else if (typeof seed === \"string\") {\n    var string = seed;\n    seed = 0;\n\n    for (var i = 0; i < string.length; i++) {\n      seed = (seed + (i + 1) * (string.charCodeAt(i) % 96)) % 2147483647;\n    }\n  }\n\n  if (seed === 0) {\n    seed = 311;\n  }\n\n  return seed;\n}\n\nfunction lcgRandom(seed) {\n  var state = normalizeSeed(seed);\n  return function () {\n    var result = state * 48271 % 2147483647;\n    state = result;\n    return result / 2147483647;\n  };\n}\n\nvar Generator = function Generator(_seed) {\n  var _this = this;\n\n  _classCallCheck(this, Generator);\n\n  _defineProperty(this, \"seed\", 0);\n\n  _defineProperty(this, \"init\", function (seed) {\n    _this.seed = seed;\n    _this.value = lcgRandom(seed);\n  });\n\n  _defineProperty(this, \"value\", lcgRandom(this.seed));\n\n  this.init(_seed);\n};\nvar defaultGen = new Generator(Math.random());\n/***\n * [3D] Sphere\n */\n\nvar defaultSphere = {\n  radius: 1,\n  center: [0, 0, 0]\n}; // random on surface of sphere\n// - https://twitter.com/fermatslibrary/status/1430932503578226688\n// - https://mathworld.wolfram.com/SpherePointPicking.html\n\nfunction onSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere.radius,\n      center = _defaultSphere$sphere.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = rng.value();\n    var v = rng.value();\n    var theta = Math.acos(2 * v - 1);\n    var phi = TAU * u;\n    buffer[i] = Math.sin(theta) * Math.cos(phi) * radius + center[0];\n    buffer[i + 1] = Math.sin(theta) * Math.sin(phi) * radius + center[1];\n    buffer[i + 2] = Math.cos(theta) * radius + center[2];\n  }\n\n  return buffer;\n} // from \"Another Method\" https://datagenetics.com/blog/january32020/index.html\n\nfunction inSphere(buffer, sphere) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultSphere$sphere2 = _objectSpread2(_objectSpread2({}, defaultSphere), sphere),\n      radius = _defaultSphere$sphere2.radius,\n      center = _defaultSphere$sphere2.center;\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    var u = Math.pow(rng.value(), 1 / 3);\n    var x = rng.value() * 2 - 1;\n    var y = rng.value() * 2 - 1;\n    var z = rng.value() * 2 - 1;\n    var mag = Math.sqrt(x * x + y * y + z * z);\n    x = u * x / mag;\n    y = u * y / mag;\n    z = u * z / mag;\n    buffer[i] = x * radius + center[0];\n    buffer[i + 1] = y * radius + center[1];\n    buffer[i + 2] = z * radius + center[2];\n  }\n\n  return buffer;\n}\n/***\n * [2D] Circle\n */\n\nvar defaultCircle = {\n  radius: 1,\n  center: [0, 0]\n}; // random circle https://stackoverflow.com/a/50746409\n\nfunction inCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle.radius,\n      center = _defaultCircle$circle.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var r = radius * Math.sqrt(rng.value());\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * r + center[0];\n    buffer[i + 1] = Math.cos(theta) * r + center[1];\n  }\n\n  return buffer;\n}\nfunction onCircle(buffer, circle) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultCircle$circle2 = _objectSpread2(_objectSpread2({}, defaultCircle), circle),\n      radius = _defaultCircle$circle2.radius,\n      center = _defaultCircle$circle2.center;\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    var theta = rng.value() * TAU;\n    buffer[i] = Math.sin(theta) * radius + center[0];\n    buffer[i + 1] = Math.cos(theta) * radius + center[1];\n  }\n\n  return buffer;\n}\n/**\n * [2D] Plane\n */\n\nvar defaultRect = {\n  sides: 1,\n  center: [0, 0]\n};\nfunction inRect(buffer, rect) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultRect$rect = _objectSpread2(_objectSpread2({}, defaultRect), rect),\n      sides = _defaultRect$rect.sides,\n      center = _defaultRect$rect.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n\n  for (var i = 0; i < buffer.length; i += 2) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n  }\n\n  return buffer;\n}\nfunction onRect(buffer, rect) {\n  return buffer;\n}\n/***\n * [3D] Box\n */\n\nfunction inBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box.sides,\n      center = _defaultBox$box.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\nvar defaultBox = {\n  sides: 1,\n  center: [0, 0, 0]\n};\nfunction onBox(buffer, box) {\n  var rng = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultGen;\n\n  var _defaultBox$box2 = _objectSpread2(_objectSpread2({}, defaultBox), box),\n      sides = _defaultBox$box2.sides,\n      center = _defaultBox$box2.center;\n\n  var sideX = typeof sides === \"number\" ? sides : sides[0];\n  var sideY = typeof sides === \"number\" ? sides : sides[1];\n  var sideZ = typeof sides === \"number\" ? sides : sides[2];\n\n  for (var i = 0; i < buffer.length; i += 3) {\n    buffer[i] = (rng.value() - 0.5) * sideX + center[0];\n    buffer[i + 1] = (rng.value() - 0.5) * sideY + center[1];\n    buffer[i + 2] = (rng.value() - 0.5) * sideZ + center[2];\n  }\n\n  return buffer;\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Generator: Generator,\n  onSphere: onSphere,\n  inSphere: inSphere,\n  inCircle: inCircle,\n  onCircle: onCircle,\n  inRect: inRect,\n  onRect: onRect,\n  inBox: inBox,\n  onBox: onBox,\n  noise: noise\n});\n\nexport { Generator as G, inSphere as a, inCircle as b, onCircle as c, inRect as d, onRect as e, inBox as f, onBox as g, index as i, noise as n, onSphere as o };\n", "import { a as _toConsumableArray } from './triangle-b62b9067.esm.js';\nimport { Color, Vector3, Quaternion, Vector2, Vector4, Euler, Spherical, Matrix4 } from 'three';\nimport { d as deltaAngle } from './misc-7d870b3c.esm.js';\n\n/**\n * Rounded square wave easing\n */\n\nvar rsqw = function rsqw(t) {\n  var delta = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0.01;\n  var a = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;\n  var f = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1 / (2 * Math.PI);\n  return a / Math.atan(1 / delta) * Math.atan(Math.sin(2 * Math.PI * t * f) / delta);\n};\n/**\n * Exponential easing\n */\n\nvar exp = function exp(t) {\n  return 1 / (1 + t + 0.48 * t * t + 0.235 * t * t * t);\n};\n/**\n * Damp, based on Game Programming Gems 4 Chapter 1.10\n *   Return value indicates whether the animation is still running.\n */\n\nfunction damp(\n/** The object */\ncurrent,\n/** The key to animate */\nprop,\n/** To goal value */\ntarget) {\n  var smoothTime = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.25;\n  var delta = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0.01;\n  var maxSpeed = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : Infinity;\n  var easing = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : exp;\n  var eps = arguments.length > 7 && arguments[7] !== undefined ? arguments[7] : 0.001;\n  var vel = \"velocity_\" + prop;\n  if (current.__damp === undefined) current.__damp = {};\n  if (current.__damp[vel] === undefined) current.__damp[vel] = 0;\n\n  if (Math.abs(current[prop] - target) <= eps) {\n    current[prop] = target;\n    return false;\n  }\n\n  smoothTime = Math.max(0.0001, smoothTime);\n  var omega = 2 / smoothTime;\n  var t = easing(omega * delta);\n  var change = current[prop] - target;\n  var originalTo = target; // Clamp maximum maxSpeed\n\n  var maxChange = maxSpeed * smoothTime;\n  change = Math.min(Math.max(change, -maxChange), maxChange);\n  target = current[prop] - change;\n  var temp = (current.__damp[vel] + omega * change) * delta;\n  current.__damp[vel] = (current.__damp[vel] - omega * temp) * t;\n  var output = target + (change + temp) * t; // Prevent overshooting\n\n  if (originalTo - current[prop] > 0.0 === output > originalTo) {\n    output = originalTo;\n    current.__damp[vel] = (output - originalTo) / delta;\n  }\n\n  current[prop] = output;\n  return true;\n}\n/**\n * DampAngle, based on Game Programming Gems 4 Chapter 1.10\n */\n\nfunction dampAngle(current, prop, target, smoothTime, delta, maxSpeed, easing, eps) {\n  return damp(current, prop, current[prop] + deltaAngle(current[prop], target), smoothTime, delta, maxSpeed, easing, eps);\n}\n/**\n * Vector2D Damp\n */\n\nvar v2d = /*@__PURE__*/new Vector2();\nvar a2, b2;\nfunction damp2(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v2d.setScalar(target);else if (Array.isArray(target)) v2d.set(target[0], target[1]);else v2d.copy(target);\n  a2 = damp(current, \"x\", v2d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b2 = damp(current, \"y\", v2d.y, smoothTime, delta, maxSpeed, easing, eps);\n  return a2 || b2;\n}\n/**\n * Vector3D Damp\n */\n\nvar v3d = /*@__PURE__*/new Vector3();\nvar a3, b3, c3;\nfunction damp3(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v3d.setScalar(target);else if (Array.isArray(target)) v3d.set(target[0], target[1], target[2]);else v3d.copy(target);\n  a3 = damp(current, \"x\", v3d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b3 = damp(current, \"y\", v3d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c3 = damp(current, \"z\", v3d.z, smoothTime, delta, maxSpeed, easing, eps);\n  return a3 || b3 || c3;\n}\n/**\n * Vector4D Damp\n */\n\nvar v4d = /*@__PURE__*/new Vector4();\nvar a4, b4, c4, d4;\nfunction damp4(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (typeof target === \"number\") v4d.setScalar(target);else if (Array.isArray(target)) v4d.set(target[0], target[1], target[2], target[3]);else v4d.copy(target);\n  a4 = damp(current, \"x\", v4d.x, smoothTime, delta, maxSpeed, easing, eps);\n  b4 = damp(current, \"y\", v4d.y, smoothTime, delta, maxSpeed, easing, eps);\n  c4 = damp(current, \"z\", v4d.z, smoothTime, delta, maxSpeed, easing, eps);\n  d4 = damp(current, \"w\", v4d.w, smoothTime, delta, maxSpeed, easing, eps);\n  return a4 || b4 || c4 || d4;\n}\n/**\n * Euler Damp\n */\n\nvar rot = /*@__PURE__*/new Euler();\nvar aE, bE, cE;\nfunction dampE(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) rot.set(target[0], target[1], target[2], target[3]);else rot.copy(target);\n  aE = dampAngle(current, \"x\", rot.x, smoothTime, delta, maxSpeed, easing, eps);\n  bE = dampAngle(current, \"y\", rot.y, smoothTime, delta, maxSpeed, easing, eps);\n  cE = dampAngle(current, \"z\", rot.z, smoothTime, delta, maxSpeed, easing, eps);\n  return aE || bE || cE;\n}\n/**\n * Color Damp\n */\n\nvar col = /*@__PURE__*/new Color();\nvar aC, bC, cC;\nfunction dampC(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (target instanceof Color) col.copy(target);else if (Array.isArray(target)) col.setRGB(target[0], target[1], target[2]);else col.set(target);\n  aC = damp(current, \"r\", col.r, smoothTime, delta, maxSpeed, easing, eps);\n  bC = damp(current, \"g\", col.g, smoothTime, delta, maxSpeed, easing, eps);\n  cC = damp(current, \"b\", col.b, smoothTime, delta, maxSpeed, easing, eps);\n  return aC || bC || cC;\n}\n/**\n * Quaternion Damp\n * https://gist.github.com/maxattack/4c7b4de00f5c1b95a33b\n * Copyright 2016 Max Kaufmann (<EMAIL>)\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\nvar qt = /*@__PURE__*/new Quaternion();\nvar v4result = /*@__PURE__*/new Vector4();\nvar v4velocity = /*@__PURE__*/new Vector4();\nvar v4error = /*@__PURE__*/new Vector4();\nvar aQ, bQ, cQ, dQ;\nfunction dampQ(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n  if (Array.isArray(target)) qt.set(target[0], target[1], target[2], target[3]);else qt.copy(target);\n  var multi = current.dot(qt) > 0 ? 1 : -1;\n  qt.x *= multi;\n  qt.y *= multi;\n  qt.z *= multi;\n  qt.w *= multi;\n  aQ = damp(current, \"x\", qt.x, smoothTime, delta, maxSpeed, easing, eps);\n  bQ = damp(current, \"y\", qt.y, smoothTime, delta, maxSpeed, easing, eps);\n  cQ = damp(current, \"z\", qt.z, smoothTime, delta, maxSpeed, easing, eps);\n  dQ = damp(current, \"w\", qt.w, smoothTime, delta, maxSpeed, easing, eps); // smooth damp (nlerp approx)\n\n  v4result.set(current.x, current.y, current.z, current.w).normalize();\n  v4velocity.set(cur.__damp.velocity_x, cur.__damp.velocity_y, cur.__damp.velocity_z, cur.__damp.velocity_w); // ensure deriv is tangent\n\n  v4error.copy(v4result).multiplyScalar(v4velocity.dot(v4result) / v4result.dot(v4result));\n  cur.__damp.velocity_x -= v4error.x;\n  cur.__damp.velocity_y -= v4error.y;\n  cur.__damp.velocity_z -= v4error.z;\n  cur.__damp.velocity_w -= v4error.w;\n  current.set(v4result.x, v4result.y, v4result.z, v4result.w);\n  return aQ || bQ || cQ || dQ;\n}\n/**\n * Spherical Damp\n */\n\nvar spherical = /*@__PURE__*/new Spherical();\nvar aS, bS, cS;\nfunction dampS(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  if (Array.isArray(target)) spherical.set(target[0], target[1], target[2]);else spherical.copy(target);\n  aS = damp(current, \"radius\", spherical.radius, smoothTime, delta, maxSpeed, easing, eps);\n  bS = dampAngle(current, \"phi\", spherical.phi, smoothTime, delta, maxSpeed, easing, eps);\n  cS = dampAngle(current, \"theta\", spherical.theta, smoothTime, delta, maxSpeed, easing, eps);\n  return aS || bS || cS;\n}\n/**\n * Matrix4 Damp\n */\n\nvar mat = /*@__PURE__*/new Matrix4();\nvar mPos = /*@__PURE__*/new Vector3();\nvar mRot = /*@__PURE__*/new Quaternion();\nvar mSca = /*@__PURE__*/new Vector3();\nvar aM, bM, cM;\nfunction dampM(current, target, smoothTime, delta, maxSpeed, easing, eps) {\n  var cur = current;\n\n  if (cur.__damp === undefined) {\n    cur.__damp = {\n      position: new Vector3(),\n      rotation: new Quaternion(),\n      scale: new Vector3()\n    };\n    current.decompose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  }\n\n  if (Array.isArray(target)) mat.set.apply(mat, _toConsumableArray(target));else mat.copy(target);\n  mat.decompose(mPos, mRot, mSca);\n  aM = damp3(cur.__damp.position, mPos, smoothTime, delta, maxSpeed, easing, eps);\n  bM = dampQ(cur.__damp.rotation, mRot, smoothTime, delta, maxSpeed, easing, eps);\n  cM = damp3(cur.__damp.scale, mSca, smoothTime, delta, maxSpeed, easing, eps);\n  current.compose(cur.__damp.position, cur.__damp.rotation, cur.__damp.scale);\n  return aM || bM || cM;\n}\n\nvar easing = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  rsqw: rsqw,\n  exp: exp,\n  damp: damp,\n  dampAngle: dampAngle,\n  damp2: damp2,\n  damp3: damp3,\n  damp4: damp4,\n  dampE: dampE,\n  dampC: dampC,\n  dampQ: dampQ,\n  dampS: dampS,\n  dampM: dampM\n});\n\nexport { exp as a, dampAngle as b, damp2 as c, damp as d, easing as e, damp3 as f, damp4 as g, dampE as h, dampC as i, dampQ as j, dampS as k, dampM as l, rsqw as r };\n", "import { _ as _classCallCheck } from './classCallCheck-9098b006.esm.js';\nimport { _ as _setPrototypeOf, a as _isNativeReflectConstruct } from './isNativeReflectConstruct-5594d075.esm.js';\nimport * as THREE from 'three';\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n\n  return _assertThisInitialized(self);\n}\n\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n        result;\n\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n\n    return _possibleConstructorReturn(this, result);\n  };\n}\n\nvar RoundedPlaneGeometry = /*#__PURE__*/function (_THREE$BufferGeometry) {\n  _inherits(RoundedPlaneGeometry, _THREE$BufferGeometry);\n\n  var _super = _createSuper(RoundedPlaneGeometry);\n\n  function RoundedPlaneGeometry() {\n    var _this;\n\n    var width = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 2;\n    var height = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var radius = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.2;\n    var segments = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 16;\n\n    _classCallCheck(this, RoundedPlaneGeometry);\n\n    _this = _super.call(this); // helper const's\n\n    var wi = width / 2 - radius; // inner width\n\n    var hi = height / 2 - radius; // inner height\n\n    var ul = radius / width; // u left\n\n    var ur = (width - radius) / width; // u right\n\n    var vl = radius / height; // v low\n\n    var vh = (height - radius) / height; // v high\n\n    var positions = [wi, hi, 0, -wi, hi, 0, -wi, -hi, 0, wi, -hi, 0];\n    var uvs = [ur, vh, ul, vh, ul, vl, ur, vl];\n    var n = [3 * (segments + 1) + 3, 3 * (segments + 1) + 4, segments + 4, segments + 5, 2 * (segments + 1) + 4, 2, 1, 2 * (segments + 1) + 3, 3, 4 * (segments + 1) + 3, 4, 0];\n    var indices = [n[0], n[1], n[2], n[0], n[2], n[3], n[4], n[5], n[6], n[4], n[6], n[7], n[8], n[9], n[10], n[8], n[10], n[11]];\n    var phi, cos, sin, xc, yc, uc, vc, idx;\n\n    for (var i = 0; i < 4; i++) {\n      xc = i < 1 || i > 2 ? wi : -wi;\n      yc = i < 2 ? hi : -hi;\n      uc = i < 1 || i > 2 ? ur : ul;\n      vc = i < 2 ? vh : vl;\n\n      for (var j = 0; j <= segments; j++) {\n        phi = Math.PI / 2 * (i + j / segments);\n        cos = Math.cos(phi);\n        sin = Math.sin(phi);\n        positions.push(xc + radius * cos, yc + radius * sin, 0);\n        uvs.push(uc + ul * cos, vc + vl * sin);\n\n        if (j < segments) {\n          idx = (segments + 1) * i + j + 4;\n          indices.push(i, idx, idx + 1);\n        }\n      }\n    }\n\n    _this.setIndex(new THREE.BufferAttribute(new Uint32Array(indices), 1));\n\n    _this.setAttribute(\"position\", new THREE.BufferAttribute(new Float32Array(positions), 3));\n\n    _this.setAttribute(\"uv\", new THREE.BufferAttribute(new Float32Array(uvs), 2));\n\n    return _this;\n  }\n\n  return RoundedPlaneGeometry;\n}(THREE.BufferGeometry); // Author: https://stackoverflow.com/users/268905/knee-cola\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\nfunction applySphereUV(bufferGeometry) {\n  var uvs = [];\n  var vertices = [];\n\n  for (var i = 0; i < bufferGeometry.attributes.position.array.length / 3; i++) {\n    var x = bufferGeometry.attributes.position.array[i * 3 + 0];\n    var y = bufferGeometry.attributes.position.array[i * 3 + 1];\n    var z = bufferGeometry.attributes.position.array[i * 3 + 2];\n    vertices.push(new THREE.Vector3(x, y, z));\n  }\n\n  var polarVertices = vertices.map(cartesian2polar);\n\n  for (var _i = 0; _i < polarVertices.length / 3; _i++) {\n    var tri = new THREE.Triangle(vertices[_i * 3 + 0], vertices[_i * 3 + 1], vertices[_i * 3 + 2]);\n    var normal = tri.getNormal(new THREE.Vector3());\n\n    for (var f = 0; f < 3; f++) {\n      var vertex = polarVertices[_i * 3 + f];\n\n      if (vertex.theta === 0 && (vertex.phi === 0 || vertex.phi === Math.PI)) {\n        var alignedVertice = vertex.phi === 0 ? _i * 3 + 1 : _i * 3 + 0;\n        vertex = {\n          r: vertex.r,\n          phi: vertex.phi,\n          theta: polarVertices[alignedVertice].theta\n        };\n      }\n\n      if (vertex.theta === Math.PI && cartesian2polar(normal).theta < Math.PI / 2) {\n        vertex.theta = -Math.PI;\n      }\n\n      var canvasPoint = polar2canvas(vertex);\n      uvs.push(1 - canvasPoint.x, 1 - canvasPoint.y);\n    }\n  }\n\n  if (bufferGeometry.attributes.uv) delete bufferGeometry.attributes.uv;\n  bufferGeometry.setAttribute(\"uv\", new THREE.Float32BufferAttribute(uvs, 2));\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction cartesian2polar(position) {\n  var r = Math.sqrt(position.x * position.x + position.z * position.z + position.y * position.y);\n  return {\n    r: r,\n    phi: Math.acos(position.y / r),\n    theta: Math.atan2(position.z, position.x)\n  };\n}\n\nfunction polar2canvas(polarPoint) {\n  return {\n    y: polarPoint.phi / Math.PI,\n    x: (polarPoint.theta + Math.PI) / (2 * Math.PI)\n  };\n} // Author: Alex Khoroshylov (https://stackoverflow.com/users/8742287/alex-khoroshylov)\n// https://stackoverflow.com/questions/20774648/three-js-generate-uv-coordinate\n\n\nfunction applyBoxUV(bufferGeometry) {\n  bufferGeometry.computeBoundingBox();\n  var bboxSize = bufferGeometry.boundingBox.getSize(new THREE.Vector3());\n  var boxSize = Math.min(bboxSize.x, bboxSize.y, bboxSize.z);\n  var boxGeometry = new THREE.BoxGeometry(boxSize, boxSize, boxSize);\n  var cube = new THREE.Mesh(boxGeometry);\n  cube.rotation.set(0, 0, 0);\n  cube.updateWorldMatrix(true, false);\n  var transformMatrix = cube.matrix.clone().invert();\n  var uvBbox = new THREE.Box3(new THREE.Vector3(-boxSize / 2, -boxSize / 2, -boxSize / 2), new THREE.Vector3(boxSize / 2, boxSize / 2, boxSize / 2));\n\n  _applyBoxUV(bufferGeometry, transformMatrix, uvBbox, boxSize);\n\n  bufferGeometry.attributes.uv.needsUpdate = true;\n  return bufferGeometry;\n}\n\nfunction _applyBoxUV(geom, transformMatrix, bbox, bbox_max_size) {\n  var coords = [];\n  coords.length = 2 * geom.attributes.position.array.length / 3; //maps 3 verts of 1 face on the better side of the cube\n  //side of the cube can be XY, XZ or YZ\n\n  var makeUVs = function makeUVs(v0, v1, v2) {\n    //pre-rotate the model so that cube sides match world axis\n    v0.applyMatrix4(transformMatrix);\n    v1.applyMatrix4(transformMatrix);\n    v2.applyMatrix4(transformMatrix); //get normal of the face, to know into which cube side it maps better\n\n    var n = new THREE.Vector3();\n    n.crossVectors(v1.clone().sub(v0), v1.clone().sub(v2)).normalize();\n    n.x = Math.abs(n.x);\n    n.y = Math.abs(n.y);\n    n.z = Math.abs(n.z);\n    var uv0 = new THREE.Vector2();\n    var uv1 = new THREE.Vector2();\n    var uv2 = new THREE.Vector2(); // xz mapping\n\n    if (n.y > n.x && n.y > n.z) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (bbox.max.z - v0.z) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (bbox.max.z - v1.z) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (bbox.max.z - v2.z) / bbox_max_size;\n    } else if (n.x > n.y && n.x > n.z) {\n      uv0.x = (v0.z - bbox.min.z) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.z - bbox.min.z) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.z - bbox.min.z) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    } else if (n.z > n.y && n.z > n.x) {\n      uv0.x = (v0.x - bbox.min.x) / bbox_max_size;\n      uv0.y = (v0.y - bbox.min.y) / bbox_max_size;\n      uv1.x = (v1.x - bbox.min.x) / bbox_max_size;\n      uv1.y = (v1.y - bbox.min.y) / bbox_max_size;\n      uv2.x = (v2.x - bbox.min.x) / bbox_max_size;\n      uv2.y = (v2.y - bbox.min.y) / bbox_max_size;\n    }\n\n    return {\n      uv0: uv0,\n      uv1: uv1,\n      uv2: uv2\n    };\n  };\n\n  if (geom.index) {\n    // is it indexed buffer geometry?\n    for (var vi = 0; vi < geom.index.array.length; vi += 3) {\n      var idx0 = geom.index.array[vi];\n      var idx1 = geom.index.array[vi + 1];\n      var idx2 = geom.index.array[vi + 2];\n      var vx0 = geom.attributes.position.array[3 * idx0];\n      var vy0 = geom.attributes.position.array[3 * idx0 + 1];\n      var vz0 = geom.attributes.position.array[3 * idx0 + 2];\n      var vx1 = geom.attributes.position.array[3 * idx1];\n      var vy1 = geom.attributes.position.array[3 * idx1 + 1];\n      var vz1 = geom.attributes.position.array[3 * idx1 + 2];\n      var vx2 = geom.attributes.position.array[3 * idx2];\n      var vy2 = geom.attributes.position.array[3 * idx2 + 1];\n      var vz2 = geom.attributes.position.array[3 * idx2 + 2];\n      var v0 = new THREE.Vector3(vx0, vy0, vz0);\n      var v1 = new THREE.Vector3(vx1, vy1, vz1);\n      var v2 = new THREE.Vector3(vx2, vy2, vz2);\n      var uvs = makeUVs(v0, v1, v2);\n      coords[2 * idx0] = uvs.uv0.x;\n      coords[2 * idx0 + 1] = uvs.uv0.y;\n      coords[2 * idx1] = uvs.uv1.x;\n      coords[2 * idx1 + 1] = uvs.uv1.y;\n      coords[2 * idx2] = uvs.uv2.x;\n      coords[2 * idx2 + 1] = uvs.uv2.y;\n    }\n  } else {\n    for (var _vi = 0; _vi < geom.attributes.position.array.length; _vi += 9) {\n      var _vx = geom.attributes.position.array[_vi];\n      var _vy = geom.attributes.position.array[_vi + 1];\n      var _vz = geom.attributes.position.array[_vi + 2];\n      var _vx2 = geom.attributes.position.array[_vi + 3];\n      var _vy2 = geom.attributes.position.array[_vi + 4];\n      var _vz2 = geom.attributes.position.array[_vi + 5];\n      var _vx3 = geom.attributes.position.array[_vi + 6];\n      var _vy3 = geom.attributes.position.array[_vi + 7];\n      var _vz3 = geom.attributes.position.array[_vi + 8];\n\n      var _v = new THREE.Vector3(_vx, _vy, _vz);\n\n      var _v2 = new THREE.Vector3(_vx2, _vy2, _vz2);\n\n      var _v3 = new THREE.Vector3(_vx3, _vy3, _vz3);\n\n      var _uvs = makeUVs(_v, _v2, _v3);\n\n      var _idx = _vi / 3;\n\n      var _idx2 = _idx + 1;\n\n      var _idx3 = _idx + 2;\n\n      coords[2 * _idx] = _uvs.uv0.x;\n      coords[2 * _idx + 1] = _uvs.uv0.y;\n      coords[2 * _idx2] = _uvs.uv1.x;\n      coords[2 * _idx2 + 1] = _uvs.uv1.y;\n      coords[2 * _idx3] = _uvs.uv2.x;\n      coords[2 * _idx3 + 1] = _uvs.uv2.y;\n    }\n  }\n\n  if (geom.attributes.uv) delete geom.attributes.uv;\n  geom.setAttribute(\"uv\", new THREE.Float32BufferAttribute(coords, 2));\n}\n\nvar geometry = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  RoundedPlaneGeometry: RoundedPlaneGeometry,\n  applySphereUV: applySphereUV,\n  applyBoxUV: applyBoxUV\n});\n\nexport { RoundedPlaneGeometry as R, applySphereUV as a, applyBoxUV as b, geometry as g };\n", "import { Vector3, Vector2 } from 'three';\n\n/**\n * Helpers for converting buffers to and from Three.js objects\n */\n\n/**\n * Convents passed buffer of passed stride to an array of vectors with the correct length.\n *\n * @param buffer\n * @param stride\n * @returns\n */\nfunction bufferToVectors(buffer) {\n  var stride = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  var p = [];\n\n  for (var i = 0, j = 0; i < buffer.length; i += stride, j++) {\n    if (stride === 3) {\n      p[j] = new Vector3(buffer[i], buffer[i + 1], buffer[i + 2]);\n    } else {\n      p[j] = new Vector2(buffer[i], buffer[i + 1]);\n    }\n  }\n\n  return p;\n}\n/**\n * Transforms a passed Vector2 or Vector3 array to a points buffer\n *\n * @param vectorArray\n * @returns\n */\n\nfunction vectorsToBuffer(vectorArray) {\n  var l = vectorArray.length;\n  var stride = vectorArray[0].hasOwnProperty(\"z\") ? 3 : 2;\n  var buffer = new Float32Array(l * stride);\n\n  for (var i = 0; i < l; i++) {\n    var j = i * stride;\n    buffer[j] = vectorArray[i].x;\n    buffer[j + 1] = vectorArray[i].y;\n\n    if (stride === 3) {\n      buffer[j + 2] = vectorArray[i].z;\n    }\n  }\n\n  return buffer;\n}\n\nvar three = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  bufferToVectors: bufferToVectors,\n  vectorsToBuffer: vectorsToBuffer\n});\n\nexport { bufferToVectors as b, three as t, vectorsToBuffer as v };\n", "import {\n\t<PERSON><PERSON>erG<PERSON><PERSON>,\n\tFloat32BufferAttribute,\n\tOrthographicCamera,\n\tMesh\n} from 'three';\n\nclass Pass {\n\n\tconstructor() {\n\n\t\tthis.isPass = true;\n\n\t\t// if set to true, the pass is processed by the composer\n\t\tthis.enabled = true;\n\n\t\t// if set to true, the pass indicates to swap read and write buffer after rendering\n\t\tthis.needsSwap = true;\n\n\t\t// if set to true, the pass clears its buffer before rendering\n\t\tthis.clear = false;\n\n\t\t// if set to true, the result of the pass is rendered to screen. This is set automatically by EffectComposer.\n\t\tthis.renderToScreen = false;\n\n\t}\n\n\tsetSize( /* width, height */ ) {}\n\n\trender( /* renderer, writeBuffer, readBuffer, deltaTime, maskActive */ ) {\n\n\t\tconsole.error( 'THREE.Pass: .render() must be implemented in derived pass.' );\n\n\t}\n\n\tdispose() {}\n\n}\n\n// Helper for passes that need to fill the viewport with a single quad.\n\nconst _camera = new OrthographicCamera( - 1, 1, 1, - 1, 0, 1 );\n\n// https://github.com/mrdoob/three.js/pull/21358\n\nclass FullscreenTriangleGeometry extends BufferGeometry {\n\n\tconstructor() {\n\n\t\tsuper();\n\n\t\tthis.setAttribute( 'position', new Float32BufferAttribute( [ - 1, 3, 0, - 1, - 1, 0, 3, - 1, 0 ], 3 ) );\n\t\tthis.setAttribute( 'uv', new Float32BufferAttribute( [ 0, 2, 0, 0, 2, 0 ], 2 ) );\n\n\t}\n\n}\n\nconst _geometry = new FullscreenTriangleGeometry();\n\nclass FullScreenQuad {\n\n\tconstructor( material ) {\n\n\t\tthis._mesh = new Mesh( _geometry, material );\n\n\t}\n\n\tdispose() {\n\n\t\tthis._mesh.geometry.dispose();\n\n\t}\n\n\trender( renderer ) {\n\n\t\trenderer.render( this._mesh, _camera );\n\n\t}\n\n\tget material() {\n\n\t\treturn this._mesh.material;\n\n\t}\n\n\tset material( value ) {\n\n\t\tthis._mesh.material = value;\n\n\t}\n\n}\n\nexport { Pass, FullScreenQuad };\n", "import * as THREE from 'three';\nimport { Pass } from \"three/examples/jsm/postprocessing/Pass.js\";\nimport { FullScreenTriangle } from './FullScreenTriangle.js';\nimport { EffectShader } from './EffectShader.js';\nimport { EffectCompositer } from './EffectCompositer.js';\nimport { PoissionBlur } from './PoissionBlur.js';\nimport { DepthDownSample } from \"./DepthDownSample.js\";\nimport { N8AOPostPass } from './N8AOPostPass.js';\nimport bluenoiseBits from './BlueNoise.js';\nimport { WebGLMultipleRenderTargetsCompat } from './compat.js';\n\n/**\n * \n * @param {*} timerQuery \n * @param {THREE.WebGLRenderer} gl \n * @param {N8AOPass} pass \n */\nfunction checkTimerQuery(timerQuery, gl, pass) {\n    const available = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT_AVAILABLE);\n    if (available) {\n        const elapsedTimeInNs = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT);\n        const elapsedTimeInMs = elapsedTimeInNs / 1000000;\n        pass.lastTime = pass.lastTime === 0 ? elapsedTimeInMs : pass.timeRollingAverage * pass.lastTime + (1 - pass.timeRollingAverage) * elapsedTimeInMs;\n    } else {\n        // If the result is not available yet, check again after a delay\n        setTimeout(() => {\n            checkTimerQuery(timerQuery, gl, pass);\n        }, 1);\n    }\n}\n\nexport const DepthType = {\n    Default: 1,\n    Log: 2,\n    Reverse: 3,\n};\n\nclass N8AOPass extends Pass {\n    /**\n     * \n     * @param {THREE.Scene} scene\n     * @param {THREE.Camera} camera \n     * @param {number} width \n     * @param {number} height\n     *  \n     * @property {THREE.Scene} scene\n     * @property {THREE.Camera} camera\n     * @property {number} width\n     * @property {number} height\n     */\n    constructor(scene, camera, width = 512, height = 512) {\n        super();\n        this.width = width;\n        this.height = height;\n\n        this.clear = true;\n\n        this.camera = camera;\n        this.scene = scene;\n        /**\n         * @type {Proxy & {\n         * aoSamples: number,\n         * aoRadius: number,\n         * denoiseSamples: number,\n         * denoiseRadius: number,\n         * distanceFalloff: number,\n         * intensity: number,\n         * denoiseIterations: number,\n         * renderMode: 0 | 1 | 2 | 3 | 4,\n         * color: THREE.Color,\n         * gammaCorrection: boolean,\n         * depthBufferType: 1 | 2 | 3,\n         * screenSpaceRadius: boolean,\n         * halfRes: boolean,\n         * depthAwareUpsampling: boolean,\n         * autoRenderBeauty: boolean\n         * colorMultiply: boolean\n         * }\n         */\n        this.configuration = new Proxy({\n            aoSamples: 16,\n            aoRadius: 5.0,\n            aoTones: 0.0,\n            denoiseSamples: 8,\n            denoiseRadius: 12,\n            distanceFalloff: 1.0,\n            intensity: 5,\n            denoiseIterations: 2.0,\n            renderMode: 0,\n            biasOffset: 0.0,\n            biasMultiplier: 0.0,\n            color: new THREE.Color(0, 0, 0),\n            gammaCorrection: true,\n            depthBufferType: DepthType.Default,\n            screenSpaceRadius: false,\n            halfRes: false,\n            depthAwareUpsampling: true,\n            autoRenderBeauty: true,\n            colorMultiply: true,\n            transparencyAware: false,\n            stencil: false,\n            accumulate: false\n        }, {\n            set: (target, propName, value) => {\n                const oldProp = target[propName];\n                target[propName] = value;\n                if (value.equals) {\n                    if (!value.equals(oldProp)) {\n                        this.firstFrame();\n                    }\n                } else {\n                    if (oldProp !== value) {\n                        this.firstFrame();\n                    }\n                }\n                if (propName === 'aoSamples' && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === 'denoiseSamples' && oldProp !== value) {\n                    this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === \"halfRes\" && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.configureHalfResTargets();\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.setSize(this.width, this.height);\n                }\n                if (propName === \"depthAwareUpsampling\" && oldProp !== value) {\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === \"transparencyAware\" && oldProp !== value) {\n                    this.autoDetectTransparency = false;\n                    this.configureTransparencyTarget();\n                }\n                if (propName === \"stencil\" && oldProp !== value) {\n                    /*  this.beautyRenderTarget.stencilBuffer = value;\n                      this.beautyRenderTarget.depthTexture.format = value ? THREE.DepthStencilFormat : THREE.DepthFormat;\n                      this.beautyRenderTarget.depthTexture.type = value ? THREE.UnsignedInt248Type : THREE.UnsignedIntType;\n                      this.beautyRenderTarget.depthTexture.needsUpdate = true;\n                      this.beautyRenderTarget.needsUpdate = true;*/\n                    this.beautyRenderTarget.dispose();\n                    this.beautyRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n                        minFilter: THREE.LinearFilter,\n                        magFilter: THREE.NearestFilter,\n                        type: THREE.HalfFloatType,\n                        format: THREE.RGBAFormat,\n                        stencilBuffer: value\n                    });\n                    this.beautyRenderTarget.depthTexture = new THREE.DepthTexture(this.width, this.height, value ? THREE.UnsignedInt248Type : THREE.UnsignedIntType);\n                    this.beautyRenderTarget.depthTexture.format = value ? THREE.DepthStencilFormat : THREE.DepthFormat;\n                }\n                return true;\n            }\n        });\n        /** @type {THREE.Vector3[]} */\n        this.samples = [];\n        /** @type {THREE.Vector2[]} */\n        this.samplesDenoise = [];\n        this.autoDetectTransparency = true;\n        this.frame = 0;\n        this.lastViewMatrix = new THREE.Matrix4();\n        this.lastProjectionMatrix = new THREE.Matrix4();\n        this.beautyRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.NearestFilter,\n            type: THREE.HalfFloatType,\n            format: THREE.RGBAFormat,\n            stencilBuffer: false\n        });\n        this.beautyRenderTarget.depthTexture = new THREE.DepthTexture(this.width, this.height, THREE.UnsignedIntType);\n        this.beautyRenderTarget.depthTexture.format = THREE.DepthFormat;\n        this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureSampleDependentPasses();\n        this.configureHalfResTargets();\n        this.detectTransparency();\n        this.configureTransparencyTarget();\n\n\n        this.writeTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.readTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.accumulationRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat,\n            type: THREE.HalfFloatType,\n            stencilBuffer: false,\n            depthBuffer: false,\n            alpha: true\n        });\n\n\n        /** @type {THREE.DataTexture} */\n        this.bluenoise = //bluenoise;\n            new THREE.DataTexture(\n                bluenoiseBits,\n                128,\n                128\n            );\n        this.accumulationQuad = new FullScreenTriangle(new THREE.ShaderMaterial({\n            uniforms: {\n                frame: { value: 0 },\n                tDiffuse: { value: null }\n            },\n            transparent: true,\n            opacity: 1,\n            vertexShader: `\n             varying vec2 vUv;\n             void main() {\n                 vUv = uv;\n                 gl_Position = vec4(position, 1);\n             }`,\n            fragmentShader: `\n             uniform sampler2D tDiffuse;\n             uniform float frame;\n                varying vec2 vUv;\n                void main() {\n                    vec4 color = texture2D(tDiffuse, vUv);\n                    gl_FragColor = vec4(color.rgb, 1.0 / (frame + 1.0));\n                }\n                `\n        }));\n        this.bluenoise.colorSpace = THREE.NoColorSpace;\n        this.bluenoise.wrapS = THREE.RepeatWrapping;\n        this.bluenoise.wrapT = THREE.RepeatWrapping;\n        this.bluenoise.minFilter = THREE.NearestFilter;\n        this.bluenoise.magFilter = THREE.NearestFilter;\n        this.bluenoise.needsUpdate = true;\n        this.lastTime = 0;\n        this.timeRollingAverage = 0.99;\n        this._r = new THREE.Vector2();\n        this._c = new THREE.Color();\n\n    }\n    configureHalfResTargets() {\n        this.firstFrame();\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget = new WebGLMultipleRenderTargetsCompat(\n                this.width / 2,\n                this.height / 2,\n                2\n            );\n\n            if (THREE.REVISION <= 161) {\n                this.depthDownsampleTarget.textures = this.depthDownsampleTarget.texture;\n            }\n            this.depthDownsampleTarget.textures[0].format = THREE.RedFormat;\n            this.depthDownsampleTarget.textures[0].type = THREE.FloatType;\n            this.depthDownsampleTarget.textures[0].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].depthBuffer = false;\n            this.depthDownsampleTarget.textures[1].format = THREE.RGBAFormat;\n            this.depthDownsampleTarget.textures[1].type = THREE.HalfFloatType;\n            this.depthDownsampleTarget.textures[1].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].depthBuffer = false;\n\n            const e = {...DepthDownSample };\n            if (this.configuration.depthBufferType === DepthType.Reverse) {\n                e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            }\n\n            this.depthDownsampleQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n        } else {\n            if (this.depthDownsampleTarget) {\n                this.depthDownsampleTarget.dispose();\n                this.depthDownsampleTarget = null;\n            }\n            if (this.depthDownsampleQuad) {\n                this.depthDownsampleQuad.dispose();\n                this.depthDownsampleQuad = null;\n            }\n        }\n    }\n    detectTransparency() {\n        if (this.autoDetectTransparency) {\n            let isTransparency = false;\n            this.scene.traverse((obj) => {\n                if (obj.material && obj.material.transparent) {\n                    isTransparency = true;\n                }\n            });\n            this.configuration.transparencyAware = isTransparency;\n        }\n    }\n    configureTransparencyTarget() {\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue.depthTexture = new THREE.DepthTexture(this.width, this.height, THREE.UnsignedIntType);\n            this.depthCopyPass = new FullScreenTriangle(new THREE.ShaderMaterial({\n                uniforms: {\n                    depthTexture: { value: this.depthTexture },\n                    reverseDepthBuffer: { value: this.configuration.depthBufferType === DepthType.Reverse },\n                },\n                vertexShader: /* glsl */ `\n                        varying vec2 vUv;\n                        void main() {\n                            vUv = uv;\n                            gl_Position = vec4(position, 1);\n                        }`,\n                fragmentShader: /* glsl */ `\n                        uniform sampler2D depthTexture;\n                        uniform bool reverseDepthBuffer;\n                        varying vec2 vUv;\n                        void main() {\n                            if (reverseDepthBuffer) {\n                           float d = 1.0 - texture2D(depthTexture, vUv).r;\n                       \n                           d += 0.00001;\n                           gl_FragDepth = 1.0 - d;\n                        } else {\n                            float d = texture2D(depthTexture, vUv).r;\n                            d += 0.00001;\n                            gl_FragDepth = d;\n                        }\n                           gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n                        }\n                        `,\n\n            }));\n        } else {\n            if (this.transparencyRenderTargetDWFalse) {\n                this.transparencyRenderTargetDWFalse.dispose();\n                this.transparencyRenderTargetDWFalse = null;\n            }\n            if (this.transparencyRenderTargetDWTrue) {\n                this.transparencyRenderTargetDWTrue.dispose();\n                this.transparencyRenderTargetDWTrue = null;\n            }\n            if (this.depthCopyPass) {\n                this.depthCopyPass.dispose();\n                this.depthCopyPass = null;\n            }\n        }\n    }\n    renderTransparency(renderer) {\n        const oldBackground = this.scene.background;\n        const oldClearColor = renderer.getClearColor(new THREE.Color());\n        const oldClearAlpha = renderer.getClearAlpha();\n        const oldVisibility = new Map();\n        const oldAutoClearDepth = renderer.autoClearDepth;\n        this.scene.traverse((obj) => {\n            oldVisibility.set(obj, obj.visible);\n        });\n\n        // Override the state\n        this.scene.background = null;\n        renderer.autoClearDepth = false;\n        renderer.setClearColor(new THREE.Color(0, 0, 0), 0);\n\n        this.depthCopyPass.material.uniforms.depthTexture.value = this.beautyRenderTarget.depthTexture;\n        this.depthCopyPass.material.uniforms.reverseDepthBuffer.value = this.configuration.depthBufferType === DepthType.Reverse;\n        // Render out transparent objects WITHOUT depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWFalse);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && ((obj.material.transparent && !obj.material.depthWrite && !obj.userData.treatAsOpaque) || !!obj.userData.cannotReceiveAO);\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Render out transparent objects WITH depth write\n\n        renderer.setRenderTarget(this.transparencyRenderTargetDWTrue);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && obj.material.transparent && obj.material.depthWrite && !obj.userData.treatAsOpaque;\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Restore\n        this.scene.traverse((obj) => {\n            obj.visible = oldVisibility.get(obj);\n        });\n        renderer.setClearColor(oldClearColor, oldClearAlpha);\n        this.scene.background = oldBackground;\n        renderer.autoClearDepth = oldAutoClearDepth;\n    }\n    configureSampleDependentPasses() {\n        this.firstFrame();\n        this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n    }\n    configureAOPass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samples = this.generateHemisphereSamples(this.configuration.aoSamples);\n        const e = {...EffectShader };\n        e.fragmentShader = e.fragmentShader.replace(\"16\", this.configuration.aoSamples).replace(\"16.0\", this.configuration.aoSamples + \".0\");\n        if (depthBufferType === DepthType.Log) {\n            e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        }\n        if (ortho) {\n            e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        }\n        if (this.configuration.halfRes) {\n            e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        }\n        if (this.effectShaderQuad) {\n            this.effectShaderQuad.material.dispose();\n            this.effectShaderQuad.material = new THREE.ShaderMaterial(e);\n        } else {\n            this.effectShaderQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n        }\n    }\n    configureDenoisePass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samplesDenoise = this.generateDenoiseSamples(this.configuration.denoiseSamples, 11);\n        const p = {...PoissionBlur };\n        p.fragmentShader = p.fragmentShader.replace(\"16\", this.configuration.denoiseSamples);\n        if (depthBufferType === DepthType.Log) {\n            p.fragmentShader = \"#define LOGDEPTH\\n\" + p.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            p.fragmentShader = \"#define REVERSEDEPTH\\n\" + p.fragmentShader;\n        }\n        if (ortho) {\n            p.fragmentShader = \"#define ORTHO\\n\" + p.fragmentShader;\n        }\n        if (this.poissonBlurQuad) {\n            this.poissonBlurQuad.material.dispose();\n            this.poissonBlurQuad.material = new THREE.ShaderMaterial(p);\n        } else {\n            this.poissonBlurQuad = new FullScreenTriangle(new THREE.ShaderMaterial(p));\n        }\n    }\n    configureEffectCompositer(depthBufferType = DepthType.Default, ortho = false) {\n            this.firstFrame();\n            const e = {...EffectCompositer };\n            if (depthBufferType === DepthType.Log) {\n                e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n            } else if (depthBufferType === DepthType.Reverse) {\n                e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            }\n            if (ortho) {\n                e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n            }\n            if (this.configuration.halfRes && this.configuration.depthAwareUpsampling) {\n                e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n            }\n            if (this.effectCompositerQuad) {\n                this.effectCompositerQuad.material.dispose();\n                this.effectCompositerQuad.material = new THREE.ShaderMaterial(e);\n            } else {\n                this.effectCompositerQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n            }\n        }\n        /**\n         * \n         * @param {Number} n \n         * @returns {THREE.Vector3[]}\n         */\n    generateHemisphereSamples(n) {\n            const points = [];\n            for (let k = 0; k < n; k++) {\n                const theta = 2.399963 * k;\n                let r = (Math.sqrt(k + 0.5) / Math.sqrt(n));\n                const x = r * Math.cos(theta);\n                const y = r * Math.sin(theta);\n                // Project to hemisphere\n                const z = Math.sqrt(1 - (x * x + y * y));\n                points.push(new THREE.Vector3(x, y, z));\n\n            }\n            return points;\n        }\n        /**\n         * \n         * @param {number} numSamples \n         * @param {number} numRings \n         * @returns {THREE.Vector2[]}\n         */\n    generateDenoiseSamples(numSamples, numRings) {\n        const angleStep = 2 * Math.PI * numRings / numSamples;\n        const invNumSamples = 1.0 / numSamples;\n        const radiusStep = invNumSamples;\n        const samples = [];\n        let radius = invNumSamples;\n        let angle = 0;\n        for (let i = 0; i < numSamples; i++) {\n            samples.push(new THREE.Vector2(Math.cos(angle), Math.sin(angle)).multiplyScalar(Math.pow(radius, 0.75)));\n            radius += radiusStep;\n            angle += angleStep;\n        }\n        return samples;\n    }\n    setSize(width, height) {\n        this.firstFrame();\n        this.width = width;\n        this.height = height;\n        const c = this.configuration.halfRes ? 0.5 : 1;\n        this.beautyRenderTarget.setSize(width, height);\n        this.writeTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.readTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.accumulationRenderTarget.setSize(width * c, height * c);\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget.setSize(width * c, height * c);\n        }\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse.setSize(width, height);\n            this.transparencyRenderTargetDWTrue.setSize(width, height);\n        }\n    }\n    firstFrame() {\n        this.needsFrame = true;\n    }\n\n    render(renderer, writeBuffer, readBuffer, deltaTime, maskActive) {\n            if (renderer.capabilities.logarithmicDepthBuffer && this.configuration.depthBufferType !== DepthType.Log || renderer.capabilities.reverseDepthBuffer && this.configuration.depthBufferType !== DepthType.Reverse) {\n                this.configuration.depthBufferType = renderer.capabilities.logarithmicDepthBuffer ? DepthType.Log : renderer.capabilities.reverseDepthBuffer ? DepthType.Reverse : DepthType.Default;\n                this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            }\n            this.detectTransparency();\n            this.camera.updateMatrixWorld();\n            if (this.lastViewMatrix.equals(this.camera.matrixWorldInverse) && this.lastProjectionMatrix.equals(this.camera.projectionMatrix) && this.configuration.accumulate && !this.needsFrame) {\n                this.frame++;\n            } else {\n                if (this.configuration.accumulate) {\n                    renderer.setRenderTarget(this.accumulationRenderTarget);\n                    renderer.clear(true, true, true);\n                }\n                this.frame = 0;\n                this.needsFrame = false;\n            }\n            this.lastViewMatrix.copy(this.camera.matrixWorldInverse);\n            this.lastProjectionMatrix.copy(this.camera.projectionMatrix);\n            let gl;\n            let ext;\n            let timerQuery;\n            if (this.debugMode) {\n                gl = renderer.getContext();\n                ext = gl.getExtension('EXT_disjoint_timer_query_webgl2');\n                if (ext === null) {\n                    console.error(\"EXT_disjoint_timer_query_webgl2 not available, disabling debug mode.\");\n                    this.debugMode = false;\n                }\n            }\n            if (this.configuration.autoRenderBeauty) {\n                renderer.setRenderTarget(this.beautyRenderTarget);\n                renderer.render(this.scene, this.camera);\n                if (this.configuration.transparencyAware) {\n                    this.renderTransparency(renderer);\n                }\n            }\n            if (this.debugMode) {\n                timerQuery = gl.createQuery();\n                gl.beginQuery(ext.TIME_ELAPSED_EXT, timerQuery);\n            }\n            const xrEnabled = renderer.xr.enabled;\n            renderer.xr.enabled = false;\n\n            this._r.set(this.width, this.height);\n            let trueRadius = this.configuration.aoRadius;\n            if (this.configuration.halfRes && this.configuration.screenSpaceRadius) {\n                trueRadius *= 0.5;\n            }\n            if (this.frame < 1024 / this.configuration.aoSamples) {\n                if (this.configuration.halfRes) {\n\n                    renderer.setRenderTarget(this.depthDownsampleTarget);\n                    this.depthDownsampleQuad.material.uniforms.sceneDepth.value = this.beautyRenderTarget.depthTexture;\n                    this.depthDownsampleQuad.material.uniforms.resolution.value = this._r;\n                    this.depthDownsampleQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.depthDownsampleQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.depthDownsampleQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.depthDownsampleQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.depthDownsampleQuad.material.uniforms[\"logDepth\"].value = this.configuration.depthBufferType === DepthType.Log;\n                    this.depthDownsampleQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                    this.depthDownsampleQuad.render(renderer);\n                }\n                this.effectShaderQuad.material.uniforms[\"sceneDiffuse\"].value = this.beautyRenderTarget.texture;\n                this.effectShaderQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n                this.effectShaderQuad.material.uniforms[\"sceneNormal\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[1] : null;\n                this.effectShaderQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                this.effectShaderQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                this.effectShaderQuad.material.uniforms[\"projViewMat\"].value = this.camera.projectionMatrix.clone().multiply(this.camera.matrixWorldInverse.clone());\n                this.effectShaderQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.effectShaderQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.effectShaderQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                this.effectShaderQuad.material.uniforms['biasAdjustment'].value = new THREE.Vector2(this.configuration.biasOffset, this.configuration.biasMultiplier);\n                this.effectShaderQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                this.effectShaderQuad.material.uniforms['time'].value = performance.now() / 1000;\n                this.effectShaderQuad.material.uniforms['samples'].value = this.samples;\n                this.effectShaderQuad.material.uniforms['bluenoise'].value = this.bluenoise;\n                this.effectShaderQuad.material.uniforms['radius'].value = trueRadius;\n                this.effectShaderQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                this.effectShaderQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.effectShaderQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.effectShaderQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                this.effectShaderQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                this.effectShaderQuad.material.uniforms[\"frame\"].value = this.frame;\n                // Start the AO\n                renderer.setRenderTarget(this.writeTargetInternal);\n                this.effectShaderQuad.render(renderer);\n                // End the AO\n                // Start the blur\n                for (let i = 0; i < this.configuration.denoiseIterations; i++) {\n                    [this.writeTargetInternal, this.readTargetInternal] = [this.readTargetInternal, this.writeTargetInternal];\n                    this.poissonBlurQuad.material.uniforms[\"tDiffuse\"].value = this.readTargetInternal.texture;\n                    this.poissonBlurQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n                    this.poissonBlurQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                    this.poissonBlurQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                    this.poissonBlurQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.poissonBlurQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.poissonBlurQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                    this.poissonBlurQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                    this.poissonBlurQuad.material.uniforms['time'].value = performance.now() / 1000;\n                    this.poissonBlurQuad.material.uniforms['blueNoise'].value = this.bluenoise;\n                    this.poissonBlurQuad.material.uniforms['radius'].value = this.configuration.denoiseRadius * (\n                        this.configuration.halfRes ? 1 / 2 : 1\n                    );\n                    this.poissonBlurQuad.material.uniforms['worldRadius'].value = trueRadius;\n                    this.poissonBlurQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                    this.poissonBlurQuad.material.uniforms['index'].value = i;\n                    this.poissonBlurQuad.material.uniforms['poissonDisk'].value = this.samplesDenoise;\n                    this.poissonBlurQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.poissonBlurQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.poissonBlurQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                    renderer.setRenderTarget(this.writeTargetInternal);\n                    this.poissonBlurQuad.render(renderer);\n\n                }\n                renderer.setRenderTarget(this.accumulationRenderTarget);\n                const oldAutoClear = renderer.autoClear;\n                renderer.autoClear = false;\n                this.accumulationQuad.material.uniforms[\"tDiffuse\"].value = this.writeTargetInternal.texture;\n                this.accumulationQuad.material.uniforms[\"frame\"].value = this.frame;\n                this.accumulationQuad.render(renderer);\n                renderer.autoClear = oldAutoClear;\n            }\n            // Now, we have the blurred AO in writeTargetInternal\n            // End the blur\n            // Start the composition\n            if (this.configuration.transparencyAware) {\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWFalse\"].value = this.transparencyRenderTargetDWFalse.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrue\"].value = this.transparencyRenderTargetDWTrue.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrueDepth\"].value = this.transparencyRenderTargetDWTrue.depthTexture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyAware\"].value = true;\n            }\n            this.effectCompositerQuad.material.uniforms[\"sceneDiffuse\"].value = this.beautyRenderTarget.texture;\n            this.effectCompositerQuad.material.uniforms[\"sceneDepth\"].value = this.beautyRenderTarget.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"aoTones\"].value = this.configuration.aoTones;\n            this.effectCompositerQuad.material.uniforms[\"near\"].value = this.camera.near;\n            this.effectCompositerQuad.material.uniforms[\"far\"].value = this.camera.far;\n            this.effectCompositerQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n            this.effectCompositerQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n            this.effectCompositerQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n            this.effectCompositerQuad.material.uniforms[\"downsampledDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.beautyRenderTarget.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"resolution\"].value = this._r;\n            this.effectCompositerQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n            this.effectCompositerQuad.material.uniforms[\"intensity\"].value = this.configuration.intensity;\n            this.effectCompositerQuad.material.uniforms[\"renderMode\"].value = this.configuration.renderMode;\n            this.effectCompositerQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n            this.effectCompositerQuad.material.uniforms['radius'].value = trueRadius;\n            this.effectCompositerQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n            this.effectCompositerQuad.material.uniforms[\"gammaCorrection\"].value = this.configuration.gammaCorrection;\n            this.effectCompositerQuad.material.uniforms[\"tDiffuse\"].value = this.accumulationRenderTarget.texture;\n            this.effectCompositerQuad.material.uniforms[\"color\"].value = this._c.copy(\n                this.configuration.color\n            ).convertSRGBToLinear();\n            this.effectCompositerQuad.material.uniforms[\"colorMultiply\"].value = this.configuration.colorMultiply;\n            this.effectCompositerQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n            this.effectCompositerQuad.material.uniforms[\"fog\"].value = !!this.scene.fog;\n            if (this.scene.fog) {\n                if (\n                    this.scene.fog.isFog\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = false;\n                    this.effectCompositerQuad.material.uniforms[\"fogNear\"].value = this.scene.fog.near;\n                    this.effectCompositerQuad.material.uniforms[\"fogFar\"].value = this.scene.fog.far;\n                } else if (\n                    this.scene.fog.isFogExp2\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = true;\n                    this.effectCompositerQuad.material.uniforms[\"fogDensity\"].value = this.scene.fog.density;\n                } else {\n                    console.error(`Unsupported fog type ${this.scene.fog.constructor.name} in SSAOPass.`);\n                }\n\n\n            }\n            renderer.setRenderTarget(\n                this.renderToScreen ? null :\n                writeBuffer\n            );\n            this.effectCompositerQuad.render(renderer);\n            if (this.debugMode) {\n                gl.endQuery(ext.TIME_ELAPSED_EXT);\n                checkTimerQuery(timerQuery, gl, this);\n            }\n\n            renderer.xr.enabled = xrEnabled;\n        }\n        /**\n         * Enables the debug mode of the AO, meaning the lastTime value will be updated.\n         */\n    enableDebugMode() {\n            this.debugMode = true;\n        }\n        /**\n         * Disables the debug mode of the AO, meaning the lastTime value will not be updated.\n         */\n    disableDebugMode() {\n            this.debugMode = false;\n        }\n        /**\n         * Sets the display mode of the AO\n         * @param {\"Combined\" | \"AO\" | \"No AO\" | \"Split\" | \"Split AO\"} mode - The display mode. \n         */\n    setDisplayMode(mode) {\n            this.configuration.renderMode = [\"Combined\", \"AO\", \"No AO\", \"Split\", \"Split AO\"].indexOf(mode);\n        }\n        /**\n         * \n         * @param {\"Performance\" | \"Low\" | \"Medium\" | \"High\" | \"Ultra\"} mode \n         */\n    setQualityMode(mode) {\n        if (mode === \"Performance\") {\n            this.configuration.aoSamples = 8;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Low\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Medium\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"High\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 6;\n        } else if (mode === \"Ultra\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 16;\n            this.configuration.denoiseRadius = 6;\n        }\n\n    }\n}\nexport { N8AOPass, N8AOPostPass };", "import * as THREE from 'three';\n\nclass FullScreenTriangleGeometry extends THREE.BufferGeometry {\n  boundingSphere = new THREE.Sphere();\n\n  constructor() {\n    super()\n    this.setAttribute('position', new THREE.BufferAttribute(new Float32Array([-1, -1, 3, -1, -1, 3]), 2));\n    this.setAttribute('uv', new THREE.BufferAttribute(new Float32Array([0, 0, 2, 0, 0, 2]), 2));\n  }\n\n  computeBoundingSphere() {}\n}\n\nconst _geometry = /* @__PURE__ */ new FullScreenTriangleGeometry();\nconst _camera = /* @__PURE__ */ new THREE.OrthographicCamera();\n\nexport class FullScreenTriangle {\n  constructor(material) {\n    this._mesh = new THREE.Mesh(_geometry, material);\n    this._mesh.frustumCulled = false;\n  }\n\n  render(renderer) {\n    renderer.render(this._mesh, _camera);\n  }\n\n  get material() {\n    return this._mesh.material;\n  }\n\n  set material(value) {\n    this._mesh.material = value;\n  }\n\n  dispose() {\n    this._mesh.material.dispose();\n    this._mesh.geometry.dispose();\n  }\n}\n", "import * as THREE from 'three';\nconst EffectShader = {\n\n    uniforms: {\n\n        'sceneDiffuse': { value: null },\n        'sceneDepth': { value: null },\n        'sceneNormal': { value: null },\n        'projMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projViewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'cameraPos': { value: /* @__PURE__ */ new THREE.Vector3() },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'biasAdjustment': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'time': { value: 0.0 },\n        'samples': { value: [] },\n        'bluenoise': { value: null },\n        'distanceFalloff': { value: 1.0 },\n        'radius': { value: 5.0 },\n        'near': { value: 0.1 },\n        'far': { value: 1000.0 },\n        'ortho': { value: false },\n        'screenSpaceRadius': { value: false },\n        'frame': { value: 0.0 }\n    },\n    depthWrite: false,\n    depthTest: false,\n    vertexShader: /* glsl */ `\nvarying vec2 vUv;\nvoid main() {\n  vUv = uv;\n  gl_Position = vec4(position, 1);\n}`,\n\n    fragmentShader: /* glsl */ `\n    #define SAMPLES 16\n    #define FSAMPLES 16.0\nuniform sampler2D sceneDiffuse;\nuniform highp sampler2D sceneNormal;\nuniform highp sampler2D sceneDepth;\nuniform mat4 projectionMatrixInv;\nuniform mat4 viewMatrixInv;\nuniform mat4 projMat;\nuniform mat4 viewMat;\nuniform mat4 projViewMat;\nuniform vec3 cameraPos;\nuniform vec2 resolution;\nuniform vec2 biasAdjustment;\nuniform float time;\nuniform vec3[SAMPLES] samples;\nuniform float radius;\nuniform float distanceFalloff;\nuniform float near;\nuniform float far;\nuniform float frame;\nuniform bool ortho;\nuniform bool screenSpaceRadius;\nuniform sampler2D bluenoise;\n    varying vec2 vUv;\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        return (zFar * zNear) / (zFar - d * (zFar - zNear));\n    }\n    highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n      return nearZ + (farZ - nearZ) * d;\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n      float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      /*return ortho ? linearize_depth_ortho(\n        linDepth,\n        nearZ,\n        farZ\n      ) :linearize_depth(linDepth, nearZ, farZ);*/\n       #ifdef ORTHO\n\n       return linearize_depth_ortho(d, nearZ, farZ);\n\n        #else\n        return linearize_depth(linDepth, nearZ, farZ);\n        #endif\n    }\n\n    vec3 getWorldPosLog(vec3 posS) {\n      vec2 uv = posS.xy;\n      float z = posS.z;\n      float nearZ =near;\n      float farZ = far;\n      float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n      vec4 wpos = projectionMatrixInv * clipVec;\n      return wpos.xyz / wpos.w;\n    }\n    vec3 getWorldPos(float depth, vec2 coord) {\n      #ifdef LOGDEPTH\n        #ifndef ORTHO\n          return getWorldPosLog(vec3(coord, depth));\n        #endif\n      #endif\n      float z = depth * 2.0 - 1.0;\n      vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n      vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n      // Perspective division\n     vec4 worldSpacePosition = viewSpacePosition;\n     worldSpacePosition.xyz /= worldSpacePosition.w;\n      return worldSpacePosition.xyz;\n  }\n\n  vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n    ivec2 p = ivec2(vUv * resolution);\n    #ifdef REVERSEDEPTH\n    float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n    float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n    float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n    float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n    float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n    float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n    float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n    float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n    float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n    #else\n    float c0 = texelFetch(sceneDepth, p, 0).x;\n    float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n    float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n    float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n    float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n    float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n    float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n    float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n    float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n    #endif\n\n    float dl = abs((2.0 * l1 - l2) - c0);\n    float dr = abs((2.0 * r1 - r2) - c0);\n    float db = abs((2.0 * b1 - b2) - c0);\n    float dt = abs((2.0 * t1 - t2) - c0);\n\n    vec3 ce = getWorldPos(c0, vUv).xyz;\n\n    vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                          : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n    vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                          : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n\n    return normalize(cross(dpdx, dpdy));\n}\n\nmat3 makeRotationZ(float theta) {\n\tfloat c = cos(theta);\n\tfloat s = sin(theta);\n\treturn mat3(c, - s, 0,\n\t\t\ts,  c, 0,\n\t\t\t0,  0, 1);\n  }\n\nvoid main() {\n      vec4 diffuse = texture2D(sceneDiffuse, vUv);\n      #ifdef REVERSEDEPTH\n      float depth = 1.0 - texture2D(sceneDepth, vUv).x;\n      #else\n      float depth = texture2D(sceneDepth, vUv).x;\n      #endif\n      if (depth == 1.0) {\n        gl_FragColor = vec4(vec3(1.0), 1.0);\n        return;\n      }\n      vec3 worldPos = getWorldPos(depth, vUv);\n      #ifdef HALFRES\n        vec3 normal = texture2D(sceneNormal, vUv).rgb;\n      #else\n        vec3 normal = computeNormal(worldPos, vUv);\n      #endif\n      vec4 noise = texture2D(bluenoise, gl_FragCoord.xy / 128.0);\n      vec2 harmoniousNumbers = vec2(\n        1.618033988749895,\n        1.324717957244746\n      );\n      noise.rg += harmoniousNumbers * frame;\n      noise.rg = fract(noise.rg);\n        vec3 helperVec = vec3(0.0, 1.0, 0.0);\n        if (dot(helperVec, normal) > 0.99) {\n          helperVec = vec3(1.0, 0.0, 0.0);\n        }\n        vec3 tangent = normalize(cross(helperVec, normal));\n        vec3 bitangent = cross(normal, tangent);\n        mediump mat3 tbn = mat3(tangent, bitangent, normal) *  makeRotationZ( noise.r * 3.1415962 * 2.0) ;\n\n      mediump float occluded = 0.0;\n      mediump float totalWeight = 0.0;\n      float radiusToUse = screenSpaceRadius ? distance(\n        worldPos,\n        getWorldPos(depth, vUv +\n          vec2(radius, 0.0) / resolution)\n      ) : radius;\n      float distanceFalloffToUse =screenSpaceRadius ?\n          radiusToUse * distanceFalloff\n      : radiusToUse * distanceFalloff * 0.2;\n      float bias = (min(\n        0.1,\n        distanceFalloffToUse * 0.1\n      ) / near) * fwidth(distance(worldPos, cameraPos)) / radiusToUse;\n      bias = biasAdjustment.x + biasAdjustment.y * bias;\n      mediump float offsetMove = noise.g;\n      mediump float offsetMoveInv = 1.0 / FSAMPLES;\n      float farTimesNear = far * near;\n      float farMinusNear = far - near;\n      \n      for(int i = 0; i < SAMPLES; i++) {\n        mediump vec3 sampleDirection = tbn * samples[i];\n\n        float moveAmt = fract(offsetMove);\n        offsetMove += offsetMoveInv;\n        vec3 samplePos = worldPos + radiusToUse * moveAmt * sampleDirection;\n        vec4 offset = projMat * vec4(samplePos, 1.0);\n        offset.xyz /= offset.w;\n        offset.xyz = offset.xyz * 0.5 + 0.5;\n        \n        if (all(greaterThan(offset.xyz * (1.0 - offset.xyz), vec3(0.0)))) {\n          #ifdef REVERSEDEPTH\n          float sampleDepth = 1.0 - textureLod(sceneDepth, offset.xy, 0.0).x;\n          #else\n          float sampleDepth = textureLod(sceneDepth, offset.xy, 0.0).x;\n          #endif\n\n          /*#ifdef LOGDEPTH\n          float distSample = linearize_depth_log(sampleDepth, near, far);\n      #else\n          #ifdef ORTHO\n              float distSample = near + farMinusNear * sampleDepth;\n          #else\n              float distSample = (farTimesNear) / (far - sampleDepth * farMinusNear);\n          #endif\n      #endif*/\n      #ifdef ORTHO\n          float distSample = near + sampleDepth * farMinusNear;\n      #else\n          #ifdef LOGDEPTH\n              float distSample = linearize_depth_log(sampleDepth, near, far);\n          #else\n              float distSample = (farTimesNear) / (far - sampleDepth * farMinusNear);\n          #endif\n      #endif\n      \n      #ifdef ORTHO\n          float distWorld = near + offset.z * farMinusNear;\n      #else\n          float distWorld = (farTimesNear) / (far - offset.z * farMinusNear);\n      #endif\n          \n          mediump float rangeCheck = smoothstep(0.0, 1.0, distanceFalloffToUse / (abs(distSample - distWorld)));\n          vec2 diff = gl_FragCoord.xy - floor(offset.xy * resolution);\n          occluded += rangeCheck * float(distSample != distWorld) * float(sampleDepth != depth) * step(distSample + bias, distWorld) * step(\n            1.0,\n            dot(diff, diff)\n          );\n          \n          totalWeight ++;\n        }\n      }\n      float occ = clamp(1.0 - occluded / (totalWeight == 0.0 ? 1.0 : totalWeight), 0.0, 1.0);\n      gl_FragColor = vec4(occ, 0.5 + 0.5 * normal);\n}`\n\n\n};\n\nexport { EffectShader };", "import * as THREE from 'three';\nconst EffectCompositer = {\n    uniforms: {\n\n        'sceneDiffuse': { value: null },\n        'sceneDepth': { value: null },\n        'tDiffuse': { value: null },\n        'transparencyDWFalse': { value: null },\n        'transparencyDWTrue': { value: null },\n        'transparencyDWTrueDepth': { value: null },\n        'transparencyAware': { value: false },\n        'projMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'cameraPos': { value: /* @__PURE__ */ new THREE.Vector3() },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'color': { value: /* @__PURE__ */ new THREE.Vector3(0, 0, 0) },\n        'blueNoise': { value: null },\n        'downsampledDepth': { value: null },\n        'time': { value: 0.0 },\n        'intensity': { value: 10.0 },\n        'renderMode': { value: 0.0 },\n        \"gammaCorrection\": { value: false },\n        \"ortho\": { value: false },\n        \"near\": { value: 0.1 },\n        \"far\": { value: 1000.0 },\n        \"screenSpaceRadius\": { value: false },\n        \"radius\": { value: 0.0 },\n        \"distanceFalloff\": { value: 1.0 },\n        'fog': { value: false },\n        'fogExp': { value: false },\n        'fogDensity': { value: 0.0 },\n        'fogNear': { value: Infinity },\n        'fogFar': { value: Infinity },\n        'colorMultiply': { value: true },\n        'aoTones': { value: 0.0 }\n\n    },\n    depthWrite: false,\n    depthTest: false,\n\n    vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = vec4(position, 1);\n\t\t}`,\n    fragmentShader: /* glsl */ `\n\t\tuniform sampler2D sceneDiffuse;\n    uniform highp sampler2D sceneDepth;\n    uniform highp sampler2D downsampledDepth;\n    uniform highp sampler2D transparencyDWFalse;\n    uniform highp sampler2D transparencyDWTrue;\n    uniform highp sampler2D transparencyDWTrueDepth;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D blueNoise;\n    uniform vec2 resolution;\n    uniform vec3 color;\n    uniform mat4 projectionMatrixInv;\n    uniform mat4 viewMatrixInv;\n    uniform float intensity;\n    uniform float renderMode;\n    uniform float near;\n    uniform float far;\n    uniform float aoTones;\n    uniform bool gammaCorrection;\n    uniform bool ortho;\n    uniform bool screenSpaceRadius;\n    uniform bool fog;\n    uniform bool fogExp;\n    uniform bool colorMultiply;\n    uniform bool transparencyAware;\n    uniform float fogDensity;\n    uniform float fogNear;\n    uniform float fogFar;\n    uniform float radius;\n    uniform float distanceFalloff;\n    uniform vec3 cameraPos;\n    varying vec2 vUv;\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        return (zFar * zNear) / (zFar - d * (zFar - zNear));\n    }\n    highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n      return nearZ + (farZ - nearZ) * d;\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n      float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n      float a = farZ / (farZ - nearZ);\n      float b = farZ * nearZ / (nearZ - farZ);\n      float linDepth = a + b / depth;\n      return ortho ? linearize_depth_ortho(\n        linDepth,\n        nearZ,\n        farZ\n      ) :linearize_depth(linDepth, nearZ, farZ);\n    }\n    vec3 getWorldPosLog(vec3 posS) {\n        vec2 uv = posS.xy;\n        float z = posS.z;\n        float nearZ =near;\n        float farZ = far;\n        float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n        float a = farZ / (farZ - nearZ);\n        float b = farZ * nearZ / (nearZ - farZ);\n        float linDepth = a + b / depth;\n        vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n        vec4 wpos = projectionMatrixInv * clipVec;\n        return wpos.xyz / wpos.w;\n      }\n      vec3 getWorldPos(float depth, vec2 coord) {\n        #ifdef LOGDEPTH\n          #ifndef ORTHO\n            return getWorldPosLog(vec3(coord, depth));\n          #endif\n        #endif\n      //  }\n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n  \n    vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n      ivec2 p = ivec2(vUv * resolution);\n      #ifdef REVERSEDEPTH\n      float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n      float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #else\n      float c0 = texelFetch(sceneDepth, p, 0).x;\n      float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #endif\n  \n      float dl = abs((2.0 * l1 - l2) - c0);\n      float dr = abs((2.0 * r1 - r2) - c0);\n      float db = abs((2.0 * b1 - b2) - c0);\n      float dt = abs((2.0 * t1 - t2) - c0);\n  \n      vec3 ce = getWorldPos(c0, vUv).xyz;\n  \n      vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                            : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n      vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                            : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n  \n      return normalize(cross(dpdx, dpdy));\n  }\n\n    #include <common>\n    #include <dithering_pars_fragment>\n    void main() {\n        //vec4 texel = texture2D(tDiffuse, vUv);//vec3(0.0);\n        vec4 sceneTexel = texture2D(sceneDiffuse, vUv);\n        #ifdef REVERSEDEPTH\n        float depth = 1.0 - texture2D(sceneDepth, vUv).x;\n        #else\n        float depth = texture2D(sceneDepth, vUv).x;\n        #endif\n        #ifdef HALFRES \n        vec4 texel;\n        if (depth == 1.0) {\n            texel = vec4(0.0, 0.0, 0.0, 1.0);\n        } else {\n        vec3 worldPos = getWorldPos(depth, vUv);\n        vec3 normal = computeNormal(getWorldPos(depth, vUv), vUv);\n       // vec4 texel = texture2D(tDiffuse, vUv);\n       // Find closest depth;\n       float totalWeight = 0.0;\n       float radiusToUse = screenSpaceRadius ? distance(\n        worldPos,\n        getWorldPos(depth, vUv +\n          vec2(radius, 0.0) / resolution)\n      ) : radius;\n      float distanceFalloffToUse =screenSpaceRadius ?\n          radiusToUse * distanceFalloff\n      : distanceFalloff;\n        for(float x = -1.0; x <= 1.0; x++) {\n            for(float y = -1.0; y <= 1.0; y++) {\n                vec2 offset = vec2(x, y);\n                ivec2 p = ivec2(\n                    (vUv * resolution * 0.5) + offset\n                );\n                vec2 pUv = vec2(p) / (resolution * 0.5);\n                float sampleDepth = texelFetch(downsampledDepth,p, 0).x;\n                vec4 sampleInfo = texelFetch(tDiffuse, p, 0);\n                vec3 normalSample = sampleInfo.gba * 2.0 - 1.0;\n                vec3 worldPosSample = getWorldPos(sampleDepth, pUv);\n                float tangentPlaneDist = abs(dot(worldPosSample - worldPos, normal));\n                float rangeCheck = exp(-1.0 * tangentPlaneDist * (1.0 / distanceFalloffToUse)) * max(dot(normal, normalSample), 0.0);\n                float weight = rangeCheck;\n                totalWeight += weight;\n                texel += sampleInfo * weight;\n            }\n        }\n        if (totalWeight == 0.0) {\n            texel = texture2D(tDiffuse, vUv);\n        } else {\n            texel /= totalWeight;\n        }\n    }\n        #else\n        vec4 texel = texture2D(tDiffuse, vUv);\n        #endif\n\n        #ifdef LOGDEPTH\n        texel.r = clamp(texel.r, 0.0, 1.0);\n        if (texel.r == 0.0) {\n          texel.r = 1.0;\n        }\n        #endif\n     \n        float finalAo = pow(texel.r, intensity);\n        if (aoTones > 0.0) {\n            finalAo = ceil(finalAo * aoTones) / aoTones;\n        }\n        float fogFactor;\n        float fogDepth = distance(\n            cameraPos,\n            getWorldPos(depth, vUv)\n        );\n        if (fog) {\n            if (fogExp) {\n                fogFactor = 1.0 - exp( - fogDensity * fogDensity * fogDepth * fogDepth );\n            } else {\n                fogFactor = smoothstep( fogNear, fogFar, fogDepth );\n            }\n        }\n        if (transparencyAware) {\n            float transparencyDWOff = texture2D(transparencyDWFalse, vUv).a;\n            float transparencyDWOn = texture2D(transparencyDWTrue, vUv).a;\n            float adjustmentFactorOff = transparencyDWOff;\n            #ifdef REVERSEDEPTH\n            float depthSample = 1.0 - texture2D(sceneDepth, vUv).r;\n            float trueDepthSample = 1.0 - texture2D(transparencyDWTrueDepth, vUv).r;\n            #else\n            float depthSample = texture2D(sceneDepth, vUv).r;\n            float trueDepthSample = texture2D(transparencyDWTrueDepth, vUv).r;\n            #endif\n            float adjustmentFactorOn = (1.0 - transparencyDWOn) * (\n                trueDepthSample == depthSample ? 1.0 : 0.0\n            );\n            float adjustmentFactor = max(adjustmentFactorOff, adjustmentFactorOn);\n            finalAo = mix(finalAo, 1.0, adjustmentFactor);\n        }\n        finalAo = mix(finalAo, 1.0, fogFactor);\n        vec3 aoApplied = color * mix(vec3(1.0), sceneTexel.rgb, float(colorMultiply));\n        if (renderMode == 0.0) {\n            gl_FragColor = vec4( mix(sceneTexel.rgb, aoApplied, 1.0 - finalAo), sceneTexel.a);\n        } else if (renderMode == 1.0) {\n            gl_FragColor = vec4( mix(vec3(1.0), aoApplied, 1.0 - finalAo), sceneTexel.a);\n        } else if (renderMode == 2.0) {\n            gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n        } else if (renderMode == 3.0) {\n            if (vUv.x < 0.5) {\n                gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n            } else if (abs(vUv.x - 0.5) < 1.0 / resolution.x) {\n                gl_FragColor = vec4(1.0);\n            } else {\n                gl_FragColor = vec4( mix(sceneTexel.rgb, aoApplied, 1.0 - finalAo), sceneTexel.a);\n            }\n        } else if (renderMode == 4.0) {\n            if (vUv.x < 0.5) {\n                gl_FragColor = vec4( sceneTexel.rgb, sceneTexel.a);\n            } else if (abs(vUv.x - 0.5) < 1.0 / resolution.x) {\n                gl_FragColor = vec4(1.0);\n            } else {\n                gl_FragColor = vec4( mix(vec3(1.0), aoApplied, 1.0 - finalAo), sceneTexel.a);\n            }\n        }\n        #include <dithering_fragment>\n        if (gammaCorrection) {\n            gl_FragColor = sRGBTransferOETF(gl_FragColor);\n        }\n    }\n    `\n\n}\nexport { EffectCompositer };", "import * as THREE from 'three';\nconst PoissionBlur = {\n    uniforms: {\n\n        'sceneDiffuse': { value: null },\n        'sceneDepth': { value: null },\n        'tDiffuse': { value: null },\n        'projMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMat': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'cameraPos': { value: /* @__PURE__ */ new THREE.Vector3() },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'time': { value: 0.0 },\n        'r': { value: 5.0 },\n        'blueNoise': { value: null },\n        'radius': { value: 12.0 },\n        'worldRadius': { value: 5.0 },\n        'index': { value: 0.0 },\n        \"poissonDisk\": { value: [] },\n        \"distanceFalloff\": { value: 1.0 },\n        'near': { value: 0.1 },\n        'far': { value: 1000.0 },\n        'screenSpaceRadius': { value: false }\n    },\n    depthWrite: false,\n    depthTest: false,\n\n    vertexShader: /* glsl */ `\n\t\tvarying vec2 vUv;\n\t\tvoid main() {\n\t\t\tvUv = uv;\n\t\t\tgl_Position = vec4(position, 1.0);\n\t\t}`,\n    fragmentShader: /* glsl */ `\n\t\tuniform sampler2D sceneDiffuse;\n    uniform highp sampler2D sceneDepth;\n    uniform sampler2D tDiffuse;\n    uniform sampler2D blueNoise;\n    uniform mat4 projectionMatrixInv;\n    uniform mat4 viewMatrixInv;\n    uniform vec2 resolution;\n    uniform float r;\n    uniform float radius;\n     uniform float worldRadius;\n    uniform float index;\n     uniform float near;\n     uniform float far;\n     uniform float distanceFalloff;\n     uniform bool screenSpaceRadius;\n    varying vec2 vUv;\n\n    highp float linearize_depth(highp float d, highp float zNear,highp float zFar)\n    {\n        highp float z_n = 2.0 * d - 1.0;\n        return 2.0 * zNear * zFar / (zFar + zNear - z_n * (zFar - zNear));\n    }\n    highp float linearize_depth_log(highp float d, highp float nearZ,highp float farZ) {\n     float depth = pow(2.0, d * log2(farZ + 1.0)) - 1.0;\n     float a = farZ / (farZ - nearZ);\n     float b = farZ * nearZ / (nearZ - farZ);\n     float linDepth = a + b / depth;\n     return linearize_depth(linDepth, nearZ, farZ);\n   }\n   highp float linearize_depth_ortho(highp float d, highp float nearZ, highp float farZ) {\n     return nearZ + (farZ - nearZ) * d;\n   }\n   vec3 getWorldPosLog(vec3 posS) {\n     vec2 uv = posS.xy;\n     float z = posS.z;\n     float nearZ =near;\n     float farZ = far;\n     float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n     float a = farZ / (farZ - nearZ);\n     float b = farZ * nearZ / (nearZ - farZ);\n     float linDepth = a + b / depth;\n     vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n     vec4 wpos = projectionMatrixInv * clipVec;\n     return wpos.xyz / wpos.w;\n   }\n    vec3 getWorldPos(float depth, vec2 coord) {\n     #ifdef LOGDEPTH\n      #ifndef ORTHO\n          return getWorldPosLog(vec3(coord, depth));\n      #endif\n     #endif\n        \n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n    #include <common>\n    #define NUM_SAMPLES 16\n    uniform vec2 poissonDisk[NUM_SAMPLES];\n    void main() {\n        const float pi = 3.14159;\n        vec2 texelSize = vec2(1.0 / resolution.x, 1.0 / resolution.y);\n        vec2 uv = vUv;\n        vec4 data = texture2D(tDiffuse, vUv);\n        float occlusion = data.r;\n        float baseOcc = data.r;\n        vec3 normal = data.gba * 2.0 - 1.0;\n        float count = 1.0;\n        float d = texture2D(sceneDepth, vUv).x;\n        if (d == 1.0) {\n          gl_FragColor = data;\n          return;\n        }\n        vec3 worldPos = getWorldPos(d, vUv);\n        float size = radius;\n        float angle;\n        if (index == 0.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).w * PI2;\n        } else if (index == 1.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).z * PI2;\n        } else if (index == 2.0) {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).y * PI2;\n        } else {\n             angle = texture2D(blueNoise, gl_FragCoord.xy / 128.0).x * PI2;\n        }\n\n        mat2 rotationMatrix = mat2(cos(angle), -sin(angle), sin(angle), cos(angle));\n        float radiusToUse = screenSpaceRadius ? distance(\n          worldPos,\n          getWorldPos(d, vUv +\n            vec2(worldRadius, 0.0) / resolution)\n        ) : worldRadius;\n        float distanceFalloffToUse =screenSpaceRadius ?\n        radiusToUse * distanceFalloff\n    : radiusToUse * distanceFalloff * 0.2;\n\n        float invDistance = (1.0 / distanceFalloffToUse);\n        for(int i = 0; i < NUM_SAMPLES; i++) {\n            vec2 offset = (rotationMatrix * poissonDisk[i]) * texelSize * size;\n            vec4 dataSample = texture2D(tDiffuse, uv + offset);\n            float occSample = dataSample.r;\n            vec3 normalSample = dataSample.gba * 2.0 - 1.0;\n            float dSample = texture2D(sceneDepth, uv + offset).x;\n            vec3 worldPosSample = getWorldPos(dSample, uv + offset);\n            float tangentPlaneDist = abs(dot(worldPosSample - worldPos, normal));\n            float rangeCheck = float(dSample != 1.0) * exp(-1.0 * tangentPlaneDist * invDistance ) * max(dot(normal, normalSample), 0.0);\n            occlusion += occSample * rangeCheck;\n            count += rangeCheck;\n        }\n        if (count > 0.0) {\n          occlusion /= count;\n        }\n        occlusion = clamp(occlusion, 0.0, 1.0);\n        if (occlusion == 0.0) {\n          occlusion = 1.0;\n        }\n        gl_FragColor = vec4(occlusion, 0.5 + 0.5 * normal);\n    }\n    `\n\n}\nexport { PoissionBlur };", "import * as THREE from 'three';\n\nconst DepthDownSample = {\n    uniforms: {\n        'sceneDepth': { value: null },\n        'resolution': { value: /* @__PURE__ */ new THREE.Vector2() },\n        'near': { value: 0.1 },\n        'far': { value: 1000.0 },\n        'viewMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'projectionMatrixInv': { value: /* @__PURE__ */ new THREE.Matrix4() },\n        'logDepth': { value: false },\n        'ortho': { value: false }\n    },\n    depthWrite: false,\n    depthTest: false,\n\n    vertexShader: /* glsl */ `\n    varying vec2 vUv;\n    void main() {\n        vUv = uv;\n        gl_Position = vec4(position, 1);\n    }`,\n    fragmentShader: /* glsl */ `\n    uniform highp sampler2D sceneDepth;\n    uniform vec2 resolution;\n    uniform float near;\n    uniform float far;\n    uniform bool logDepth;\n    uniform bool ortho;\n    uniform mat4 viewMatrixInv;\n    uniform mat4 projectionMatrixInv;\n    varying vec2 vUv;\n    layout(location = 1) out vec4 gNormal;\n    vec3 getWorldPosLog(vec3 posS) {\n        vec2 uv = posS.xy;\n        float z = posS.z;\n        float nearZ =near;\n        float farZ = far;\n        float depth = pow(2.0, z * log2(farZ + 1.0)) - 1.0;\n        float a = farZ / (farZ - nearZ);\n        float b = farZ * nearZ / (nearZ - farZ);\n        float linDepth = a + b / depth;\n        vec4 clipVec = vec4(uv, linDepth, 1.0) * 2.0 - 1.0;\n        vec4 wpos = projectionMatrixInv * clipVec;\n        return wpos.xyz / wpos.w;\n      }\n      vec3 getWorldPos(float depth, vec2 coord) {\n        if (logDepth && !ortho) {\n          return getWorldPosLog(vec3(coord, depth));\n        }\n        float z = depth * 2.0 - 1.0;\n        vec4 clipSpacePosition = vec4(coord * 2.0 - 1.0, z, 1.0);\n        vec4 viewSpacePosition = projectionMatrixInv * clipSpacePosition;\n        // Perspective division\n       vec4 worldSpacePosition = viewSpacePosition;\n       worldSpacePosition.xyz /= worldSpacePosition.w;\n        return worldSpacePosition.xyz;\n    }\n  \n    vec3 computeNormal(vec3 worldPos, vec2 vUv) {\n      ivec2 p = ivec2(vUv * resolution);\n      #ifdef REVERSEDEPTH\n      float c0 = 1.0 - texelFetch(sceneDepth, p, 0).x;\n      float l2 = 1.0 - texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = 1.0 - texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = 1.0 - texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = 1.0 - texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = 1.0 - texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = 1.0 - texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #else\n      float c0 = texelFetch(sceneDepth, p, 0).x;\n      float l2 = texelFetch(sceneDepth, p - ivec2(2, 0), 0).x;\n      float l1 = texelFetch(sceneDepth, p - ivec2(1, 0), 0).x;\n      float r1 = texelFetch(sceneDepth, p + ivec2(1, 0), 0).x;\n      float r2 = texelFetch(sceneDepth, p + ivec2(2, 0), 0).x;\n      float b2 = texelFetch(sceneDepth, p - ivec2(0, 2), 0).x;\n      float b1 = texelFetch(sceneDepth, p - ivec2(0, 1), 0).x;\n      float t1 = texelFetch(sceneDepth, p + ivec2(0, 1), 0).x;\n      float t2 = texelFetch(sceneDepth, p + ivec2(0, 2), 0).x;\n      #endif\n  \n      float dl = abs((2.0 * l1 - l2) - c0);\n      float dr = abs((2.0 * r1 - r2) - c0);\n      float db = abs((2.0 * b1 - b2) - c0);\n      float dt = abs((2.0 * t1 - t2) - c0);\n  \n      vec3 ce = getWorldPos(c0, vUv).xyz;\n  \n      vec3 dpdx = (dl < dr) ? ce - getWorldPos(l1, (vUv - vec2(1.0 / resolution.x, 0.0))).xyz\n                            : -ce + getWorldPos(r1, (vUv + vec2(1.0 / resolution.x, 0.0))).xyz;\n      vec3 dpdy = (db < dt) ? ce - getWorldPos(b1, (vUv - vec2(0.0, 1.0 / resolution.y))).xyz\n                            : -ce + getWorldPos(t1, (vUv + vec2(0.0, 1.0 / resolution.y))).xyz;\n  \n      return normalize(cross(dpdx, dpdy));\n  }\n    void main() {\n        vec2 uv = vUv - vec2(0.5) / resolution;\n        vec2 pixelSize = vec2(1.0) / resolution;\n        highp vec2[4] uvSamples;\n        uvSamples[0] = uv;\n        uvSamples[1] = uv + vec2(pixelSize.x, 0.0);\n        uvSamples[2] = uv + vec2(0.0, pixelSize.y);\n        uvSamples[3] = uv + pixelSize;\n        #ifdef REVERSEDEPTH\n        float depth00 = 1.0 - texture2D(sceneDepth, uvSamples[0]).r;\n        float depth10 = 1.0 - texture2D(sceneDepth, uvSamples[1]).r;\n        float depth01 = 1.0 - texture2D(sceneDepth, uvSamples[2]).r;\n        float depth11 = 1.0 - texture2D(sceneDepth, uvSamples[3]).r;\n        #else\n        float depth00 = texture2D(sceneDepth, uvSamples[0]).r;\n        float depth10 = texture2D(sceneDepth, uvSamples[1]).r;\n        float depth01 = texture2D(sceneDepth, uvSamples[2]).r;\n        float depth11 = texture2D(sceneDepth, uvSamples[3]).r;\n        #endif\n        float minDepth = min(min(depth00, depth10), min(depth01, depth11));\n        float maxDepth = max(max(depth00, depth10), max(depth01, depth11));\n        float targetDepth = minDepth;\n        // Checkerboard pattern to avoid artifacts\n        if (mod(gl_FragCoord.x + gl_FragCoord.y, 2.0) > 0.5) { \n            targetDepth = maxDepth;\n        }\n        int chosenIndex = 0;\n        float[4] samples;\n        samples[0] = depth00;\n        samples[1] = depth10;\n        samples[2] = depth01;\n        samples[3] = depth11;\n        for(int i = 0; i < 4; ++i) {\n            if (samples[i] == targetDepth) {\n                chosenIndex = i;\n                break;\n            }\n        }\n        gl_FragColor = vec4(samples[chosenIndex], 0.0, 0.0, 1.0);\n        gNormal = vec4(computeNormal(\n            getWorldPos(samples[chosenIndex], uvSamples[chosenIndex]), uvSamples[chosenIndex]\n        ), 0.0);\n    }`\n};\n\nexport { DepthDownSample };", "import * as THREE from 'three';\nimport { Pass } from \"postprocessing\";\nimport { FullScreenTriangle } from \"./FullScreenTriangle.js\";\nimport { EffectShader } from './EffectShader.js';\nimport { EffectCompositer } from './EffectCompositer.js';\nimport { PoissionBlur } from './PoissionBlur.js';\nimport { DepthDownSample } from \"./DepthDownSample.js\";\nimport bluenoiseBits from './BlueNoise.js';\nimport { N8AOPass, DepthType } from './N8AOPass.js';\nimport { WebGLMultipleRenderTargetsCompat } from './compat.js';\n\n/**\n * \n * @param {*} timerQuery \n * @param {THREE.WebGLRenderer} gl \n * @param {N8AOPostPass | N8AOPass} pass \n */\nfunction checkTimerQuery(timerQuery, gl, pass) {\n    const available = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT_AVAILABLE);\n    if (available) {\n        const elapsedTimeInNs = gl.getQueryParameter(timerQuery, gl.QUERY_RESULT);\n        const elapsedTimeInMs = elapsedTimeInNs / 1000000;\n        pass.lastTime = pass.lastTime === 0 ? elapsedTimeInMs : pass.timeRollingAverage * pass.lastTime + (1 - pass.timeRollingAverage) * elapsedTimeInMs;\n    } else {\n        // If the result is not available yet, check again after a delay\n        setTimeout(() => {\n            checkTimerQuery(timerQuery, gl, pass);\n        }, 1);\n    }\n}\nclass N8AOPostPass extends Pass {\n    /**\n     * \n     * @param {THREE.Scene} scene\n     * @param {THREE.Camera} camera \n     * @param {number} width \n     * @param {number} height\n     *  \n     * @property {THREE.Scene} scene\n     * @property {THREE.Camera} camera\n     * @property {number} width\n     * @property {number} height\n     */\n    constructor(scene, camera, width = 512, height = 512) {\n        super();\n        this.width = width;\n        this.height = height;\n\n        this.clear = true;\n\n        this.camera = camera;\n        this.scene = scene;\n        /**\n         * @type {Proxy & {\n         * aoSamples: number,\n         * aoRadius: number,\n         * denoiseSamples: number,\n         * denoiseRadius: number,\n         * distanceFalloff: number,\n         * intensity: number,\n         * denoiseIterations: number,\n         * renderMode: 0 | 1 | 2 | 3 | 4,\n         * color: THREE.Color,\n         * gammaCorrection: boolean,\n         * depthBufferType: 1 | 2 | 3,\n         * screenSpaceRadius: boolean,\n         * halfRes: boolean,\n         * depthAwareUpsampling: boolean\n         * colorMultiply: boolean\n         * }\n         */\n        this.autosetGamma = true;\n        this.configuration = new Proxy({\n            aoSamples: 16,\n            aoRadius: 5.0,\n            aoTones: 0.0,\n            denoiseSamples: 8,\n            denoiseRadius: 12,\n            distanceFalloff: 1.0,\n            intensity: 5,\n            denoiseIterations: 2.0,\n            renderMode: 0,\n            biasOffset: 0.0,\n            biasMultiplier: 0.0,\n            color: new THREE.Color(0, 0, 0),\n            gammaCorrection: true,\n            depthBufferType: DepthType.Default,\n            screenSpaceRadius: false,\n            halfRes: false,\n            depthAwareUpsampling: true,\n            colorMultiply: true,\n            transparencyAware: false,\n            accumulate: false\n        }, {\n            set: (target, propName, value) => {\n                const oldProp = target[propName];\n                target[propName] = value;\n                if (value.equals) {\n                    if (!value.equals(oldProp)) {\n                        this.firstFrame();\n                    }\n                } else {\n                    if (oldProp !== value) {\n                        this.firstFrame();\n                    }\n                }\n                if (propName === 'aoSamples' && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === 'denoiseSamples' && oldProp !== value) {\n                    this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === \"halfRes\" && oldProp !== value) {\n                    this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.configureHalfResTargets();\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                    this.setSize(this.width, this.height);\n                }\n                if (propName === \"depthAwareUpsampling\" && oldProp !== value) {\n                    this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                }\n                if (propName === 'gammaCorrection') {\n                    this.autosetGamma = false;\n                }\n                if (propName === \"transparencyAware\" && oldProp !== value) {\n                    this.autoDetectTransparency = false;\n                    this.configureTransparencyTarget();\n                }\n                return true;\n            }\n        });\n        /** @type {THREE.Vector3[]} */\n        this.samples = [];\n        /** @type {THREE.Vector2[]} */\n        this.samplesDenoise = [];\n        this.autoDetectTransparency = true;\n        this.frames = 0;\n        this.lastViewMatrix = new THREE.Matrix4();\n        this.lastProjectionMatrix = new THREE.Matrix4();\n        this.configureEffectCompositer(this.configuration.depthBufferType);\n        this.configureSampleDependentPasses();\n        this.configureHalfResTargets();\n        this.detectTransparency();\n        this.configureTransparencyTarget();\n\n        //   this.effectCompisterQuad = new FullScreenTriangle(new THREE.ShaderMaterial(EffectCompositer));\n        this.copyQuad = new FullScreenTriangle(new THREE.ShaderMaterial({\n            uniforms: {\n                tDiffuse: {\n                    value: null\n                }\n            },\n            depthWrite: false,\n            vertexShader: `\n            varying vec2 vUv;\n            void main() {\n                vUv = uv;\n                gl_Position = vec4(position, 1);\n            }\n            `,\n            fragmentShader: `\n            uniform sampler2D tDiffuse;\n            varying vec2 vUv;\n            void main() {\n                gl_FragColor = texture2D(tDiffuse, vUv);\n            }\n            `\n        }))\n        this.writeTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.readTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat\n        });\n        this.outputTargetInternal = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false\n        });\n        this.accumulationRenderTarget = new THREE.WebGLRenderTarget(this.width, this.height, {\n            minFilter: THREE.LinearFilter,\n            magFilter: THREE.LinearFilter,\n            depthBuffer: false,\n            format: THREE.RGBAFormat,\n            type: THREE.HalfFloatType,\n            stencilBuffer: false,\n            depthBuffer: false,\n            alpha: true\n        });\n        this.accumulationQuad = new FullScreenTriangle(new THREE.ShaderMaterial({\n            uniforms: {\n                frame: { value: 0 },\n                tDiffuse: { value: null }\n            },\n            transparent: true,\n            opacity: 1,\n            vertexShader: `\n             varying vec2 vUv;\n             void main() {\n                 vUv = uv;\n                 gl_Position = vec4(position, 1);\n             }`,\n            fragmentShader: `\n             uniform sampler2D tDiffuse;\n             uniform float frame;\n                varying vec2 vUv;\n                void main() {\n                    vec4 color = texture2D(tDiffuse, vUv);\n                    gl_FragColor = vec4(color.rgb, 1.0 / (frame + 1.0));\n                }\n                `\n        }));\n\n\n        /** @type {THREE.DataTexture} */\n        this.bluenoise = //bluenoise;\n            new THREE.DataTexture(\n                bluenoiseBits,\n                128,\n                128\n            );\n        this.bluenoise.colorSpace = THREE.NoColorSpace;\n        this.bluenoise.wrapS = THREE.RepeatWrapping;\n        this.bluenoise.wrapT = THREE.RepeatWrapping;\n        this.bluenoise.minFilter = THREE.NearestFilter;\n        this.bluenoise.magFilter = THREE.NearestFilter;\n        this.bluenoise.needsUpdate = true;\n        this.lastTime = 0;\n        this.timeRollingAverage = 0.99;\n        this.needsDepthTexture = true;\n        this.needsSwap = true;\n        this._r = new THREE.Vector2();\n        this._c = new THREE.Color();\n\n\n\n    }\n    configureHalfResTargets() {\n        this.firstFrame();\n\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget = new WebGLMultipleRenderTargetsCompat(\n                this.width / 2,\n                this.height / 2,\n                2\n            );\n\n            if (THREE.REVISION <= 161) {\n                this.depthDownsampleTarget.textures = this.depthDownsampleTarget.texture;\n            }\n\n            this.depthDownsampleTarget.textures[0].format = THREE.RedFormat;\n            this.depthDownsampleTarget.textures[0].type = THREE.FloatType;\n            this.depthDownsampleTarget.textures[0].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[0].depthBuffer = false;\n            this.depthDownsampleTarget.textures[1].format = THREE.RGBAFormat;\n            this.depthDownsampleTarget.textures[1].type = THREE.HalfFloatType;\n            this.depthDownsampleTarget.textures[1].minFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].magFilter = THREE.NearestFilter;\n            this.depthDownsampleTarget.textures[1].depthBuffer = false;\n\n\n            this.depthDownsampleQuad = new FullScreenTriangle(new THREE.ShaderMaterial(DepthDownSample));\n        } else {\n            if (this.depthDownsampleTarget) {\n                this.depthDownsampleTarget.dispose();\n                this.depthDownsampleTarget = null;\n            }\n            if (this.depthDownsampleQuad) {\n                this.depthDownsampleQuad.dispose();\n                this.depthDownsampleQuad = null;\n            }\n        }\n    }\n    detectTransparency() {\n        if (this.autoDetectTransparency) {\n            let isTransparency = false;\n            this.scene.traverse((obj) => {\n                if (obj.material && obj.material.transparent) {\n                    isTransparency = true;\n                }\n            });\n            if (isTransparency) {\n                this.configuration.transparencyAware = true;\n            }\n        }\n    }\n    configureTransparencyTarget() {\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue = new THREE.WebGLRenderTarget(this.width, this.height, {\n                minFilter: THREE.LinearFilter,\n                magFilter: THREE.NearestFilter,\n                type: THREE.HalfFloatType,\n                format: THREE.RGBAFormat\n            });\n            this.transparencyRenderTargetDWTrue.depthTexture = new THREE.DepthTexture(this.width, this.height, THREE.UnsignedIntType);\n            this.depthCopyPass = new FullScreenTriangle(new THREE.ShaderMaterial({\n                uniforms: {\n                    depthTexture: { value: this.depthTexture },\n                    reverseDepthBuffer: { value: this.configuration.depthBufferType === DepthType.Reverse },\n                },\n                vertexShader: /* glsl */ `\n            varying vec2 vUv;\n            void main() {\n                vUv = uv;\n                gl_Position = vec4(position, 1);\n            }`,\n                fragmentShader: /* glsl */ `\n            uniform sampler2D depthTexture;\n            uniform bool reverseDepthBuffer;\n            varying vec2 vUv;\n            void main() {\n                if (reverseDepthBuffer) {\n               float d = 1.0 - texture2D(depthTexture, vUv).r;\n           \n               d += 0.00001;\n               gl_FragDepth = 1.0 - d;\n            } else {\n                float d = texture2D(depthTexture, vUv).r;\n                d += 0.00001;\n                gl_FragDepth = d;\n            }\n               gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n            }\n            `,\n\n            }));\n        } else {\n            if (this.transparencyRenderTargetDWFalse) {\n                this.transparencyRenderTargetDWFalse.dispose();\n                this.transparencyRenderTargetDWFalse = null;\n            }\n            if (this.transparencyRenderTargetDWTrue) {\n                this.transparencyRenderTargetDWTrue.dispose();\n                this.transparencyRenderTargetDWTrue = null;\n            }\n            if (this.depthCopyPass) {\n                this.depthCopyPass.dispose();\n                this.depthCopyPass = null;\n            }\n        }\n    }\n    renderTransparency(renderer) {\n        const oldBackground = this.scene.background;\n        const oldClearColor = renderer.getClearColor(new THREE.Color());\n        const oldClearAlpha = renderer.getClearAlpha();\n        const oldVisibility = new Map();\n        const oldAutoClearDepth = renderer.autoClearDepth;\n        this.scene.traverse((obj) => {\n            oldVisibility.set(obj, obj.visible);\n        });\n\n        // Override the state\n        this.scene.background = null;\n        renderer.autoClearDepth = false;\n        renderer.setClearColor(new THREE.Color(0, 0, 0), 0);\n\n        this.depthCopyPass.material.uniforms.depthTexture.value = this.depthTexture;\n        this.depthCopyPass.material.uniforms.reverseDepthBuffer.value = this.configuration.depthBufferType === DepthType.Reverse;\n        // Render out transparent objects WITHOUT depth write\n        renderer.setRenderTarget(this.transparencyRenderTargetDWFalse);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && ((obj.material.transparent && !obj.material.depthWrite && !obj.userData.treatAsOpaque) || !!obj.userData.cannotReceiveAO);\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Render out transparent objects WITH depth write\n\n        renderer.setRenderTarget(this.transparencyRenderTargetDWTrue);\n        this.scene.traverse((obj) => {\n            if (obj.material) {\n                obj.visible = oldVisibility.get(obj) && obj.material.transparent && obj.material.depthWrite && !obj.userData.treatAsOpaque;\n            }\n        });\n        renderer.clear(true, true, true);\n        this.depthCopyPass.render(renderer);\n        renderer.render(this.scene, this.camera);\n\n        // Restore\n        this.scene.traverse((obj) => {\n            obj.visible = oldVisibility.get(obj);\n        });\n        renderer.setClearColor(oldClearColor, oldClearAlpha);\n        this.scene.background = oldBackground;\n        renderer.autoClearDepth = oldAutoClearDepth;\n    }\n    configureSampleDependentPasses() {\n        this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n        this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n    }\n    configureAOPass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samples = this.generateHemisphereSamples(this.configuration.aoSamples);\n        const e = {...EffectShader };\n        e.fragmentShader = e.fragmentShader.replace(\"16\", this.configuration.aoSamples).replace(\"16.0\", this.configuration.aoSamples + \".0\");\n        if (depthBufferType === DepthType.Log) {\n            e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n        }\n        if (ortho) {\n            e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n        }\n        if (this.configuration.halfRes) {\n            e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n        }\n        if (this.effectShaderQuad) {\n            this.effectShaderQuad.material.dispose();\n            this.effectShaderQuad.material = new THREE.ShaderMaterial(e);\n        } else {\n            this.effectShaderQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n        }\n    }\n    configureDenoisePass(depthBufferType = DepthType.Default, ortho = false) {\n        this.firstFrame();\n        this.samplesDenoise = this.generateDenoiseSamples(this.configuration.denoiseSamples, 11);\n        const p = {...PoissionBlur };\n        p.fragmentShader = p.fragmentShader.replace(\"16\", this.configuration.denoiseSamples);\n        if (depthBufferType === DepthType.Log) {\n            p.fragmentShader = \"#define LOGDEPTH\\n\" + p.fragmentShader;\n        } else if (depthBufferType === DepthType.Reverse) {\n            p.fragmentShader = \"#define REVERSEDEPTH\\n\" + p.fragmentShader;\n        }\n        if (ortho) {\n            p.fragmentShader = \"#define ORTHO\\n\" + p.fragmentShader;\n        }\n        if (this.poissonBlurQuad) {\n            this.poissonBlurQuad.material.dispose();\n            this.poissonBlurQuad.material = new THREE.ShaderMaterial(p);\n        } else {\n            this.poissonBlurQuad = new FullScreenTriangle(new THREE.ShaderMaterial(p));\n        }\n    }\n    configureEffectCompositer(depthBufferType = DepthType.Default, ortho = false) {\n            this.firstFrame();\n\n            const e = {...EffectCompositer };\n            if (depthBufferType === DepthType.Log) {\n                e.fragmentShader = \"#define LOGDEPTH\\n\" + e.fragmentShader;\n            } else if (depthBufferType === DepthType.Reverse) {\n                e.fragmentShader = \"#define REVERSEDEPTH\\n\" + e.fragmentShader;\n            }\n            if (ortho) {\n                e.fragmentShader = \"#define ORTHO\\n\" + e.fragmentShader;\n            }\n            if (this.configuration.halfRes && this.configuration.depthAwareUpsampling) {\n                e.fragmentShader = \"#define HALFRES\\n\" + e.fragmentShader;\n            }\n            if (this.effectCompositerQuad) {\n                this.effectCompositerQuad.material.dispose();\n                this.effectCompositerQuad.material = new THREE.ShaderMaterial(e);\n            } else {\n                this.effectCompositerQuad = new FullScreenTriangle(new THREE.ShaderMaterial(e));\n            }\n        }\n        /**\n         * \n         * @param {Number} n \n         * @returns {THREE.Vector3[]}\n         */\n    generateHemisphereSamples(n) {\n            const points = [];\n            for (let k = 0; k < n; k++) {\n                const theta = 2.399963 * k;\n                const r = (Math.sqrt(k + 0.5) / Math.sqrt(n));\n                const x = r * Math.cos(theta);\n                const y = r * Math.sin(theta);\n                // Project to hemisphere\n                const z = Math.sqrt(1 - (x * x + y * y));\n                points.push(new THREE.Vector3(x, y, z));\n\n            }\n            return points;\n        }\n        /**\n         * \n         * @param {number} numSamples \n         * @param {number} numRings \n         * @returns {THREE.Vector2[]}\n         */\n    generateDenoiseSamples(numSamples, numRings) {\n        const angleStep = 2 * Math.PI * numRings / numSamples;\n        const invNumSamples = 1.0 / numSamples;\n        const radiusStep = invNumSamples;\n        const samples = [];\n        let radius = invNumSamples;\n        let angle = 0;\n        for (let i = 0; i < numSamples; i++) {\n            samples.push(new THREE.Vector2(Math.cos(angle), Math.sin(angle)).multiplyScalar(Math.pow(radius, 0.75)));\n            radius += radiusStep;\n            angle += angleStep;\n        }\n        return samples;\n    }\n    setSize(width, height) {\n        this.firstFrame();\n        this.width = width;\n        this.height = height;\n        const c = this.configuration.halfRes ? 0.5 : 1;\n        this.writeTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.readTargetInternal.setSize(width *\n            c, height *\n            c);\n        this.accumulationRenderTarget.setSize(width * c, height * c);\n        if (this.configuration.halfRes) {\n            this.depthDownsampleTarget.setSize(width * c, height * c);\n        }\n        if (this.configuration.transparencyAware) {\n            this.transparencyRenderTargetDWFalse.setSize(width, height);\n            this.transparencyRenderTargetDWTrue.setSize(width, height);\n        }\n        this.outputTargetInternal.setSize(width, height);\n    }\n    setDepthTexture(depthTexture) {\n        this.depthTexture = depthTexture;\n    }\n    firstFrame() {\n        this.needsFrame = true;\n    }\n    render(renderer, inputBuffer, outputBuffer) {\n            const xrEnabled = renderer.xr.enabled;\n            renderer.xr.enabled = false;\n\n            // Copy inputBuffer to outputBuffer\n            //renderer.setRenderTarget(outputBuffer);\n            //  this.copyQuad.material.uniforms.tDiffuse.value = inputBuffer.texture;\n            //   this.copyQuad.render(renderer);\n\n            if (renderer.capabilities.logarithmicDepthBuffer && this.configuration.depthBufferType !== DepthType.Log || renderer.capabilities.reverseDepthBuffer && this.configuration.depthBufferType !== DepthType.Reverse) {\n                this.configuration.depthBufferType = renderer.capabilities.logarithmicDepthBuffer ? DepthType.Log : renderer.capabilities.reverseDepthBuffer ? DepthType.Reverse : DepthType.Default;\n                this.configureAOPass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureDenoisePass(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n                this.configureEffectCompositer(this.configuration.depthBufferType, this.camera.isOrthographicCamera);\n            }\n            this.detectTransparency();\n            if (inputBuffer.texture.type !== this.outputTargetInternal.texture.type ||\n                inputBuffer.texture.format !== this.outputTargetInternal.texture.format\n            ) {\n                this.outputTargetInternal.texture.type = inputBuffer.texture.type;\n                this.outputTargetInternal.texture.format = inputBuffer.texture.format;\n                this.outputTargetInternal.texture.needsUpdate = true;\n            }\n            this.camera.updateMatrixWorld();\n            if (this.lastViewMatrix.equals(this.camera.matrixWorldInverse) && this.lastProjectionMatrix.equals(this.camera.projectionMatrix) && this.configuration.accumulate && !this.needsFrame) {\n                this.frame++;\n            } else {\n                if (this.configuration.accumulate) {\n                    renderer.setRenderTarget(this.accumulationRenderTarget);\n                    renderer.clear(true, true, true);\n                }\n                this.frame = 0;\n                this.needsFrame = false;\n            }\n            this.lastViewMatrix.copy(this.camera.matrixWorldInverse);\n            this.lastProjectionMatrix.copy(this.camera.projectionMatrix);\n            let gl;\n            let ext;\n            let timerQuery;\n            if (this.debugMode) {\n                gl = renderer.getContext();\n                ext = gl.getExtension('EXT_disjoint_timer_query_webgl2');\n                if (ext === null) {\n                    console.error(\"EXT_disjoint_timer_query_webgl2 not available, disabling debug mode.\");\n                    this.debugMode = false;\n                }\n            }\n            if (this.debugMode) {\n                timerQuery = gl.createQuery();\n                gl.beginQuery(ext.TIME_ELAPSED_EXT, timerQuery);\n            }\n            if (this.configuration.transparencyAware) {\n                this.renderTransparency(renderer);\n            }\n            this._r.set(this.width, this.height);\n            let trueRadius = this.configuration.aoRadius;\n            if (this.configuration.halfRes && this.configuration.screenSpaceRadius) {\n                trueRadius *= 0.5;\n            }\n            if (this.frame < 1024 / this.configuration.aoSamples) {\n                if (this.configuration.halfRes) {\n\n                    renderer.setRenderTarget(this.depthDownsampleTarget);\n                    this.depthDownsampleQuad.material.uniforms.sceneDepth.value = this.depthTexture;\n                    this.depthDownsampleQuad.material.uniforms.resolution.value = this._r;\n                    this.depthDownsampleQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.depthDownsampleQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.depthDownsampleQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.depthDownsampleQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.depthDownsampleQuad.material.uniforms[\"logDepth\"].value = this.configuration.logarithmicDepthBuffer;\n                    this.depthDownsampleQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                    this.depthDownsampleQuad.render(renderer);\n                }\n                this.effectShaderQuad.material.uniforms[\"sceneDiffuse\"].value = inputBuffer.texture;\n                this.effectShaderQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n                this.effectShaderQuad.material.uniforms[\"sceneNormal\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[1] : null;\n                this.effectShaderQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                this.effectShaderQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                this.effectShaderQuad.material.uniforms[\"projViewMat\"].value = this.camera.projectionMatrix.clone().multiply(this.camera.matrixWorldInverse.clone());\n                this.effectShaderQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                this.effectShaderQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                this.effectShaderQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                this.effectShaderQuad.material.uniforms['biasAdjustment'].value = new THREE.Vector2(this.configuration.biasOffset, this.configuration.biasMultiplier);\n                this.effectShaderQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                this.effectShaderQuad.material.uniforms['time'].value = performance.now() / 1000;\n                this.effectShaderQuad.material.uniforms['samples'].value = this.samples;\n                this.effectShaderQuad.material.uniforms['bluenoise'].value = this.bluenoise;\n                this.effectShaderQuad.material.uniforms['radius'].value = trueRadius;\n                this.effectShaderQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                this.effectShaderQuad.material.uniforms[\"near\"].value = this.camera.near;\n                this.effectShaderQuad.material.uniforms[\"far\"].value = this.camera.far;\n                this.effectShaderQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n                this.effectShaderQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                this.effectShaderQuad.material.uniforms[\"frame\"].value = this.frame;\n                // Start the AO\n                renderer.setRenderTarget(this.writeTargetInternal);\n                this.effectShaderQuad.render(renderer);\n                // End the AO\n                // Start the blur\n                for (let i = 0; i < this.configuration.denoiseIterations; i++) {\n                    [this.writeTargetInternal, this.readTargetInternal] = [this.readTargetInternal, this.writeTargetInternal];\n                    this.poissonBlurQuad.material.uniforms[\"tDiffuse\"].value = this.readTargetInternal.texture;\n                    this.poissonBlurQuad.material.uniforms[\"sceneDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n                    this.poissonBlurQuad.material.uniforms[\"projMat\"].value = this.camera.projectionMatrix;\n                    this.poissonBlurQuad.material.uniforms[\"viewMat\"].value = this.camera.matrixWorldInverse;\n                    this.poissonBlurQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n                    this.poissonBlurQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n                    this.poissonBlurQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n                    this.poissonBlurQuad.material.uniforms['resolution'].value = (this.configuration.halfRes ? this._r.clone().multiplyScalar(1 / 2).floor() : this._r);\n                    this.poissonBlurQuad.material.uniforms['time'].value = performance.now() / 1000;\n                    this.poissonBlurQuad.material.uniforms['blueNoise'].value = this.bluenoise;\n                    this.poissonBlurQuad.material.uniforms['radius'].value = this.configuration.denoiseRadius * (\n                        this.configuration.halfRes ? 1 / 2 : 1\n                    );\n                    this.poissonBlurQuad.material.uniforms['worldRadius'].value = trueRadius;\n                    this.poissonBlurQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n                    this.poissonBlurQuad.material.uniforms['index'].value = i;\n                    this.poissonBlurQuad.material.uniforms['poissonDisk'].value = this.samplesDenoise;\n                    this.poissonBlurQuad.material.uniforms[\"near\"].value = this.camera.near;\n                    this.poissonBlurQuad.material.uniforms[\"far\"].value = this.camera.far;\n                    this.poissonBlurQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n                    renderer.setRenderTarget(this.writeTargetInternal);\n                    this.poissonBlurQuad.render(renderer);\n\n                }\n                renderer.setRenderTarget(this.accumulationRenderTarget);\n                const oldAutoClear = renderer.autoClear;\n                renderer.autoClear = false;\n                this.accumulationQuad.material.uniforms[\"tDiffuse\"].value = this.writeTargetInternal.texture;\n                this.accumulationQuad.material.uniforms[\"frame\"].value = this.frame;\n                this.accumulationQuad.render(renderer);\n                renderer.autoClear = oldAutoClear;\n            }\n            // Now, we have the blurred AO in writeTargetInternal\n            // End the blur\n            // Start the composition\n            if (this.configuration.transparencyAware) {\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWFalse\"].value = this.transparencyRenderTargetDWFalse.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrue\"].value = this.transparencyRenderTargetDWTrue.texture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyDWTrueDepth\"].value = this.transparencyRenderTargetDWTrue.depthTexture;\n                this.effectCompositerQuad.material.uniforms[\"transparencyAware\"].value = true;\n            }\n            this.effectCompositerQuad.material.uniforms[\"sceneDiffuse\"].value = inputBuffer.texture;\n            this.effectCompositerQuad.material.uniforms[\"sceneDepth\"].value = this.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"aoTones\"].value = this.configuration.aoTones;\n            this.effectCompositerQuad.material.uniforms[\"near\"].value = this.camera.near;\n            this.effectCompositerQuad.material.uniforms[\"far\"].value = this.camera.far;\n            this.effectCompositerQuad.material.uniforms[\"projectionMatrixInv\"].value = this.camera.projectionMatrixInverse;\n            this.effectCompositerQuad.material.uniforms[\"viewMatrixInv\"].value = this.camera.matrixWorld;\n            this.effectCompositerQuad.material.uniforms[\"ortho\"].value = this.camera.isOrthographicCamera;\n            this.effectCompositerQuad.material.uniforms[\"downsampledDepth\"].value = this.configuration.halfRes ? this.depthDownsampleTarget.textures[0] : this.depthTexture;\n            this.effectCompositerQuad.material.uniforms[\"resolution\"].value = this._r;\n            this.effectCompositerQuad.material.uniforms[\"blueNoise\"].value = this.bluenoise;\n            this.effectCompositerQuad.material.uniforms[\"intensity\"].value = this.configuration.intensity;\n            this.effectCompositerQuad.material.uniforms[\"renderMode\"].value = this.configuration.renderMode;\n            this.effectCompositerQuad.material.uniforms[\"screenSpaceRadius\"].value = this.configuration.screenSpaceRadius;\n            this.effectCompositerQuad.material.uniforms['radius'].value = trueRadius;\n            this.effectCompositerQuad.material.uniforms['distanceFalloff'].value = this.configuration.distanceFalloff;\n            this.effectCompositerQuad.material.uniforms[\"gammaCorrection\"].value = this.autosetGamma ?\n                this.renderToScreen :\n                this.configuration.gammaCorrection;\n            this.effectCompositerQuad.material.uniforms[\"tDiffuse\"].value = this.accumulationRenderTarget.texture;\n            this.effectCompositerQuad.material.uniforms[\"color\"].value =\n                this._c.copy(\n                    this.configuration.color\n                ).convertSRGBToLinear();\n            this.effectCompositerQuad.material.uniforms[\"colorMultiply\"].value = this.configuration.colorMultiply;\n            this.effectCompositerQuad.material.uniforms[\"cameraPos\"].value = this.camera.getWorldPosition(new THREE.Vector3());\n            this.effectCompositerQuad.material.uniforms[\"fog\"].value = !!this.scene.fog;\n            if (this.scene.fog) {\n                if (\n                    this.scene.fog.isFog\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = false;\n                    this.effectCompositerQuad.material.uniforms[\"fogNear\"].value = this.scene.fog.near;\n                    this.effectCompositerQuad.material.uniforms[\"fogFar\"].value = this.scene.fog.far;\n                } else if (\n                    this.scene.fog.isFogExp2\n                ) {\n                    this.effectCompositerQuad.material.uniforms[\"fogExp\"].value = true;\n                    this.effectCompositerQuad.material.uniforms[\"fogDensity\"].value = this.scene.fog.density;\n                } else {\n                    console.error(`Unsupported fog type ${this.scene.fog.constructor.name} in SSAOPass.`);\n                }\n\n\n            }\n            renderer.setRenderTarget(\n                /* this.renderToScreen ? null :\n                 outputBuffer*/\n                this.outputTargetInternal\n            );\n            this.effectCompositerQuad.render(renderer);\n            renderer.setRenderTarget(\n                this.renderToScreen ? null :\n                outputBuffer\n            );\n            this.copyQuad.material.uniforms[\"tDiffuse\"].value = this.outputTargetInternal.texture;\n            this.copyQuad.render(renderer);\n            if (this.debugMode) {\n                gl.endQuery(ext.TIME_ELAPSED_EXT);\n                checkTimerQuery(timerQuery, gl, this);\n            }\n\n            renderer.xr.enabled = xrEnabled;\n        }\n        /**\n         * Enables the debug mode of the AO, meaning the lastTime value will be updated.\n         */\n    enableDebugMode() {\n            this.debugMode = true;\n        }\n        /**\n         * Disables the debug mode of the AO, meaning the lastTime value will not be updated.\n         */\n    disableDebugMode() {\n            this.debugMode = false;\n        }\n        /**\n         * Sets the display mode of the AO\n         * @param {\"Combined\" | \"AO\" | \"No AO\" | \"Split\" | \"Split AO\"} mode - The display mode. \n         */\n    setDisplayMode(mode) {\n            this.configuration.renderMode = [\"Combined\", \"AO\", \"No AO\", \"Split\", \"Split AO\"].indexOf(mode);\n        }\n        /**\n         * \n         * @param {\"Performance\" | \"Low\" | \"Medium\" | \"High\" | \"Ultra\"} mode \n         */\n    setQualityMode(mode) {\n        if (mode === \"Performance\") {\n            this.configuration.aoSamples = 8;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Low\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 4;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"Medium\") {\n            this.configuration.aoSamples = 16;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 12;\n        } else if (mode === \"High\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 8;\n            this.configuration.denoiseRadius = 6;\n        } else if (mode === \"Ultra\") {\n            this.configuration.aoSamples = 64;\n            this.configuration.denoiseSamples = 16;\n            this.configuration.denoiseRadius = 6;\n        }\n\n    }\n}\nexport { N8AOPostPass };", "const BlueNoise = `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`;\n\nconst bluenoiseBits = /* @__PURE__ */ (() => Uint8Array.from(atob(BlueNoise), c => c.charCodeAt(0)))();\n\nexport default bluenoiseBits;", "import * as THREE from \"three\";\nconst version = /* @__PURE__ */ (() =>\n  parseInt(THREE.REVISION.replace(/\\D+/g, \"\")))();\n\n// NOTE: WebGLMultipleRenderTargets is removed since r172, so we implement it ourselves.\n// https://github.com/mrdoob/three.js/pull/26427\nexport const WebGLMultipleRenderTargetsCompat =\n  version >= 162\n    ? class extends THREE.WebGLRenderTarget {\n        constructor(width = 1, height = 1, count = 1, options = {}) {\n          super(width, height, { ...options, count });\n          this.isWebGLMultipleRenderTargets = true;\n        }\n        get texture() {\n          return this.textures;\n        }\n      }\n    : class extends THREE.WebGLRenderTarget {\n        constructor(width = 1, height = 1, count = 1, options = {}) {\n          super(width, height, options);\n          this.isWebGLMultipleRenderTargets = true;\n          const texture = this.texture;\n          this.texture = [];\n          for (let i = 0; i < count; i++) {\n            this.texture[i] = texture.clone();\n            this.texture[i].isRenderTargetTexture = true;\n          }\n        }\n        setSize(width, height, depth = 1) {\n          if (\n            this.width !== width ||\n            this.height !== height ||\n            this.depth !== depth\n          ) {\n            this.width = width;\n            this.height = height;\n            this.depth = depth;\n            for (let i = 0, il = this.texture.length; i < il; i++) {\n              this.texture[i].image.width = width;\n              this.texture[i].image.height = height;\n              this.texture[i].image.depth = depth;\n            }\n            this.dispose();\n          }\n          this.viewport.set(0, 0, width, height);\n          this.scissor.set(0, 0, width, height);\n        }\n        copy(source) {\n          this.dispose();\n          this.width = source.width;\n          this.height = source.height;\n          this.depth = source.depth;\n          this.scissor.copy(source.scissor);\n          this.scissorTest = source.scissorTest;\n          this.viewport.copy(source.viewport);\n          this.depthBuffer = source.depthBuffer;\n          this.stencilBuffer = source.stencilBuffer;\n          if (source.depthTexture !== null)\n            this.depthTexture = source.depthTexture.clone();\n          this.texture.length = 0;\n          for (let i = 0, il = source.texture.length; i < il; i++) {\n            this.texture[i] = source.texture[i].clone();\n            this.texture[i].isRenderTargetTexture = true;\n          }\n          return this;\n        }\n      };\n", "import * as THREE from 'three'\r\nimport React, { createContext, useState, useContext, useEffect, useRef, useMemo } from 'react'\r\nimport { type ThreeElements } from '@react-three/fiber'\r\n\r\nexport type Api = {\r\n  selected: THREE.Object3D[]\r\n  select: React.Dispatch<React.SetStateAction<THREE.Object3D[]>>\r\n  enabled: boolean\r\n}\r\nexport type SelectApi = Omit<ThreeElements['group'], 'ref'> & {\r\n  enabled?: boolean\r\n}\r\n\r\nexport const selectionContext = /* @__PURE__ */ createContext<Api | null>(null)\r\n\r\nexport function Selection({ children, enabled = true }: { enabled?: boolean; children: React.ReactNode }) {\r\n  const [selected, select] = useState<THREE.Object3D[]>([])\r\n  const value = useMemo(() => ({ selected, select, enabled }), [selected, select, enabled])\r\n  return <selectionContext.Provider value={value}>{children}</selectionContext.Provider>\r\n}\r\n\r\nexport function Select({ enabled = false, children, ...props }: SelectApi) {\r\n  const group = useRef<THREE.Group>(null!)\r\n  const api = useContext(selectionContext)\r\n  useEffect(() => {\r\n    if (api && enabled) {\r\n      let changed = false\r\n      const current: THREE.Object3D[] = []\r\n      group.current.traverse((o) => {\r\n        o.type === 'Mesh' && current.push(o)\r\n        if (api.selected.indexOf(o) === -1) changed = true\r\n      })\r\n      if (changed) {\r\n        api.select((state) => [...state, ...current])\r\n        return () => {\r\n          api.select((state) => state.filter((selected) => !current.includes(selected)))\r\n        }\r\n      }\r\n    }\r\n  }, [enabled, children, api])\r\n  return (\r\n    <group ref={group} {...props}>\r\n      {children}\r\n    </group>\r\n  )\r\n}\r\n", "import type { TextureDataType, Group, Camera, Scene } from 'three'\r\nimport { HalfFloatType, NoToneMapping } from 'three'\r\nimport {\r\n  type JSX,\r\n  memo,\r\n  forwardRef,\r\n  useMemo,\r\n  useEffect,\r\n  useLayoutEffect,\r\n  createContext,\r\n  useRef,\r\n  useImperativeHandle,\r\n} from 'react'\r\nimport { useThree, useFrame, type Instance } from '@react-three/fiber'\r\nimport {\r\n  EffectComposer as EffectComposerImpl,\r\n  RenderPass,\r\n  EffectPass,\r\n  NormalPass,\r\n  DepthDownsamplingPass,\r\n  Effect,\r\n  Pass,\r\n  EffectAttribute,\r\n} from 'postprocessing'\r\n\r\nexport const EffectComposerContext = /* @__PURE__ */ createContext<{\r\n  composer: EffectComposerImpl\r\n  normalPass: NormalPass | null\r\n  downSamplingPass: DepthDownsamplingPass | null\r\n  camera: Camera\r\n  scene: Scene\r\n  resolutionScale?: number\r\n}>(null!)\r\n\r\nexport type EffectComposerProps = {\r\n  enabled?: boolean\r\n  children: JSX.Element | JSX.Element[]\r\n  depthBuffer?: boolean\r\n  /** Only used for SSGI currently, leave it disabled for everything else unless it's needed */\r\n  enableNormalPass?: boolean\r\n  stencilBuffer?: boolean\r\n  autoClear?: boolean\r\n  resolutionScale?: number\r\n  multisampling?: number\r\n  frameBufferType?: TextureDataType\r\n  renderPriority?: number\r\n  camera?: Camera\r\n  scene?: Scene\r\n}\r\n\r\nconst isConvolution = (effect: Effect): boolean =>\r\n  (effect.getAttributes() & EffectAttribute.CONVOLUTION) === EffectAttribute.CONVOLUTION\r\n\r\nexport const EffectComposer = /* @__PURE__ */ memo(\r\n  /* @__PURE__ */ forwardRef<EffectComposerImpl, EffectComposerProps>(\r\n    (\r\n      {\r\n        children,\r\n        camera: _camera,\r\n        scene: _scene,\r\n        resolutionScale,\r\n        enabled = true,\r\n        renderPriority = 1,\r\n        autoClear = true,\r\n        depthBuffer,\r\n        enableNormalPass,\r\n        stencilBuffer,\r\n        multisampling = 8,\r\n        frameBufferType = HalfFloatType,\r\n      },\r\n      ref\r\n    ) => {\r\n      const { gl, scene: defaultScene, camera: defaultCamera, size } = useThree()\r\n      const scene = _scene || defaultScene\r\n      const camera = _camera || defaultCamera\r\n\r\n      const [composer, normalPass, downSamplingPass] = useMemo(() => {\r\n        // Initialize composer\r\n        const effectComposer = new EffectComposerImpl(gl, {\r\n          depthBuffer,\r\n          stencilBuffer,\r\n          multisampling,\r\n          frameBufferType,\r\n        })\r\n\r\n        // Add render pass\r\n        effectComposer.addPass(new RenderPass(scene, camera))\r\n\r\n        // Create normal pass\r\n        let downSamplingPass = null\r\n        let normalPass = null\r\n        if (enableNormalPass) {\r\n          normalPass = new NormalPass(scene, camera)\r\n          normalPass.enabled = false\r\n          effectComposer.addPass(normalPass)\r\n          if (resolutionScale !== undefined) {\r\n            downSamplingPass = new DepthDownsamplingPass({ normalBuffer: normalPass.texture, resolutionScale })\r\n            downSamplingPass.enabled = false\r\n            effectComposer.addPass(downSamplingPass)\r\n          }\r\n        }\r\n\r\n        return [effectComposer, normalPass, downSamplingPass]\r\n      }, [\r\n        camera,\r\n        gl,\r\n        depthBuffer,\r\n        stencilBuffer,\r\n        multisampling,\r\n        frameBufferType,\r\n        scene,\r\n        enableNormalPass,\r\n        resolutionScale,\r\n      ])\r\n\r\n      useEffect(() => composer?.setSize(size.width, size.height), [composer, size])\r\n      useFrame(\r\n        (_, delta) => {\r\n          if (enabled) {\r\n            const currentAutoClear = gl.autoClear\r\n            gl.autoClear = autoClear\r\n            if (stencilBuffer && !autoClear) gl.clearStencil()\r\n            composer.render(delta)\r\n            gl.autoClear = currentAutoClear\r\n          }\r\n        },\r\n        enabled ? renderPriority : 0\r\n      )\r\n\r\n      const group = useRef<Group>(null!)\r\n      useLayoutEffect(() => {\r\n        const passes: Pass[] = []\r\n\r\n        // TODO: rewrite all of this with R3F v9\r\n        const groupInstance = (group.current as Group & { __r3f: Instance<Group> }).__r3f\r\n\r\n        if (groupInstance && composer) {\r\n          const children = groupInstance.children\r\n\r\n          for (let i = 0; i < children.length; i++) {\r\n            const child = children[i].object\r\n\r\n            if (child instanceof Effect) {\r\n              const effects: Effect[] = [child]\r\n\r\n              if (!isConvolution(child)) {\r\n                let next: unknown = null\r\n                while ((next = children[i + 1]?.object) instanceof Effect) {\r\n                  if (isConvolution(next)) break\r\n                  effects.push(next)\r\n                  i++\r\n                }\r\n              }\r\n\r\n              const pass = new EffectPass(camera, ...effects)\r\n              passes.push(pass)\r\n            } else if (child instanceof Pass) {\r\n              passes.push(child)\r\n            }\r\n          }\r\n\r\n          for (const pass of passes) composer?.addPass(pass)\r\n\r\n          if (normalPass) normalPass.enabled = true\r\n          if (downSamplingPass) downSamplingPass.enabled = true\r\n        }\r\n\r\n        return () => {\r\n          for (const pass of passes) composer?.removePass(pass)\r\n          if (normalPass) normalPass.enabled = false\r\n          if (downSamplingPass) downSamplingPass.enabled = false\r\n        }\r\n      }, [composer, children, camera, normalPass, downSamplingPass])\r\n\r\n      // Disable tone mapping because threejs disallows tonemapping on render targets\r\n      useEffect(() => {\r\n        const currentTonemapping = gl.toneMapping\r\n        gl.toneMapping = NoToneMapping\r\n        return () => {\r\n          gl.toneMapping = currentTonemapping\r\n        }\r\n      }, [gl])\r\n\r\n      // Memoize state, otherwise it would trigger all consumers on every render\r\n      const state = useMemo(\r\n        () => ({ composer, normalPass, downSamplingPass, resolutionScale, camera, scene }),\r\n        [composer, normalPass, downSamplingPass, resolutionScale, camera, scene]\r\n      )\r\n\r\n      // Expose the composer\r\n      useImperativeHandle(ref, () => composer, [composer])\r\n\r\n      return (\r\n        <EffectComposerContext.Provider value={state}>\r\n          <group ref={group}>{children}</group>\r\n        </EffectComposerContext.Provider>\r\n      )\r\n    }\r\n  )\r\n)\r\n", "import React, { RefObject } from 'react'\r\nimport { Vector2 } from 'three'\r\nimport * as THREE from 'three'\r\nimport { type ReactThreeFiber, type ThreeElement, extend, useThree } from '@react-three/fiber'\r\nimport type { Effect, Pass, BlendFunction } from 'postprocessing'\r\n\r\nexport const resolveRef = <T,>(ref: T | React.RefObject<T>) =>\r\n  typeof ref === 'object' && ref != null && 'current' in ref ? ref.current : ref\r\n\r\nexport type EffectConstructor = new (...args: any[]) => Effect | Pass\r\n\r\nexport type EffectProps<T extends EffectConstructor> = ThreeElement<T> &\r\n  ConstructorParameters<T>[0] & {\r\n    blendFunction?: BlendFunction\r\n    opacity?: number\r\n  }\r\n\r\nlet i = 0\r\nconst components = new WeakMap<EffectConstructor, React.ExoticComponent<any> | string>()\r\n\r\nexport const wrapEffect = <T extends EffectConstructor>(effect: T, defaults?: EffectProps<T>) =>\r\n  /* @__PURE__ */ function Effect({ blendFunction = defaults?.blendFunction, opacity = defaults?.opacity, ...props }) {\r\n    let Component = components.get(effect)\r\n    if (!Component) {\r\n      const key = `@react-three/postprocessing/${effect.name}-${i++}`\r\n      extend({ [key]: effect })\r\n      components.set(effect, (Component = key))\r\n    }\r\n\r\n    const camera = useThree((state) => state.camera)\r\n    const args = React.useMemo(\r\n      () => [...(defaults?.args ?? []), ...(props.args ?? [{ ...defaults, ...props }])],\r\n      // eslint-disable-next-line react-hooks/exhaustive-deps\r\n      [JSON.stringify(props)]\r\n    )\r\n\r\n    return (\r\n      <Component\r\n        camera={camera}\r\n        blendMode-blendFunction={blendFunction}\r\n        blendMode-opacity-value={opacity}\r\n        {...props}\r\n        args={args}\r\n      />\r\n    )\r\n  }\r\n\r\nexport const useVector2 = (props: Record<string, unknown>, key: string): THREE.Vector2 => {\r\n  const value = props[key] as ReactThreeFiber.Vector2 | undefined\r\n  return React.useMemo(() => {\r\n    if (typeof value === 'number') return new THREE.Vector2(value, value)\r\n    else if (value) return new THREE.Vector2(...(value as THREE.Vector2Tuple))\r\n    else return new THREE.Vector2()\r\n  }, [value])\r\n}\r\n", "import { DepthOfFieldEffect, MaskFunction } from 'postprocessing'\r\nimport { Ref, forwardRef, useMemo, useEffect, useContext } from 'react'\r\nimport { ReactThreeFiber } from '@react-three/fiber'\r\nimport { type DepthPackingStrategies, type Texture, Vector3 } from 'three'\r\nimport { EffectComposerContext } from '../EffectComposer'\r\n\r\ntype DOFProps = ConstructorParameters<typeof DepthOfFieldEffect>[1] &\r\n  Partial<{\r\n    target: ReactThreeFiber.Vector3\r\n    depthTexture: {\r\n      texture: Texture\r\n      // TODO: narrow to DepthPackingStrategies\r\n      packing: number\r\n    }\r\n    // TODO: not used\r\n    blur: number\r\n  }>\r\n\r\nexport const DepthOfField = /* @__PURE__ */ forwardRef(function DepthOfField(\r\n  {\r\n    blendFunction,\r\n    worldFocusDistance,\r\n    worldFocusRange,\r\n    focusDistance,\r\n    focusRange,\r\n    focalLength,\r\n    bokehScale,\r\n    resolutionScale,\r\n    resolutionX,\r\n    resolutionY,\r\n    width,\r\n    height,\r\n    target,\r\n    depthTexture,\r\n    ...props\r\n  }: DOFProps,\r\n  ref: Ref<DepthOfFieldEffect>\r\n) {\r\n  const { camera } = useContext(EffectComposerContext)\r\n  const autoFocus = target != null\r\n  const effect = useMemo(() => {\r\n    const effect = new DepthOfFieldEffect(camera, {\r\n      blendFunction,\r\n      worldFocusDistance,\r\n      worldFocusRange,\r\n      focusDistance,\r\n      focusRange,\r\n      focalLength,\r\n      bokehScale,\r\n      resolutionScale,\r\n      resolutionX,\r\n      resolutionY,\r\n      width,\r\n      height,\r\n    })\r\n    // Creating a target enables autofocus, R3F will set via props\r\n    if (autoFocus) effect.target = new Vector3()\r\n    // Depth texture for depth picking with optional packing strategy\r\n    if (depthTexture) effect.setDepthTexture(depthTexture.texture, depthTexture.packing as DepthPackingStrategies)\r\n    // Temporary fix that restores DOF 6.21.3 behavior, everything since then lets shapes leak through the blur\r\n    const maskPass = (effect as any).maskPass\r\n    maskPass.maskFunction = MaskFunction.MULTIPLY_RGB_SET_ALPHA\r\n    return effect\r\n  }, [\r\n    camera,\r\n    blendFunction,\r\n    worldFocusDistance,\r\n    worldFocusRange,\r\n    focusDistance,\r\n    focusRange,\r\n    focalLength,\r\n    bokehScale,\r\n    resolutionScale,\r\n    resolutionX,\r\n    resolutionY,\r\n    width,\r\n    height,\r\n    autoFocus,\r\n    depthTexture,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      effect.dispose()\r\n    }\r\n  }, [effect])\r\n\r\n  return <primitive {...props} ref={ref} object={effect} target={target} />\r\n})\r\n", "import * as THREE from 'three'\r\nimport React, {\r\n  useRef,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  useCallback,\r\n  forwardRef,\r\n  useImperativeHandle,\r\n  RefObject,\r\n  useMemo,\r\n} from 'react'\r\nimport { useThree, useFrame, createPortal, type Vector3 } from '@react-three/fiber'\r\nimport { CopyPass, DepthPickingPass, DepthOfFieldEffect } from 'postprocessing'\r\nimport { easing } from 'maath'\r\n\r\nimport { DepthOfField } from './DepthOfField'\r\nimport { EffectComposerContext } from '../EffectComposer'\r\n\r\nexport type AutofocusProps = React.ComponentProps<typeof DepthOfField> & {\r\n  target?: Vector3\r\n  /** should the target follow the pointer */\r\n  mouse?: boolean\r\n  /** size of the debug green point  */\r\n  debug?: number\r\n  /** manual update */\r\n  manual?: boolean\r\n  /** approximate time to reach the target */\r\n  smoothTime?: number\r\n}\r\n\r\nexport type AutofocusApi = {\r\n  dofRef: RefObject<DepthOfFieldEffect | null>\r\n  hitpoint: THREE.Vector3\r\n  update: (delta: number, updateTarget: boolean) => void\r\n}\r\n\r\nexport const Autofocus = /* @__PURE__ */ forwardRef<AutofocusApi, AutofocusProps>(\r\n  (\r\n    { target = undefined, mouse: followMouse = false, debug = undefined, manual = false, smoothTime = 0.25, ...props },\r\n    fref\r\n  ) => {\r\n    const dofRef = useRef<DepthOfFieldEffect>(null)\r\n    const hitpointRef = useRef<THREE.Mesh>(null)\r\n    const targetRef = useRef<THREE.Mesh>(null)\r\n\r\n    const scene = useThree(({ scene }) => scene)\r\n    const pointer = useThree(({ pointer }) => pointer)\r\n    const { composer, camera } = useContext(EffectComposerContext)\r\n\r\n    // see: https://codesandbox.io/s/depthpickingpass-x130hg\r\n    const [depthPickingPass] = useState(() => new DepthPickingPass())\r\n    const [copyPass] = useState(() => new CopyPass())\r\n    useEffect(() => {\r\n      composer.addPass(depthPickingPass)\r\n      composer.addPass(copyPass)\r\n      return () => {\r\n        composer.removePass(depthPickingPass)\r\n        composer.removePass(copyPass)\r\n      }\r\n    }, [composer, depthPickingPass, copyPass])\r\n\r\n    useEffect(() => {\r\n      return () => {\r\n        depthPickingPass.dispose()\r\n        copyPass.dispose()\r\n      }\r\n    }, [depthPickingPass, copyPass])\r\n\r\n    const [hitpoint] = useState(() => new THREE.Vector3(0, 0, 0))\r\n\r\n    const [ndc] = useState(() => new THREE.Vector3(0, 0, 0))\r\n    const getHit = useCallback(\r\n      async (x: number, y: number) => {\r\n        ndc.x = x\r\n        ndc.y = y\r\n        ndc.z = await depthPickingPass.readDepth(ndc)\r\n        ndc.z = ndc.z * 2.0 - 1.0\r\n        const hit = 1 - ndc.z > 0.0000001 // it is missed if ndc.z is close to 1\r\n        return hit ? ndc.unproject(camera) : false\r\n      },\r\n      [ndc, depthPickingPass, camera]\r\n    )\r\n\r\n    const update = useCallback(\r\n      async (delta: number, updateTarget = true) => {\r\n        // Update hitpoint\r\n        if (target) {\r\n          hitpoint.set(...(target as [number, number, number]))\r\n        } else {\r\n          const { x, y } = followMouse ? pointer : { x: 0, y: 0 }\r\n          const hit = await getHit(x, y)\r\n          if (hit) hitpoint.copy(hit)\r\n        }\r\n\r\n        // Update target\r\n        if (updateTarget && dofRef.current?.target) {\r\n          if (smoothTime > 0 && delta > 0) {\r\n            easing.damp3(dofRef.current.target, hitpoint, smoothTime, delta)\r\n          } else {\r\n            dofRef.current.target.copy(hitpoint)\r\n          }\r\n        }\r\n      },\r\n      [target, hitpoint, followMouse, getHit, smoothTime, pointer]\r\n    )\r\n\r\n    useFrame(async (_, delta) => {\r\n      if (!manual) {\r\n        update(delta)\r\n      }\r\n      if (hitpointRef.current) {\r\n        hitpointRef.current.position.copy(hitpoint)\r\n      }\r\n      if (targetRef.current && dofRef.current?.target) {\r\n        targetRef.current.position.copy(dofRef.current.target)\r\n      }\r\n    })\r\n\r\n    // Ref API\r\n    const api = useMemo<AutofocusApi>(\r\n      () => ({\r\n        dofRef,\r\n        hitpoint,\r\n        update,\r\n      }),\r\n      [hitpoint, update]\r\n    )\r\n    useImperativeHandle(fref, () => api, [api])\r\n\r\n    return (\r\n      <>\r\n        {debug\r\n          ? createPortal(\r\n              <>\r\n                <mesh ref={hitpointRef}>\r\n                  <sphereGeometry args={[debug, 16, 16]} />\r\n                  <meshBasicMaterial color=\"#00ff00\" opacity={1} transparent depthWrite={false} />\r\n                </mesh>\r\n                <mesh ref={targetRef}>\r\n                  <sphereGeometry args={[debug / 2, 16, 16]} />\r\n                  <meshBasicMaterial color=\"#00ff00\" opacity={0.5} transparent depthWrite={false} />\r\n                </mesh>\r\n              </>,\r\n              scene\r\n            )\r\n          : null}\r\n\r\n        <DepthOfField ref={dofRef} {...props} target={hitpoint} />\r\n      </>\r\n    )\r\n  }\r\n)\r\n", "// Created by <PERSON> 2023\r\n// From https://github.com/ektogamat/R3F-Ultimate-Lens-Flare\r\n\r\nimport * as THREE from 'three'\r\nimport React, { useEffect, useState, useContext, useRef } from 'react'\r\nimport { useFrame, useThree } from '@react-three/fiber'\r\nimport { BlendFunction, Effect } from 'postprocessing'\r\nimport { easing } from 'maath'\r\n\r\nimport { EffectComposerContext } from '../EffectComposer'\r\nimport { wrapEffect } from '../util'\r\n\r\nconst LensFlareShader = {\r\n  fragmentShader: /* glsl */ `\r\n    uniform float time;\r\n    uniform vec2 lensPosition;\r\n    uniform vec2 screenRes;\r\n    uniform vec3 colorGain;\r\n    uniform float starPoints;\r\n    uniform float glareSize;\r\n    uniform float flareSize;\r\n    uniform float flareSpeed;\r\n    uniform float flareShape;\r\n    uniform float haloScale;\r\n    uniform float opacity;\r\n    uniform bool animated;\r\n    uniform bool anamorphic;\r\n    uniform bool enabled;\r\n    uniform bool secondaryGhosts;\r\n    uniform bool starBurst;\r\n    uniform float ghostScale;\r\n    uniform bool aditionalStreaks;\r\n    uniform sampler2D lensDirtTexture;\r\n    vec2 vTexCoord;\r\n    \r\n    float rand(float n){return fract(sin(n) * 43758.5453123);}\r\n\r\n    float noise(float p){\r\n      float fl = floor(p);\r\n      float fc = fract(p);\r\n      return mix(rand(fl),rand(fl + 1.0), fc);\r\n    }\r\n\r\n    vec3 hsv2rgb(vec3 c)\r\n    {\r\n      vec4 k = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\r\n      vec3 p = abs(fract(c.xxx + k.xyz) * 6.0 - k.www);\r\n      return c.z * mix(k.xxx, clamp(p - k.xxx, 0.0, 1.0), c.y);\r\n    }\r\n\r\n    float saturate(float x)\r\n    {\r\n      return clamp(x, 0.,1.);\r\n    }\r\n\r\n    vec2 rotateUV(vec2 uv, float rotation)\r\n    {\r\n      return vec2(\r\n          cos(rotation) * uv.x + sin(rotation) * uv.y,\r\n          cos(rotation) * uv.y - sin(rotation) * uv.x\r\n      );\r\n    }\r\n\r\n    // Based on https://www.shadertoy.com/view/XtKfRV\r\n    vec3 drawflare(vec2 p, float intensity, float rnd, float speed, int id)\r\n    {\r\n      float flarehueoffset = (1. / 32.) * float(id) * 0.1;\r\n      float lingrad = distance(vec2(0.), p);\r\n      float expgrad = 1. / exp(lingrad * (fract(rnd) * 0.66 + 0.33));\r\n      vec3 colgrad = hsv2rgb(vec3( fract( (expgrad * 8.) + speed * flareSpeed + flarehueoffset), pow(1.-abs(expgrad*2.-1.), 0.45), 20.0 * expgrad * intensity)); //rainbow spectrum effect\r\n\r\n      float internalStarPoints;\r\n\r\n      if(anamorphic){\r\n        internalStarPoints = 1.0;\r\n      } else{\r\n        internalStarPoints = starPoints;\r\n      }\r\n      \r\n      float blades = length(p * flareShape * sin(internalStarPoints * atan(p.x, p.y)));\r\n      \r\n      float comp = pow(1.-saturate(blades), ( anamorphic ? 100. : 12.));\r\n      comp += saturate(expgrad-0.9) * 3.;\r\n      comp = pow(comp * expgrad, 8. + (1.-intensity) * 5.);\r\n      \r\n      if(flareSpeed > 0.0){\r\n        return vec3(comp) * colgrad;\r\n      } else{\r\n        return vec3(comp) * flareSize * 15.;\r\n      }\r\n    }\r\n\r\n    float dist(vec3 a, vec3 b) { return abs(a.x - b.x) + abs(a.y - b.y) + abs(a.z - b.z); }\r\n\r\n    vec3 saturate(vec3 x)\r\n    {\r\n      return clamp(x, vec3(0.0), vec3(1.0));\r\n    }\r\n\r\n    // Based on https://www.shadertoy.com/view/XtKfRV\r\n    float glare(vec2 uv, vec2 pos, float size)\r\n    {\r\n      vec2 main;\r\n\r\n      if(animated){\r\n        main = rotateUV(uv-pos, time * 0.1);      \r\n      } else{\r\n        main = uv-pos;     \r\n      }\r\n      \r\n      float ang = atan(main.y, main.x) * (anamorphic ? 1.0 : starPoints);\r\n      float dist = length(main); \r\n      dist = pow(dist, .9);\r\n      \r\n      float f0 = 1.0/(length(uv-pos)*(1.0/size*16.0)+.2);\r\n\r\n      return f0+f0*(sin((ang))*.2 +.3);\r\n    }\r\n\r\n    float sdHex(vec2 p){\r\n      p = abs(p);\r\n      vec2 q = vec2(p.x*2.0*0.5773503, p.y + p.x*0.5773503);\r\n      return dot(step(q.xy,q.yx), 1.0-q.yx);\r\n    }\r\n\r\n    //Based on https://www.shadertoy.com/view/dllSRX\r\n    float fpow(float x, float k){\r\n      return x > k ? pow((x-k)/(1.0-k),2.0) : 0.0;\r\n    }\r\n\r\n    vec3 renderhex(vec2 uv, vec2 p, float s, vec3 col){\r\n      uv -= p;\r\n      if (abs(uv.x) < 0.2*s && abs(uv.y) < 0.2*s){\r\n          return mix(vec3(0),mix(vec3(0),col,0.1 + fpow(length(uv/s),0.1)*10.0),smoothstep(0.0,0.1,sdHex(uv*20.0/s)));\r\n      }\r\n      return vec3(0);\r\n    }\r\n\r\n    // Based on https://www.shadertoy.com/view/4sX3Rs\r\n    vec3 LensFlare(vec2 uv, vec2 pos)\r\n    {\r\n      vec2 main = uv-pos;\r\n      vec2 uvd = uv*(length(uv));\r\n      \r\n      float ang = atan(main.x,main.y);\r\n      \r\n      float f0 = .3/(length(uv-pos)*16.0+1.0);\r\n      \r\n      f0 = f0*(sin(noise(sin(ang*3.9-(animated ? time : 0.0) * 0.3) * starPoints))*.2 );\r\n      \r\n      float f1 = max(0.01-pow(length(uv+1.2*pos),1.9),.0)*7.0;\r\n\r\n      float f2 = max(.9/(10.0+32.0*pow(length(uvd+0.99*pos),2.0)),.0)*0.35;\r\n      float f22 = max(.9/(11.0+32.0*pow(length(uvd+0.85*pos),2.0)),.0)*0.23;\r\n      float f23 = max(.9/(12.0+32.0*pow(length(uvd+0.95*pos),2.0)),.0)*0.6;\r\n      \r\n      vec2 uvx = mix(uv,uvd, 0.1);\r\n      \r\n      float f4 = max(0.01-pow(length(uvx+0.4*pos),2.9),.0)*4.02;\r\n      float f42 = max(0.0-pow(length(uvx+0.45*pos),2.9),.0)*4.1;\r\n      float f43 = max(0.01-pow(length(uvx+0.5*pos),2.9),.0)*4.6;\r\n      \r\n      uvx = mix(uv,uvd,-.4);\r\n      \r\n      float f5 = max(0.01-pow(length(uvx+0.1*pos),5.5),.0)*2.0;\r\n      float f52 = max(0.01-pow(length(uvx+0.2*pos),5.5),.0)*2.0;\r\n      float f53 = max(0.01-pow(length(uvx+0.1*pos),5.5),.0)*2.0;\r\n      \r\n      uvx = mix(uv,uvd, 2.1);\r\n      \r\n      float f6 = max(0.01-pow(length(uvx-0.3*pos),1.61),.0)*3.159;\r\n      float f62 = max(0.01-pow(length(uvx-0.325*pos),1.614),.0)*3.14;\r\n      float f63 = max(0.01-pow(length(uvx-0.389*pos),1.623),.0)*3.12;\r\n      \r\n      vec3 c = vec3(glare(uv,pos, glareSize));\r\n\r\n      vec2 prot;\r\n\r\n      if(animated){\r\n        prot = rotateUV(uv - pos, (time * 0.1));  \r\n      } else if(anamorphic){\r\n        prot = rotateUV(uv - pos, 1.570796);     \r\n      } else {\r\n        prot = uv - pos;\r\n      }\r\n\r\n      c += drawflare(prot, (anamorphic ? flareSize * 10. : flareSize), 0.1, time, 1);\r\n      \r\n      c.r+=f1+f2+f4+f5+f6; c.g+=f1+f22+f42+f52+f62; c.b+=f1+f23+f43+f53+f63;\r\n      c = c*1.3 * vec3(length(uvd)+.09);\r\n      c+=vec3(f0);\r\n      \r\n      return c;\r\n    }\r\n\r\n    vec3 cc(vec3 color, float factor,float factor2)\r\n    {\r\n      float w = color.x+color.y+color.z;\r\n      return mix(color,vec3(w)*factor,w*factor2);\r\n    }    \r\n\r\n    float rnd(vec2 p)\r\n    {\r\n      float f = fract(sin(dot(p, vec2(12.1234, 72.8392) )*45123.2));\r\n      return f;   \r\n    }\r\n\r\n    float rnd(float w)\r\n    {\r\n      float f = fract(sin(w)*1000.);\r\n      return f;   \r\n    }\r\n\r\n    float regShape(vec2 p, int N)\r\n    {\r\n      float f;\r\n      \r\n      float a=atan(p.x,p.y)+.2;\r\n      float b=6.28319/float(N);\r\n      f=smoothstep(.5,.51, cos(floor(.5+a/b)*b-a)*length(p.xy)* 2.0  -ghostScale);\r\n          \r\n      return f;\r\n    }\r\n\r\n    // Based on https://www.shadertoy.com/view/Xlc3D2\r\n    vec3 circle(vec2 p, float size, float decay, vec3 color, vec3 color2, float dist, vec2 position)\r\n    {\r\n      float l = length(p + position*(dist*2.))+size/2.;\r\n      float l2 = length(p + position*(dist*4.))+size/3.;\r\n      \r\n      float c = max(0.01-pow(length(p + position*dist), size*ghostScale), 0.0)*10.;\r\n      float c1 = max(0.001-pow(l-0.3, 1./40.)+sin(l*20.), 0.0)*3.;\r\n      float c2 =  max(0.09/pow(length(p-position*dist/.5)*1., .95), 0.0)/20.;\r\n      float s = max(0.02-pow(regShape(p*5. + position*dist*5. + decay, 6) , 1.), 0.0)*1.5;\r\n      \r\n      color = cos(vec3(0.44, .24, .2)*16. + dist/8.)*0.5+.5;\r\n      vec3 f = c*color;\r\n      f += c1*color;\r\n      f += c2*color;  \r\n      f +=  s*color;\r\n      return f;\r\n    }\r\n\r\n    vec4 getLensColor(float x){\r\n      return vec4(vec3(mix(mix(mix(mix(mix(mix(mix(mix(mix(mix(mix(mix(mix(mix(mix(vec3(0., 0., 0.),\r\n        vec3(0., 0., 0.), smoothstep(0.0, 0.063, x)),\r\n        vec3(0., 0., 0.), smoothstep(0.063, 0.125, x)),\r\n        vec3(0.0, 0., 0.), smoothstep(0.125, 0.188, x)),\r\n        vec3(0.188, 0.131, 0.116), smoothstep(0.188, 0.227, x)),\r\n        vec3(0.31, 0.204, 0.537), smoothstep(0.227, 0.251, x)),\r\n        vec3(0.192, 0.106, 0.286), smoothstep(0.251, 0.314, x)),\r\n        vec3(0.102, 0.008, 0.341), smoothstep(0.314, 0.392, x)),\r\n        vec3(0.086, 0.0, 0.141), smoothstep(0.392, 0.502, x)),\r\n        vec3(1.0, 0.31, 0.0), smoothstep(0.502, 0.604, x)),\r\n        vec3(.1, 0.1, 0.1), smoothstep(0.604, 0.643, x)),\r\n        vec3(1.0, 0.929, 0.0), smoothstep(0.643, 0.761, x)),\r\n        vec3(1.0, 0.086, 0.424), smoothstep(0.761, 0.847, x)),\r\n        vec3(1.0, 0.49, 0.0), smoothstep(0.847, 0.89, x)),\r\n        vec3(0.945, 0.275, 0.475), smoothstep(0.89, 0.941, x)),\r\n        vec3(0.251, 0.275, 0.796), smoothstep(0.941, 1.0, x))),\r\n      1.0);\r\n    }\r\n\r\n    float dirtNoise(vec2 p){\r\n      vec2 f = fract(p);\r\n      f = (f * f) * (3.0 - (2.0 * f));    \r\n      float n = dot(floor(p), vec2(1.0, 157.0));\r\n      vec4 a = fract(sin(vec4(n + 0.0, n + 1.0, n + 157.0, n + 158.0)) * 43758.5453123);\r\n      return mix(mix(a.x, a.y, f.x), mix(a.z, a.w, f.x), f.y);\r\n    } \r\n\r\n    float fbm(vec2 p){\r\n      const mat2 m = mat2(0.80, -0.60, 0.60, 0.80);\r\n      float f = 0.0;\r\n      f += 0.5000*dirtNoise(p); p = m*p*2.02;\r\n      f += 0.2500*dirtNoise(p); p = m*p*2.03;\r\n      f += 0.1250*dirtNoise(p); p = m*p*2.01;\r\n      f += 0.0625*dirtNoise(p);\r\n      return f/0.9375;\r\n    }\r\n\r\n    vec4 getLensStar(vec2 p){\r\n      vec2 pp = (p - vec2(0.5)) * 2.0;\r\n      float a = atan(pp.y, pp.x);\r\n      vec4 cp = vec4(sin(a * 1.0), length(pp), sin(a * 13.0), sin(a * 53.0));\r\n      float d = sin(clamp(pow(length(vec2(0.5) - p) * 0.5 + haloScale /2., 5.0), 0.0, 1.0) * 3.14159);\r\n      vec3 c = vec3(d) * vec3(fbm(cp.xy * 16.0) * fbm(cp.zw * 9.0) * max(max(max(max(0.5, sin(a * 1.0)), sin(a * 3.0) * 0.8), sin(a * 7.0) * 0.8), sin(a * 9.0) * 10.6));\r\n      c *= vec3(mix(2.0, (sin(length(pp.xy) * 256.0) * 0.5) + 0.5, sin((clamp((length(pp.xy) - 0.875) / 0.1, 0.0, 1.0) + 0.0) * 2.0 * 3.14159) * 1.5) + 0.5) * 0.3275;\r\n      return vec4(vec3(c * 1.0), d);\t\r\n    }\r\n\r\n    vec4 getLensDirt(vec2 p){\r\n      p.xy += vec2(fbm(p.yx * 3.0), fbm(p.yx * 2.0)) * 0.0825;\r\n      vec3 o = vec3(mix(0.125, 0.25, max(max(smoothstep(0.1, 0.0, length(p - vec2(0.25))),\r\n                                            smoothstep(0.4, 0.0, length(p - vec2(0.75)))),\r\n                                            smoothstep(0.8, 0.0, length(p - vec2(0.875, 0.125))))));\r\n      o += vec3(max(fbm(p * 1.0) - 0.5, 0.0)) * 0.5;\r\n      o += vec3(max(fbm(p * 2.0) - 0.5, 0.0)) * 0.5;\r\n      o += vec3(max(fbm(p * 4.0) - 0.5, 0.0)) * 0.25;\r\n      o += vec3(max(fbm(p * 8.0) - 0.75, 0.0)) * 1.0;\r\n      o += vec3(max(fbm(p * 16.0) - 0.75, 0.0)) * 0.75;\r\n      o += vec3(max(fbm(p * 64.0) - 0.75, 0.0)) * 0.5;\r\n      return vec4(clamp(o, vec3(0.15), vec3(1.0)), 1.0);\t\r\n    }\r\n\r\n    vec4 textureLimited(sampler2D tex, vec2 texCoord){\r\n      if(((texCoord.x < 0.) || (texCoord.y < 0.)) || ((texCoord.x > 1.) || (texCoord.y > 1.))){\r\n        return vec4(0.0);\r\n      }else{\r\n        return texture(tex, texCoord); \r\n      }\r\n    }\r\n\r\n    vec4 textureDistorted(sampler2D tex, vec2 texCoord, vec2 direction, vec3 distortion) {\r\n      return vec4(textureLimited(tex, (texCoord + (direction * distortion.r))).r,\r\n                  textureLimited(tex, (texCoord + (direction * distortion.g))).g,\r\n                  textureLimited(tex, (texCoord + (direction * distortion.b))).b,\r\n                  1.0);\r\n    }\r\n\r\n    // Based on https://www.shadertoy.com/view/4sK3W3\r\n    vec4 getStartBurst(){\r\n      vec2 aspectTexCoord = vec2(1.0) - (((vTexCoord - vec2(0.5)) * vec2(1.0)) + vec2(0.5)); \r\n      vec2 texCoord = vec2(1.0) - vTexCoord; \r\n      vec2 ghostVec = (vec2(0.5) - texCoord) * 0.3 - lensPosition;\r\n      vec2 ghostVecAspectNormalized = normalize(ghostVec * vec2(1.0)) * vec2(1.0);\r\n      vec2 haloVec = normalize(ghostVec) * 0.6;\r\n      vec2 haloVecAspectNormalized = ghostVecAspectNormalized * 0.6;\r\n      vec2 texelSize = vec2(1.0) / vec2(screenRes.xy);\r\n      vec3 distortion = vec3(-(texelSize.x * 1.5), 0.2, texelSize.x * 1.5);\r\n      vec4 c = vec4(0.0);\r\n      for (int i = 0; i < 8; i++) {\r\n        vec2 offset = texCoord + (ghostVec * float(i));\r\n        c += textureDistorted(lensDirtTexture, offset, ghostVecAspectNormalized, distortion) * pow(max(0.0, 1.0 - (length(vec2(0.5) - offset) / length(vec2(0.5)))), 10.0);\r\n      }                       \r\n      vec2 haloOffset = texCoord + haloVecAspectNormalized; \r\n      return (c * getLensColor((length(vec2(0.5) - aspectTexCoord) / length(vec2(haloScale))))) + \r\n            (textureDistorted(lensDirtTexture, haloOffset, ghostVecAspectNormalized, distortion) * pow(max(0.0, 1.0 - (length(vec2(0.5) - haloOffset) / length(vec2(0.5)))), 10.0));\r\n    } \r\n\r\n    void mainImage(vec4 inputColor, vec2 uv, out vec4 outputColor)\r\n    {\r\n      vec2 myUV = uv -0.5;\r\n      myUV.y *= screenRes.y/screenRes.x;\r\n      vec2 finalLensPosition = lensPosition * 0.5;\r\n      finalLensPosition.y *= screenRes.y/screenRes.x;\r\n      \r\n      //First Lens flare pass\r\n      vec3 finalColor = LensFlare(myUV, finalLensPosition) * 20.0 * colorGain / 256.;\r\n\r\n      //Aditional streaks\r\n      if(aditionalStreaks){\r\n        vec3 circColor = vec3(0.9, 0.2, 0.1);\r\n        vec3 circColor2 = vec3(0.3, 0.1, 0.9);\r\n\r\n        for(float i=0.;i<10.;i++){\r\n          finalColor += circle(myUV, pow(rnd(i*2000.)*2.8, .1)+1.41, 0.0, circColor+i , circColor2+i, rnd(i*20.)*3.+0.2-.5, lensPosition);\r\n        }\r\n      }\r\n\r\n      //Alternative ghosts\r\n      if(secondaryGhosts){\r\n        vec3 altGhosts = vec3(0);\r\n        altGhosts += renderhex(myUV, -lensPosition*0.25, ghostScale * 1.4, vec3(0.25,0.35,0));\r\n        altGhosts += renderhex(myUV, lensPosition*0.25, ghostScale * 0.5, vec3(1,0.5,0.5));\r\n        altGhosts += renderhex(myUV, lensPosition*0.1, ghostScale * 1.6, vec3(1,1,1));\r\n        altGhosts += renderhex(myUV, lensPosition*1.8, ghostScale * 2.0, vec3(0,0.5,0.75));\r\n        altGhosts += renderhex(myUV, lensPosition*1.25, ghostScale * 0.8, vec3(1,1,0.5));\r\n        altGhosts += renderhex(myUV, -lensPosition*1.25, ghostScale * 5.0, vec3(0.5,0.5,0.25));\r\n        \r\n        //Circular ghosts\r\n        altGhosts += fpow(1.0 - abs(distance(lensPosition*0.8,myUV) - 0.7),0.985)*colorGain / 2100.;\r\n        finalColor += altGhosts;\r\n      }\r\n      \r\n\r\n      //Starburst                     \r\n      if(starBurst){\r\n        vTexCoord = myUV + 0.5;\r\n        vec4 lensMod = getLensDirt(myUV);\r\n        float tooBright = 1.0 - (clamp(0.5, 0.0, 0.5) * 2.0); \r\n        float tooDark = clamp(0.5 - 0.5, 0.0, 0.5) * 2.0;\r\n        lensMod += mix(lensMod, pow(lensMod * 2.0, vec4(2.0)) * 0.5, tooBright);\r\n        float lensStarRotationAngle = ((myUV.x + myUV.y)) * (1.0 / 6.0);\r\n        vec2 lensStarTexCoord = (mat2(cos(lensStarRotationAngle), -sin(lensStarRotationAngle), sin(lensStarRotationAngle), cos(lensStarRotationAngle)) * vTexCoord);\r\n        lensMod += getLensStar(lensStarTexCoord) * 2.;\r\n        \r\n        finalColor += clamp((lensMod.rgb * getStartBurst().rgb ), 0.01, 1.0);\r\n      }\r\n\r\n      //Final composed output\r\n      if(enabled){\r\n        outputColor = vec4(mix(finalColor, vec3(.0), opacity) + inputColor.rgb, inputColor.a);\r\n      } else {\r\n        outputColor = vec4(inputColor);\r\n      }\r\n    }\r\n  `,\r\n}\r\n\r\ntype LensFlareEffectOptions = {\r\n  /** The blend function of this effect */\r\n  blendFunction: BlendFunction\r\n  /** Boolean to enable/disable the effect */\r\n  enabled: boolean\r\n  /** The glare size */\r\n  glareSize: number\r\n  /** The position of the lens flare in 3d space */\r\n  lensPosition: THREE.Vector3\r\n  /** Effect resolution */\r\n  screenRes: THREE.Vector2\r\n  /** The number of points for the star */\r\n  starPoints: number\r\n  /** The flare side */\r\n  flareSize: number\r\n  /** The flare animation speed */\r\n  flareSpeed: number\r\n  /** Changes the appearance to anamorphic */\r\n  flareShape: number\r\n  /** Animated flare */\r\n  animated: boolean\r\n  /** Set the appearance to full anamorphic */\r\n  anamorphic: boolean\r\n  /** Set the color gain for the lens flare. Must be a THREE.Color in RBG format */\r\n  colorGain: THREE.Color\r\n  /** Texture to be used as color dirt for starburst effect */\r\n  lensDirtTexture: THREE.Texture | null\r\n  /** The halo scale */\r\n  haloScale: number\r\n  /** Option to enable/disable secondary ghosts */\r\n  secondaryGhosts: boolean\r\n  /** Option to enable/disable aditional streaks */\r\n  aditionalStreaks: boolean\r\n  /** Option to enable/disable secondary ghosts */\r\n  ghostScale: number\r\n  /** TODO The opacity for this effect */\r\n  opacity: number\r\n  /** Boolean to enable/disable the start burst effect. Can be disabled to improve performance */\r\n  starBurst: boolean\r\n}\r\n\r\nexport class LensFlareEffect extends Effect {\r\n  constructor({\r\n    blendFunction,\r\n    enabled,\r\n    glareSize,\r\n    lensPosition,\r\n    screenRes,\r\n    starPoints,\r\n    flareSize,\r\n    flareSpeed,\r\n    flareShape,\r\n    animated,\r\n    anamorphic,\r\n    colorGain,\r\n    lensDirtTexture,\r\n    haloScale,\r\n    secondaryGhosts,\r\n    aditionalStreaks,\r\n    ghostScale,\r\n    opacity,\r\n    starBurst,\r\n  }: LensFlareEffectOptions) {\r\n    super('LensFlareEffect', LensFlareShader.fragmentShader, {\r\n      blendFunction,\r\n      uniforms: new Map<string, THREE.Uniform>([\r\n        ['enabled', new THREE.Uniform(enabled)],\r\n        ['glareSize', new THREE.Uniform(glareSize)],\r\n        ['lensPosition', new THREE.Uniform(lensPosition)],\r\n        ['time', new THREE.Uniform(0)],\r\n        ['screenRes', new THREE.Uniform(screenRes)],\r\n        ['starPoints', new THREE.Uniform(starPoints)],\r\n        ['flareSize', new THREE.Uniform(flareSize)],\r\n        ['flareSpeed', new THREE.Uniform(flareSpeed)],\r\n        ['flareShape', new THREE.Uniform(flareShape)],\r\n        ['animated', new THREE.Uniform(animated)],\r\n        ['anamorphic', new THREE.Uniform(anamorphic)],\r\n        ['colorGain', new THREE.Uniform(colorGain)],\r\n        ['lensDirtTexture', new THREE.Uniform(lensDirtTexture)],\r\n        ['haloScale', new THREE.Uniform(haloScale)],\r\n        ['secondaryGhosts', new THREE.Uniform(secondaryGhosts)],\r\n        ['aditionalStreaks', new THREE.Uniform(aditionalStreaks)],\r\n        ['ghostScale', new THREE.Uniform(ghostScale)],\r\n        ['starBurst', new THREE.Uniform(starBurst)],\r\n        ['opacity', new THREE.Uniform(opacity)],\r\n      ]),\r\n    })\r\n  }\r\n\r\n  update(_renderer: any, _inputBuffer: any, deltaTime: number) {\r\n    const time = this.uniforms.get('time')\r\n    if (time) {\r\n      time.value += deltaTime\r\n    }\r\n  }\r\n}\r\n\r\ntype LensFlareProps = {\r\n  /** Position of the effect */\r\n  lensPosition?: THREE.Vector3\r\n  /** The time that it takes to fade the occlusion */\r\n  smoothTime?: number\r\n} & Partial<LensFlareEffectOptions>\r\n\r\nconst LensFlareWrapped = /* @__PURE__ */ wrapEffect(LensFlareEffect)\r\n\r\nexport const LensFlare = ({\r\n  smoothTime = 0.07,\r\n  //\r\n  blendFunction = BlendFunction.NORMAL,\r\n  enabled = true,\r\n  glareSize = 0.2,\r\n  lensPosition = new THREE.Vector3(-25, 6, -60),\r\n  screenRes = new THREE.Vector2(0, 0),\r\n  starPoints = 6,\r\n  flareSize = 0.01,\r\n  flareSpeed = 0.01,\r\n  flareShape = 0.01,\r\n  animated = true,\r\n  anamorphic = false,\r\n  colorGain = new THREE.Color(20, 20, 20),\r\n  lensDirtTexture = null,\r\n  haloScale = 0.5,\r\n  secondaryGhosts = true,\r\n  aditionalStreaks = true,\r\n  ghostScale = 0.0,\r\n  opacity = 1.0,\r\n  starBurst = false,\r\n}: LensFlareProps) => {\r\n  const viewport = useThree(({ viewport }) => viewport)\r\n  const raycaster = useThree(({ raycaster }) => raycaster)\r\n  const { scene, camera } = useContext(EffectComposerContext)\r\n  const [raycasterPos] = useState(() => new THREE.Vector2())\r\n  const [projectedPosition] = useState(() => new THREE.Vector3())\r\n\r\n  const ref = useRef<LensFlareEffect>(null)\r\n\r\n  useFrame((_, delta) => {\r\n    if (!ref?.current) return\r\n    const uLensPosition = ref.current.uniforms.get('lensPosition')\r\n    const uOpacity = ref.current.uniforms.get('opacity')\r\n    if (!uLensPosition || !uOpacity) return\r\n\r\n    let target = 1\r\n\r\n    projectedPosition.copy(lensPosition).project(camera)\r\n    if (projectedPosition.z > 1) return\r\n\r\n    uLensPosition.value.x = projectedPosition.x\r\n    uLensPosition.value.y = projectedPosition.y\r\n    raycasterPos.x = projectedPosition.x\r\n    raycasterPos.y = projectedPosition.y\r\n    raycaster.setFromCamera(raycasterPos, camera)\r\n\r\n    const intersects = raycaster.intersectObjects(scene.children, true)\r\n    const { object } = intersects[0] || {}\r\n    if (object) {\r\n      if (object.userData?.lensflare === 'no-occlusion') {\r\n        target = 0\r\n      } else if (object instanceof THREE.Mesh) {\r\n        if (object.material.uniforms?._transmission?.value > 0.2) {\r\n          //Check for MeshTransmissionMaterial\r\n          target = 0.2\r\n        } else if (object.material._transmission && object.material._transmission > 0.2) {\r\n          //Check for MeshPhysicalMaterial with transmission setting\r\n          target = 0.2\r\n        } else if (object.material.transparent) {\r\n          // Check for OtherMaterials with transparent parameter\r\n          target = object.material.opacity\r\n        }\r\n      }\r\n    }\r\n\r\n    easing.damp(uOpacity, 'value', target, smoothTime, delta)\r\n  })\r\n\r\n  useEffect(() => {\r\n    if (!ref?.current) return\r\n\r\n    const screenRes = ref.current.uniforms.get('screenRes')\r\n    if (screenRes) {\r\n      screenRes.value.x = viewport.width\r\n      screenRes.value.y = viewport.height\r\n    }\r\n  }, [viewport])\r\n\r\n  return (\r\n    <LensFlareWrapped\r\n      ref={ref}\r\n      blendFunction={blendFunction}\r\n      enabled={enabled}\r\n      glareSize={glareSize}\r\n      lensPosition={lensPosition}\r\n      screenRes={screenRes}\r\n      starPoints={starPoints}\r\n      flareSize={flareSize}\r\n      flareSpeed={flareSpeed}\r\n      flareShape={flareShape}\r\n      animated={animated}\r\n      anamorphic={anamorphic}\r\n      colorGain={colorGain}\r\n      lensDirtTexture={lensDirtTexture}\r\n      haloScale={haloScale}\r\n      secondaryGhosts={secondaryGhosts}\r\n      aditionalStreaks={aditionalStreaks}\r\n      ghostScale={ghostScale}\r\n      opacity={opacity}\r\n      starBurst={starBurst}\r\n    />\r\n  )\r\n}\r\n", "import { BloomEffect, BlendFunction } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const Bloom = /* @__PURE__ */ wrapEffect(BloomEffect, {\r\n  blendFunction: BlendFunction.ADD,\r\n})\r\n", "import { BrightnessContrastEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const BrightnessContrast = /* @__PURE__ */ wrapEffect(BrightnessContrastEffect)\r\n", "import { ChromaticAberrationEffect } from 'postprocessing'\r\nimport { type EffectProps, wrapEffect } from '../util'\r\n\r\nexport type ChromaticAberrationProps = EffectProps<typeof ChromaticAberrationEffect>\r\nexport const ChromaticAberration = /* @__PURE__ */ wrapEffect(ChromaticAberrationEffect)\r\n", "import { ColorAverageEffect, BlendFunction } from 'postprocessing'\r\nimport React, { Ref, forwardRef, useMemo } from 'react'\r\n\r\nexport type ColorAverageProps = Partial<{\r\n  blendFunction: BlendFunction\r\n}>\r\n\r\nexport const ColorAverage = /* @__PURE__ */ forwardRef<ColorAverageEffect, ColorAverageProps>(function ColorAverage(\r\n  { blendFunction = BlendFunction.NORMAL }: ColorAverageProps,\r\n  ref: Ref<ColorAverageEffect>\r\n) {\r\n  /** Because ColorAverage blendFunction is not an object but a number, we have to define a custom prop \"blendFunction\" */\r\n  const effect = useMemo(() => new ColorAverageEffect(blendFunction), [blendFunction])\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import { ColorDepthEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const ColorDepth = /* @__PURE__ */ wrapEffect(ColorDepthEffect)\r\n", "import { DepthEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const Depth = /* @__PURE__ */ wrapEffect(DepthEffect)\r\n", "import { DotScreenEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const DotScreen = /* @__PURE__ */ wrapEffect(DotScreenEffect)\r\n", "import { Vector2 } from 'three'\r\nimport { GlitchEffect, GlitchMode } from 'postprocessing'\r\nimport { Ref, forwardRef, useMemo, useLayoutEffect, useEffect } from 'react'\r\nimport { ReactThreeFiber, useThree } from '@react-three/fiber'\r\nimport { useVector2 } from '../util'\r\n\r\nexport type GlitchProps = ConstructorParameters<typeof GlitchEffect>[0] &\r\n  Partial<{\r\n    mode: GlitchMode\r\n    active: boolean\r\n    delay: ReactThreeFiber.Vector2\r\n    duration: ReactThreeFiber.Vector2\r\n    chromaticAberrationOffset: ReactThreeFiber.Vector2\r\n    strength: ReactThreeFiber.Vector2\r\n  }>\r\n\r\nexport const Glitch = /* @__PURE__ */ forwardRef<GlitchEffect, GlitchProps>(function Glitch(\r\n  { active = true, ...props }: GlitchProps,\r\n  ref: Ref<GlitchEffect>\r\n) {\r\n  const invalidate = useThree((state) => state.invalidate)\r\n  const delay = useVector2(props, 'delay')\r\n  const duration = useVector2(props, 'duration')\r\n  const strength = useVector2(props, 'strength')\r\n  const chromaticAberrationOffset = useVector2(props, 'chromaticAberrationOffset')\r\n  const effect = useMemo(\r\n    () => new GlitchEffect({ ...props, delay, duration, strength, chromaticAberrationOffset }),\r\n    [delay, duration, props, strength, chromaticAberrationOffset]\r\n  )\r\n  useLayoutEffect(() => {\r\n    effect.mode = active ? props.mode || GlitchMode.SPORADIC : GlitchMode.DISABLED\r\n    invalidate()\r\n  }, [active, effect, invalidate, props.mode])\r\n  useEffect(() => {\r\n    return () => {\r\n      effect.dispose?.()\r\n    }\r\n  }, [effect])\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import { GodRaysEffect } from 'postprocessing'\r\nimport React, { Ref, forwardRef, useMemo, useContext, useLayoutEffect } from 'react'\r\nimport { Mesh, Points } from 'three'\r\nimport { EffectComposerContext } from '../EffectComposer'\r\nimport { resolveRef } from '../util'\r\n\r\ntype GodRaysProps = ConstructorParameters<typeof GodRaysEffect>[2] & {\r\n  sun: Mesh | Points | React.RefObject<Mesh | Points>\r\n}\r\n\r\nexport const GodRays = /* @__PURE__ */ forwardRef(function GodRays(props: GodRaysProps, ref: Ref<GodRaysEffect>) {\r\n  const { camera } = useContext(EffectComposerContext)\r\n  const effect = useMemo(() => new GodRaysEffect(camera, resolveRef(props.sun), props), [camera, props])\r\n  useLayoutEffect(() => void (effect.lightSource = resolveRef(props.sun)), [effect, props.sun])\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import React, { Ref, forwardRef, useMemo, useLayoutEffect } from 'react'\r\nimport { GridEffect } from 'postprocessing'\r\nimport { useThree } from '@react-three/fiber'\r\n\r\ntype GridProps = ConstructorParameters<typeof GridEffect>[0] &\r\n  Partial<{\r\n    size: {\r\n      width: number\r\n      height: number\r\n    }\r\n  }>\r\n\r\nexport const Grid = /* @__PURE__ */ forwardRef(function Grid({ size, ...props }: GridProps, ref: Ref<GridEffect>) {\r\n  const invalidate = useThree((state) => state.invalidate)\r\n  const effect = useMemo(() => new GridEffect(props), [props])\r\n  useLayoutEffect(() => {\r\n    if (size) effect.setSize(size.width, size.height)\r\n    invalidate()\r\n  }, [effect, size, invalidate])\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import { HueSaturationEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const HueSaturation = /* @__PURE__ */ wrapEffect(HueSaturationEffect)\r\n", "import { NoiseEffect, BlendFunction } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const Noise = /* @__PURE__ */ wrapEffect(NoiseEffect, { blendFunction: BlendFunction.COLOR_DODGE })\r\n", "import { OutlineEffect } from 'postprocessing'\r\nimport { Ref, RefObject, forwardRef, useMemo, useEffect, useContext, useRef } from 'react'\r\nimport { Object3D } from 'three'\r\nimport { useThree } from '@react-three/fiber'\r\nimport { EffectComposerContext } from '../EffectComposer'\r\nimport { selectionContext } from '../Selection'\r\nimport { resolveRef } from '../util'\r\n\r\ntype ObjectRef = RefObject<Object3D>\r\n\r\nexport type OutlineProps = ConstructorParameters<typeof OutlineEffect>[2] &\r\n  Partial<{\r\n    selection: Object3D | Object3D[] | ObjectRef | ObjectRef[]\r\n    selectionLayer: number\r\n  }>\r\n\r\nexport const Outline = /* @__PURE__ */ forwardRef(function Outline(\r\n  {\r\n    selection = [],\r\n    selectionLayer = 10,\r\n    blendFunction,\r\n    patternTexture,\r\n    edgeStrength,\r\n    pulseSpeed,\r\n    visibleEdgeColor,\r\n    hiddenEdgeColor,\r\n    width,\r\n    height,\r\n    kernelSize,\r\n    blur,\r\n    xRay,\r\n    ...props\r\n  }: OutlineProps,\r\n  forwardRef: Ref<OutlineEffect>\r\n) {\r\n  const invalidate = useThree((state) => state.invalidate)\r\n  const { scene, camera } = useContext(EffectComposerContext)\r\n\r\n  const effect = useMemo(\r\n    () =>\r\n      new OutlineEffect(scene, camera, {\r\n        blendFunction,\r\n        patternTexture,\r\n        edgeStrength,\r\n        pulseSpeed,\r\n        visibleEdgeColor,\r\n        hiddenEdgeColor,\r\n        width,\r\n        height,\r\n        kernelSize,\r\n        blur,\r\n        xRay,\r\n        ...props,\r\n      }),\r\n    // NOTE: `props` is an unstable reference, so we can't memoize it\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    [\r\n      blendFunction,\r\n      blur,\r\n      camera,\r\n      edgeStrength,\r\n      height,\r\n      hiddenEdgeColor,\r\n      kernelSize,\r\n      patternTexture,\r\n      pulseSpeed,\r\n      scene,\r\n      visibleEdgeColor,\r\n      width,\r\n      xRay,\r\n    ]\r\n  )\r\n\r\n  const api = useContext(selectionContext)\r\n\r\n  useEffect(() => {\r\n    // Do not allow array selection if declarative selection is active\r\n    // TODO: array selection should probably be deprecated altogether\r\n    if (!api && selection) {\r\n      effect.selection.set(\r\n        Array.isArray(selection) ? (selection as Object3D[]).map(resolveRef) : [resolveRef(selection) as Object3D]\r\n      )\r\n      invalidate()\r\n      return () => {\r\n        effect.selection.clear()\r\n        invalidate()\r\n      }\r\n    }\r\n  }, [effect, selection, api, invalidate])\r\n\r\n  useEffect(() => {\r\n    effect.selectionLayer = selectionLayer\r\n    invalidate()\r\n  }, [effect, invalidate, selectionLayer])\r\n\r\n  const ref = useRef<OutlineEffect>(undefined)\r\n  useEffect(() => {\r\n    if (api && api.enabled) {\r\n      if (api.selected?.length) {\r\n        effect.selection.set(api.selected)\r\n        invalidate()\r\n        return () => {\r\n          effect.selection.clear()\r\n          invalidate()\r\n        }\r\n      }\r\n    }\r\n  }, [api, effect.selection, invalidate])\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      effect.dispose()\r\n    }\r\n  }, [effect])\r\n\r\n  return <primitive ref={forwardRef} object={effect} />\r\n})\r\n", "import { forwardRef, useMemo, Ref } from 'react'\r\nimport { PixelationEffect } from 'postprocessing'\r\n\r\nexport type PixelationProps = {\r\n  granularity?: number\r\n}\r\n\r\nexport const Pixelation = /* @__PURE__ */ forwardRef<PixelationEffect, PixelationProps>(function Pixelation(\r\n  { granularity = 5 }: PixelationProps,\r\n  ref: Ref<PixelationEffect>\r\n) {\r\n  /** Because GlitchEffect granularity is not an object but a number, we have to define a custom prop \"granularity\" */\r\n  const effect = useMemo(() => new PixelationEffect(granularity), [granularity])\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import { ScanlineEffect, BlendFunction } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const Scanline = /* @__PURE__ */ wrapEffect(ScanlineEffect, {\r\n  blendFunction: BlendFunction.OVERLAY,\r\n  density: 1.25,\r\n})\r\n", "import { SelectiveBloomEffect, BlendFunction } from 'postprocessing'\r\nimport type { BloomEffectOptions } from 'postprocessing'\r\nimport React, { Ref, RefObject, forwardRef, useMemo, useEffect, useContext, useRef } from 'react'\r\nimport { Object3D } from 'three'\r\nimport { useThree } from '@react-three/fiber'\r\nimport { EffectComposerContext } from '../EffectComposer'\r\nimport { selectionContext } from '../Selection'\r\nimport { resolveRef } from '../util'\r\n\r\ntype ObjectRef = RefObject<Object3D>\r\n\r\nexport type SelectiveBloomProps = BloomEffectOptions &\r\n  Partial<{\r\n    lights: Object3D[] | ObjectRef[]\r\n    selection: Object3D | Object3D[] | ObjectRef | ObjectRef[]\r\n    selectionLayer: number\r\n    inverted: boolean\r\n    ignoreBackground: boolean\r\n  }>\r\n\r\nconst addLight = (light: Object3D, effect: SelectiveBloomEffect) => light.layers.enable(effect.selection.layer)\r\nconst removeLight = (light: Object3D, effect: SelectiveBloomEffect) => light.layers.disable(effect.selection.layer)\r\n\r\nexport const SelectiveBloom = /* @__PURE__ */ forwardRef(function SelectiveBloom(\r\n  {\r\n    selection = [],\r\n    selectionLayer = 10,\r\n    lights = [],\r\n    inverted = false,\r\n    ignoreBackground = false,\r\n    luminanceThreshold,\r\n    luminanceSmoothing,\r\n    intensity,\r\n    width,\r\n    height,\r\n    kernelSize,\r\n    mipmapBlur,\r\n\r\n    ...props\r\n  }: SelectiveBloomProps,\r\n  forwardRef: Ref<SelectiveBloomEffect>\r\n) {\r\n  if (lights.length === 0) {\r\n    console.warn('SelectiveBloom requires lights to work.')\r\n  }\r\n\r\n  const invalidate = useThree((state) => state.invalidate)\r\n  const { scene, camera } = useContext(EffectComposerContext)\r\n  const effect = useMemo(() => {\r\n    const effect = new SelectiveBloomEffect(scene, camera, {\r\n      blendFunction: BlendFunction.ADD,\r\n      luminanceThreshold,\r\n      luminanceSmoothing,\r\n      intensity,\r\n      width,\r\n      height,\r\n      kernelSize,\r\n      mipmapBlur,\r\n      ...props,\r\n    })\r\n    effect.inverted = inverted\r\n    effect.ignoreBackground = ignoreBackground\r\n    return effect\r\n  }, [\r\n    scene,\r\n    camera,\r\n    luminanceThreshold,\r\n    luminanceSmoothing,\r\n    intensity,\r\n    width,\r\n    height,\r\n    kernelSize,\r\n    mipmapBlur,\r\n    inverted,\r\n    ignoreBackground,\r\n    props,\r\n  ])\r\n\r\n  const api = useContext(selectionContext)\r\n\r\n  useEffect(() => {\r\n    // Do not allow array selection if declarative selection is active\r\n    // TODO: array selection should probably be deprecated altogether\r\n    if (!api && selection) {\r\n      effect.selection.set(\r\n        Array.isArray(selection) ? (selection as Object3D[]).map(resolveRef) : [resolveRef(selection) as Object3D]\r\n      )\r\n      invalidate()\r\n      return () => {\r\n        effect.selection.clear()\r\n        invalidate()\r\n      }\r\n    }\r\n  }, [effect, selection, api, invalidate])\r\n\r\n  useEffect(() => {\r\n    effect.selection.layer = selectionLayer\r\n    invalidate()\r\n  }, [effect, invalidate, selectionLayer])\r\n\r\n  useEffect(() => {\r\n    if (lights && lights.length > 0) {\r\n      lights.forEach((light) => addLight(resolveRef(light), effect))\r\n      invalidate()\r\n      return () => {\r\n        lights.forEach((light) => removeLight(resolveRef(light), effect))\r\n        invalidate()\r\n      }\r\n    }\r\n  }, [effect, invalidate, lights, selectionLayer])\r\n\r\n  useEffect(() => {\r\n    if (api && api.enabled) {\r\n      if (api.selected?.length) {\r\n        effect.selection.set(api.selected)\r\n        invalidate()\r\n        return () => {\r\n          effect.selection.clear()\r\n          invalidate()\r\n        }\r\n      }\r\n    }\r\n  }, [api, effect.selection, invalidate])\r\n\r\n  return <primitive ref={forwardRef} object={effect} dispose={null} />\r\n})\r\n", "import { SepiaEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const Sepia = /* @__PURE__ */ wrapEffect(SepiaEffect)\r\n", "import { Ref, forwardRef, useContext, useMemo } from 'react'\r\nimport { SSAOEffect, BlendFunction } from 'postprocessing'\r\nimport { EffectComposerContext } from '../EffectComposer'\r\n\r\n// first two args are camera and texture\r\ntype SSAOProps = ConstructorParameters<typeof SSAOEffect>[2]\r\n\r\nexport const SSAO = /* @__PURE__ */ forwardRef<SSAOEffect, SSAOProps>(function SSAO(\r\n  props: SSAOProps,\r\n  ref: Ref<SSAOEffect>\r\n) {\r\n  const { camera, normalPass, downSamplingPass, resolutionScale } = useContext(EffectComposerContext)\r\n  const effect = useMemo<SSAOEffect | {}>(() => {\r\n    if (normalPass === null && downSamplingPass === null) {\r\n      console.error('Please enable the NormalPass in the EffectComposer in order to use SSAO.')\r\n      return {}\r\n    }\r\n    return new SSAOEffect(camera, normalPass && !downSamplingPass ? (normalPass as any).texture : null, {\r\n      blendFunction: BlendFunction.MULTIPLY,\r\n      samples: 30,\r\n      rings: 4,\r\n      distanceThreshold: 1.0,\r\n      distanceFalloff: 0.0,\r\n      rangeThreshold: 0.5,\r\n      rangeFalloff: 0.1,\r\n      luminanceInfluence: 0.9,\r\n      radius: 20,\r\n      bias: 0.5,\r\n      intensity: 1.0,\r\n      color: undefined,\r\n      // @ts-ignore\r\n      normalDepthBuffer: downSamplingPass ? downSamplingPass.texture : null,\r\n      resolutionScale: resolutionScale ?? 1,\r\n      depthAwareUpsampling: true,\r\n      ...props,\r\n    })\r\n    // NOTE: `props` is an unstable reference, so we can't memoize it\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [camera, downSamplingPass, normalPass, resolutionScale])\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import { SMAAEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const SMAA = /* @__PURE__ */ wrapEffect(SMAAEffect)\r\n", "import { FXAAEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const FXAA = /* @__PURE__ */ wrapEffect(FXAAEffect)\r\n", "import { Uniform } from 'three'\r\nimport { Effect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nconst RampShader = {\r\n  fragmentShader: /* glsl */ `\r\n    uniform int rampType;\r\n\r\n    uniform vec2 rampStart;\r\n    uniform vec2 rampEnd;\r\n\r\n    uniform vec4 startColor;\r\n    uniform vec4 endColor;\r\n\r\n    uniform float rampBias;\r\n    uniform float rampGain;\r\n\r\n    uniform bool rampMask;\r\n    uniform bool rampInvert;\r\n\r\n    float getBias(float time, float bias) {\r\n      return time / (((1.0 / bias) - 2.0) * (1.0 - time) + 1.0);\r\n    }\r\n\r\n    float getGain(float time, float gain) {\r\n      if (time < 0.5)\r\n        return getBias(time * 2.0, gain) / 2.0;\r\n      else\r\n        return getBias(time * 2.0 - 1.0, 1.0 - gain) / 2.0 + 0.5;\r\n    }\r\n\r\n    void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {\r\n      vec2 centerPixel = uv * resolution;\r\n      vec2 startPixel = rampStart * resolution;\r\n      vec2 endPixel = rampEnd * resolution;\r\n\r\n      float rampAlpha;\r\n\r\n      if (rampType == 1) {\r\n        vec2 fuv = centerPixel / resolution.y;\r\n        vec2 suv = startPixel / resolution.y;\r\n        vec2 euv = endPixel / resolution.y;\r\n\r\n        float radius = length(suv - euv);\r\n        float falloff = length(fuv - suv);\r\n        rampAlpha = smoothstep(0.0, radius, falloff);\r\n      } else {\r\n        float radius = length(startPixel - endPixel);\r\n        vec2 direction = normalize(vec2(endPixel.x - startPixel.x, -(startPixel.y - endPixel.y)));\r\n\r\n        float fade = dot(centerPixel - startPixel, direction);\r\n        if (rampType == 2) fade = abs(fade);\r\n\r\n        rampAlpha = smoothstep(0.0, 1.0, fade / radius);\r\n      }\r\n\r\n      rampAlpha = abs((rampInvert ? 1.0 : 0.0) - getBias(rampAlpha, rampBias) * getGain(rampAlpha, rampGain));\r\n\r\n      if (rampMask) {\r\n        vec4 inputBuff = texture2D(inputBuffer, uv);\r\n        outputColor = mix(inputBuff, inputColor, rampAlpha);\r\n      } else {\r\n        outputColor = mix(startColor, endColor, rampAlpha);\r\n      }\r\n    }\r\n  `,\r\n}\r\n\r\nexport enum RampType {\r\n  Linear,\r\n  Radial,\r\n  MirroredLinear,\r\n}\r\n\r\nexport class RampEffect extends Effect {\r\n  constructor({\r\n    /**\r\n     * Type of ramp gradient.\r\n     */\r\n    rampType = RampType.Linear,\r\n    /**\r\n     * Starting point of the ramp gradient in normalized coordinates.\r\n     *\r\n     * Ranges from `[0 - 1]` as `[x, y]`. Default is `[0.5, 0.5]`.\r\n     */\r\n    rampStart = [0.5, 0.5],\r\n    /**\r\n     * Ending point of the ramp gradient in normalized coordinates.\r\n     *\r\n     * Ranges from `[0 - 1]` as `[x, y]`. Default is `[1, 1]`\r\n     */\r\n    rampEnd = [1, 1],\r\n    /**\r\n     * Color at the starting point of the gradient.\r\n     *\r\n     * Default is black: `[0, 0, 0, 1]`\r\n     */\r\n    startColor = [0, 0, 0, 1],\r\n    /**\r\n     * Color at the ending point of the gradient.\r\n     *\r\n     * Default is white: `[1, 1, 1, 1]`\r\n     */\r\n    endColor = [1, 1, 1, 1],\r\n    /**\r\n     * Bias for the interpolation curve when both bias and gain are 0.5.\r\n     *\r\n     * Ranges from `[0 - 1]`. Default is `0.5`.\r\n     */\r\n    rampBias = 0.5,\r\n    /**\r\n     * Gain for the interpolation curve when both bias and gain are 0.5.\r\n     *\r\n     * Ranges from `[0 - 1]`. Default is `0.5`.\r\n     */\r\n    rampGain = 0.5,\r\n    /**\r\n     * When enabled, the ramp gradient is used as an effect mask, and colors are ignored.\r\n     *\r\n     * Default is `false`.\r\n     */\r\n    rampMask = false,\r\n    /**\r\n     * Controls whether the ramp gradient is inverted.\r\n     *\r\n     * When disabled, rampStart is transparent and rampEnd is opaque.\r\n     *\r\n     * Default is `false`.\r\n     */\r\n    rampInvert = false,\r\n    ...params\r\n  } = {}) {\r\n    super('RampEffect', RampShader.fragmentShader, {\r\n      ...params,\r\n      uniforms: new Map<string, Uniform>([\r\n        ['rampType', new Uniform(rampType)],\r\n        ['rampStart', new Uniform(rampStart)],\r\n        ['rampEnd', new Uniform(rampEnd)],\r\n        ['startColor', new Uniform(startColor)],\r\n        ['endColor', new Uniform(endColor)],\r\n        ['rampBias', new Uniform(rampBias)],\r\n        ['rampGain', new Uniform(rampGain)],\r\n        ['rampMask', new Uniform(rampMask)],\r\n        ['rampInvert', new Uniform(rampInvert)],\r\n      ]),\r\n    })\r\n  }\r\n}\r\n\r\nexport const Ramp = /* @__PURE__ */ wrapEffect(RampEffect)\r\n", "import { TextureEffect } from 'postprocessing'\r\nimport { Ref, forwardRef, useMemo, useLayoutEffect } from 'react'\r\nimport { useLoader } from '@react-three/fiber'\r\nimport { TextureLoader, SRGBColorSpace, RepeatWrapping } from 'three'\r\n\r\ntype TextureProps = ConstructorParameters<typeof TextureEffect>[0] & {\r\n  textureSrc: string\r\n  /** opacity of provided texture */\r\n  opacity?: number\r\n}\r\n\r\nexport const Texture = /* @__PURE__ */ forwardRef<TextureEffect, TextureProps>(function Texture(\r\n  { textureSrc, texture, opacity = 1, ...props }: TextureProps,\r\n  ref: Ref<TextureEffect>\r\n) {\r\n  const t = useLoader(TextureLoader, textureSrc)\r\n  useLayoutEffect(() => {\r\n    t.colorSpace = SRGBColorSpace\r\n    t.wrapS = t.wrapT = RepeatWrapping\r\n  }, [t])\r\n  const effect = useMemo(() => new TextureEffect({ ...props, texture: t || texture }), [props, t, texture])\r\n  return <primitive ref={ref} object={effect} blendMode-opacity-value={opacity} dispose={null} />\r\n})\r\n", "import { ToneMappingEffect } from 'postprocessing'\r\nimport { type EffectProps, wrapEffect } from '../util'\r\n\r\nexport type ToneMappingProps = EffectProps<typeof ToneMappingEffect>\r\n\r\nexport const ToneMapping = /* @__PURE__ */ wrapEffect(ToneMappingEffect)\r\n", "import { VignetteEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const Vignette = /* @__PURE__ */ wrapEffect(VignetteEffect)\r\n", "import { ShockWaveEffect } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const ShockWave = /* @__PURE__ */ wrapEffect(ShockWaveEffect)\r\n", "import { useThree } from '@react-three/fiber'\r\nimport { LUT3DEffect, BlendFunction } from 'postprocessing'\r\nimport React, { forwardRef, Ref, useLayoutEffect, useMemo } from 'react'\r\nimport type { Texture } from 'three'\r\n\r\nexport type LUTProps = {\r\n  lut: Texture\r\n  blendFunction?: BlendFunction\r\n  tetrahedralInterpolation?: boolean\r\n}\r\n\r\nexport const LUT = /* @__PURE__ */ forwardRef(function LUT(\r\n  { lut, tetrahedralInterpolation, ...props }: LUTProps,\r\n  ref: Ref<LUT3DEffect>\r\n) {\r\n  const effect = useMemo(() => new LUT3DEffect(lut, props), [lut, props])\r\n  const invalidate = useThree((state) => state.invalidate)\r\n\r\n  useLayoutEffect(() => {\r\n    if (tetrahedralInterpolation) effect.tetrahedralInterpolation = tetrahedralInterpolation\r\n    if (lut) effect.lut = lut\r\n    invalidate()\r\n  }, [effect, invalidate, lut, tetrahedralInterpolation])\r\n\r\n  return <primitive ref={ref} object={effect} dispose={null} />\r\n})\r\n", "import { TiltShiftEffect, BlendFunction } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nexport const TiltShift = /* @__PURE__ */ wrapEffect(TiltShiftEffect, { blendFunction: BlendFunction.ADD })\r\n", "import { Uniform } from 'three'\r\nimport { BlendFunction, Effect, EffectAttribute } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nconst TiltShiftShader = {\r\n  fragmentShader: `\r\n\r\n    // original shader by <PERSON>\r\n\r\n    #define MAX_ITERATIONS 100\r\n\r\n    uniform float blur;\r\n    uniform float taper;\r\n    uniform vec2 start;\r\n    uniform vec2 end;\r\n    uniform vec2 direction;\r\n    uniform int samples;\r\n\r\n    float random(vec3 scale, float seed) {\r\n        /* use the fragment position for a different seed per-pixel */\r\n        return fract(sin(dot(gl_FragCoord.xyz + seed, scale)) * 43758.5453 + seed);\r\n    }\r\n\r\n    void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {\r\n        vec4 color = vec4(0.0);\r\n        float total = 0.0;\r\n        vec2 startPixel = vec2(start.x * resolution.x, start.y * resolution.y);\r\n        vec2 endPixel = vec2(end.x * resolution.x, end.y * resolution.y);\r\n        float f_samples = float(samples);\r\n        float half_samples = f_samples / 2.0;\r\n\r\n        // use screen diagonal to normalize blur radii\r\n        float maxScreenDistance = distance(vec2(0.0), resolution); // diagonal distance\r\n        float gradientRadius = taper * (maxScreenDistance);\r\n        float blurRadius = blur * (maxScreenDistance / 16.0);\r\n\r\n        /* randomize the lookup values to hide the fixed number of samples */\r\n        float offset = random(vec3(12.9898, 78.233, 151.7182), 0.0);\r\n        vec2 normal = normalize(vec2(startPixel.y - endPixel.y, endPixel.x - startPixel.x));\r\n        float radius = smoothstep(0.0, 1.0, abs(dot(uv * resolution - startPixel, normal)) / gradientRadius) * blurRadius;\r\n\r\n        #pragma unroll_loop_start\r\n        for (int i = 0; i <= MAX_ITERATIONS; i++) {\r\n            if (i >= samples) { break; } // return early if over sample count\r\n            float f_i = float(i);\r\n            float s_i = -half_samples + f_i;\r\n            float percent = (s_i + offset - 0.5) / half_samples;\r\n            float weight = 1.0 - abs(percent);\r\n            vec4 sample_i = texture2D(inputBuffer, uv + normalize(direction) / resolution * percent * radius);\r\n            /* switch to pre-multiplied alpha to correctly blur transparent images */\r\n            sample_i.rgb *= sample_i.a;\r\n            color += sample_i * weight;\r\n            total += weight;\r\n        }\r\n        #pragma unroll_loop_end\r\n\r\n        outputColor = color / total;\r\n\r\n        /* switch back from pre-multiplied alpha */\r\n        outputColor.rgb /= outputColor.a + 0.00001;\r\n    }\r\n    `,\r\n}\r\n\r\nexport class TiltShiftEffect extends Effect {\r\n  constructor({\r\n    blendFunction = BlendFunction.NORMAL,\r\n    blur = 0.15, // [0, 1], can go beyond 1 for extra\r\n    taper = 0.5, // [0, 1], can go beyond 1 for extra\r\n    start = [0.5, 0.0], // [0,1] percentage x,y of screenspace\r\n    end = [0.5, 1.0], // [0,1] percentage x,y of screenspace\r\n    samples = 10.0, // number of blur samples\r\n    direction = [1, 1], // direction of blur\r\n  } = {}) {\r\n    super('TiltShiftEffect', TiltShiftShader.fragmentShader, {\r\n      blendFunction,\r\n      attributes: EffectAttribute.CONVOLUTION,\r\n      uniforms: new Map<string, Uniform<number | number[]>>([\r\n        ['blur', new Uniform(blur)],\r\n        ['taper', new Uniform(taper)],\r\n        ['start', new Uniform(start)],\r\n        ['end', new Uniform(end)],\r\n        ['samples', new Uniform(samples)],\r\n        ['direction', new Uniform(direction)],\r\n      ]),\r\n    })\r\n  }\r\n}\r\n\r\nexport const TiltShift2 = /* @__PURE__ */ wrapEffect(TiltShiftEffect, { blendFunction: BlendFunction.NORMAL })\r\n", "// From: https://github.com/emilwidlund/ASCII\r\n// https://twitter.com/emilwidlund/status/1652386482420609024\r\n\r\nimport { forwardRef, useMemo } from 'react'\r\nimport { CanvasTexture, Color, NearestFilter, RepeatWrapping, Texture, Uniform } from 'three'\r\nimport { Effect } from 'postprocessing'\r\n\r\nconst fragment = `\r\nuniform sampler2D uCharacters;\r\nuniform float uCharactersCount;\r\nuniform float uCellSize;\r\nuniform bool uInvert;\r\nuniform vec3 uColor;\r\n\r\nconst vec2 SIZE = vec2(16.);\r\n\r\nvec3 greyscale(vec3 color, float strength) {\r\n    float g = dot(color, vec3(0.299, 0.587, 0.114));\r\n    return mix(color, vec3(g), strength);\r\n}\r\n\r\nvec3 greyscale(vec3 color) {\r\n    return greyscale(color, 1.0);\r\n}\r\n\r\nvoid mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {\r\n    vec2 cell = resolution / uCellSize;\r\n    vec2 grid = 1.0 / cell;\r\n    vec2 pixelizedUV = grid * (0.5 + floor(uv / grid));\r\n    vec4 pixelized = texture2D(inputBuffer, pixelizedUV);\r\n    float greyscaled = greyscale(pixelized.rgb).r;\r\n\r\n    if (uInvert) {\r\n        greyscaled = 1.0 - greyscaled;\r\n    }\r\n\r\n    float characterIndex = floor((uCharactersCount - 1.0) * greyscaled);\r\n    vec2 characterPosition = vec2(mod(characterIndex, SIZE.x), floor(characterIndex / SIZE.y));\r\n    vec2 offset = vec2(characterPosition.x, -characterPosition.y) / SIZE;\r\n    vec2 charUV = mod(uv * (cell / SIZE), 1.0 / SIZE) - vec2(0., 1.0 / SIZE) + offset;\r\n    vec4 asciiCharacter = texture2D(uCharacters, charUV);\r\n\r\n    asciiCharacter.rgb = uColor * asciiCharacter.r;\r\n    asciiCharacter.a = pixelized.a;\r\n    outputColor = asciiCharacter;\r\n}\r\n`\r\n\r\ninterface IASCIIEffectProps {\r\n  font?: string\r\n  characters?: string\r\n  fontSize?: number\r\n  cellSize?: number\r\n  color?: string\r\n  invert?: boolean\r\n}\r\n\r\nclass ASCIIEffect extends Effect {\r\n  constructor({\r\n    font = 'arial',\r\n    characters = ` .:,'-^=*+?!|0#X%WM@`,\r\n    fontSize = 54,\r\n    cellSize = 16,\r\n    color = '#ffffff',\r\n    invert = false,\r\n  }: IASCIIEffectProps = {}) {\r\n    const uniforms = new Map<string, Uniform>([\r\n      ['uCharacters', new Uniform(new Texture())],\r\n      ['uCellSize', new Uniform(cellSize)],\r\n      ['uCharactersCount', new Uniform(characters.length)],\r\n      ['uColor', new Uniform(new Color(color))],\r\n      ['uInvert', new Uniform(invert)],\r\n    ])\r\n\r\n    super('ASCIIEffect', fragment, { uniforms })\r\n\r\n    const charactersTextureUniform = this.uniforms.get('uCharacters')\r\n\r\n    if (charactersTextureUniform) {\r\n      charactersTextureUniform.value = this.createCharactersTexture(characters, font, fontSize)\r\n    }\r\n  }\r\n\r\n  /** Draws the characters on a Canvas and returns a texture */\r\n  public createCharactersTexture(characters: string, font: string, fontSize: number): Texture {\r\n    const canvas = document.createElement('canvas')\r\n    const SIZE = 1024\r\n    const MAX_PER_ROW = 16\r\n    const CELL = SIZE / MAX_PER_ROW\r\n\r\n    canvas.width = canvas.height = SIZE\r\n    const texture = new CanvasTexture(canvas, undefined, RepeatWrapping, RepeatWrapping, NearestFilter, NearestFilter)\r\n    const context = canvas.getContext('2d')\r\n\r\n    if (!context) {\r\n      throw new Error('Context not available')\r\n    }\r\n\r\n    context.clearRect(0, 0, SIZE, SIZE)\r\n    context.font = `${fontSize}px ${font}`\r\n    context.textAlign = 'center'\r\n    context.textBaseline = 'middle'\r\n    context.fillStyle = '#fff'\r\n\r\n    for (let i = 0; i < characters.length; i++) {\r\n      const char = characters[i]\r\n      const x = i % MAX_PER_ROW\r\n      const y = Math.floor(i / MAX_PER_ROW)\r\n      context.fillText(char, x * CELL + CELL / 2, y * CELL + CELL / 2)\r\n    }\r\n\r\n    texture.needsUpdate = true\r\n    return texture\r\n  }\r\n}\r\n\r\nexport const ASCII = /* @__PURE__ */ forwardRef<ASCIIEffect, IASCIIEffectProps>(\r\n  (\r\n    {\r\n      font = 'arial',\r\n      characters = ` .:,'-^=*+?!|0#X%WM@`,\r\n      fontSize = 54,\r\n      cellSize = 16,\r\n      color = '#ffffff',\r\n      invert = false,\r\n    },\r\n    fref\r\n  ) => {\r\n    const effect = useMemo(\r\n      () => new ASCIIEffect({ characters, font, fontSize, cellSize, color, invert }),\r\n      [characters, fontSize, cellSize, color, invert, font]\r\n    )\r\n    return <primitive ref={fref} object={effect} />\r\n  }\r\n)\r\n", "import { Uniform } from 'three'\r\nimport { BlendFunction, Effect, EffectAttribute } from 'postprocessing'\r\nimport { wrapEffect } from '../util'\r\n\r\nconst WaterShader = {\r\n  fragmentShader: /* glsl */ `\r\n    uniform float factor;\r\n\r\n    void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {\r\n      vec2 vUv = uv;\r\n      float frequency = 6.0 * factor;\r\n      float amplitude = 0.015 * factor;\r\n      float x = vUv.y * frequency + time * 0.7; \r\n      float y = vUv.x * frequency + time * 0.3;\r\n      vUv.x += cos(x + y) * amplitude * cos(y);\r\n      vUv.y += sin(x - y) * amplitude * cos(y);\r\n      vec4 rgba = texture(inputBuffer, vUv);\r\n      outputColor = rgba;\r\n    }\r\n  `,\r\n}\r\n\r\nexport class WaterEffectImpl extends Effect {\r\n  constructor({ blendFunction = BlendFunction.NORMAL, factor = 0 } = {}) {\r\n    super('WaterEffect', WaterShader.fragmentShader, {\r\n      blendFunction,\r\n      attributes: EffectAttribute.CONVOLUTION,\r\n      uniforms: new Map<string, Uniform<number | number[]>>([['factor', new Uniform(factor)]]),\r\n    })\r\n  }\r\n}\r\n\r\nexport const WaterEffect = /* @__PURE__ */ wrapEffect(WaterEffectImpl, {\r\n  blendFunction: BlendFunction.NORMAL,\r\n})\r\n", "// From https://github.com/N8python/n8ao\r\n// https://twitter.com/N8Programs/status/1660996748485984261\r\n\r\nimport { Ref, forwardRef, useLayoutEffect, useMemo } from 'react'\r\n/* @ts-ignore */\r\nimport { N8AOPostPass } from 'n8ao'\r\nimport { useThree, ReactThreeFiber, applyProps } from '@react-three/fiber'\r\n\r\nexport type N8AOProps = {\r\n  aoRadius?: number\r\n  distanceFalloff?: number\r\n  intensity?: number\r\n  quality?: 'performance' | 'low' | 'medium' | 'high' | 'ultra'\r\n  aoSamples?: number\r\n  denoiseSamples?: number\r\n  denoiseRadius?: number\r\n  color?: ReactThreeFiber.Color\r\n  halfRes?: boolean\r\n  depthAwareUpsampling?: boolean\r\n  screenSpaceRadius?: boolean\r\n  renderMode?: 0 | 1 | 2 | 3 | 4\r\n}\r\n\r\nexport const N8AO = /* @__PURE__ */ forwardRef<N8AOPostPass, N8AOProps>(\r\n  (\r\n    {\r\n      halfRes,\r\n      screenSpaceRadius,\r\n      quality,\r\n      depthAwareUpsampling = true,\r\n      aoRadius = 5,\r\n      aoSamples = 16,\r\n      denoiseSamples = 4,\r\n      denoiseRadius = 12,\r\n      distanceFalloff = 1,\r\n      intensity = 1,\r\n      color,\r\n      renderMode = 0,\r\n    },\r\n    ref: Ref<N8AOPostPass>\r\n  ) => {\r\n    const { camera, scene } = useThree()\r\n    const effect = useMemo(() => new N8AOPostPass(scene, camera), [camera, scene])\r\n\r\n    // TODO: implement dispose upstream; this effect has memory leaks without\r\n    useLayoutEffect(() => {\r\n      applyProps(effect.configuration, {\r\n        color,\r\n        aoRadius,\r\n        distanceFalloff,\r\n        intensity,\r\n        aoSamples,\r\n        denoiseSamples,\r\n        denoiseRadius,\r\n        screenSpaceRadius,\r\n        renderMode,\r\n        halfRes,\r\n        depthAwareUpsampling,\r\n      })\r\n    }, [\r\n      screenSpaceRadius,\r\n      color,\r\n      aoRadius,\r\n      distanceFalloff,\r\n      intensity,\r\n      aoSamples,\r\n      denoiseSamples,\r\n      denoiseRadius,\r\n      renderMode,\r\n      halfRes,\r\n      depthAwareUpsampling,\r\n      effect,\r\n    ])\r\n\r\n    useLayoutEffect(() => {\r\n      if (quality) effect.setQualityMode(quality.charAt(0).toUpperCase() + quality.slice(1))\r\n    }, [effect, quality])\r\n\r\n    return <primitive ref={ref} object={effect} />\r\n  }\r\n)\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,QAAI,gBAAgB;AAClB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;;;ACnDA,SAAS,gBAAgB,GAAGA,IAAG;AAC7B,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBC,IAAGF,IAAG;AACxE,IAAAE,GAAE,YAAYF;AACd,WAAOE;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAGF,EAAC;AAC7B;AAEA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AACjE,MAAI,QAAQ,UAAU,KAAM,QAAO;AACnC,MAAI,OAAO,UAAU,WAAY,QAAO;AAExC,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;;;ACRA,SAAS,eAAe;AACtB,WAAS,OAAO,UAAU,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACxF,UAAM,IAAI,IAAI,UAAU,IAAI;AAAA,EAC9B;AAEA,MAAI,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC;AACf,SAAO,IAAI,IAAI,IAAI;AACrB;AAYA,SAAS,eAAe;AACtB,WAAS,QAAQ,UAAU,QAAQ,QAAQ,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC9F,UAAM,KAAK,IAAI,UAAU,KAAK;AAAA,EAChC;AAEA,MAAI,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC,GACX,IAAI,MAAM,CAAC;AACf,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7E;AAYA,SAAS,eAAe;AACtB,WAAS,QAAQ,UAAU,QAAQ,QAAQ,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC9F,UAAM,KAAK,IAAI,UAAU,KAAK;AAAA,EAChC;AAEA,QAAM,CAAC;AACH,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,CAAC;AACP,QAAM,EAAE;AACR,QAAM,EAAE;AACR,QAAM,EAAE;AACR,QAAM,EAAE;AACR,QAAM,EAAE;AACd;AAqBA,SAAS,SAASG,SAAQ,GAAG,GAAG;AAC9B,MAAI,mBAAmBA,QAAO,MAAM,EAAE,UAAU;AAEhD,MAAI,IAAI,CAAC;AACT,MAAI,IAAI,iBAAiB,SAAS;AAClC,MAAI,IAAI,KAAK,KAAK,CAAC;AAEnB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,UAAU,iBAAiB,SAAS,CAAC;AACzC,QAAI,MAAM,KAAK,MAAM,IAAI,CAAC;AAC1B,QAAIC,OAAM,IAAI;AAEd,QAAI,QAAQ,IAAI,KAAKA,SAAQ,IAAI,GAAG;AAClC,QAAE,KAAK,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,SAAO,aAAa,MAAM,QAAQ,CAAC;AACrC;AAKA,SAAS,WAAW,IAAI,IAAI;AAC1B,MAAI,MAAM,CAAC;AACX,MAAI,UAAU,GAAG,QAAQ;AACzB,MAAI,UAAU,GAAG,QAAQ;AAEzB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC;AAAA,EACjC;AAEA,SAAO,IAAI,QAAQ,EAAE,UAAU,GAAG;AACpC;AAEA,IAAI,SAAsB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC5ID,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,OAAO,OAAO,OAAO,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAEvG,MAAI,MAAM,KAAM;AAChB,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI,IAAI;AAER,MAAI;AACF,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO,kBAAkB,GAAG;AACtD;AAEA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,OAAO,WAAW,eAAe,KAAK,OAAO,QAAQ,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAM,QAAO,MAAM,KAAK,IAAI;AAC1H;AAEA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;AAEA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,mBAAmB,GAAG,KAAK,iBAAiB,GAAG,KAAK,4BAA4B,GAAG,KAAK,mBAAmB;AACpH;AAEA,SAAS,WAAW,QAAQ,MAAM,OAAO;AACvC,MAAI,0BAA0B,GAAG;AAC/B,iBAAa,QAAQ;AAAA,EACvB,OAAO;AACL,iBAAa,SAASC,YAAWC,SAAQC,OAAMC,QAAO;AACpD,UAAI,IAAI,CAAC,IAAI;AACb,QAAE,KAAK,MAAM,GAAGD,KAAI;AACpB,UAAI,cAAc,SAAS,KAAK,MAAMD,SAAQ,CAAC;AAC/C,UAAI,WAAW,IAAI,YAAY;AAC/B,UAAIE,OAAO,iBAAgB,UAAUA,OAAM,SAAS;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,WAAW,MAAM,MAAM,SAAS;AACzC;AAYA,SAAS,kBAAkB,OAAOC,WAAU;AAC1C,MAAI,aAAa,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC1C,KAAK,WAAW,CAAC,GACjB,KAAK,WAAW,CAAC;AAErB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,SAAS,eAAe,OAAO,CAAC,GAChC,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,CAAC;AAGjB,MAAIC,UAAS,IAAI,QAAQ;AAEzB,EAAAA,QAAO,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC;AACjI,SAAOA,QAAO,YAAY,KAAK;AACjC;AACA,SAAS,oBAAoBD,WAAU;AACrC,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAGtB,SAAO,aAAa,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;AACrD;AAeA,SAAS,mBAAmB,QAAQ;AAClC,SAAO,oBAAoB,MAAM,MAAM;AACzC;AAEA,SAAS,oBAAoBA,WAAU;AACrC,SAAO,oBAAoBA,SAAQ,IAAI;AACzC;AA+BA,SAAS,gBAAgBA,WAAU;AAGjC,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAGtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAGtB,MAAI,cAAc,eAAeA,UAAS,CAAC,GAAG,CAAC,GAC3C,KAAK,YAAY,CAAC,GAClB,KAAK,YAAY,CAAC;AAEtB,MAAI,mBAAmBA,SAAQ,EAAG,QAAO;AAEzC,MAAI,IAAI,IAAI,QAAQ;AAEpB,IAAE,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC1G,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,MAAI,KAAK,OAAO,MAAM;AACtB,MAAI,KAAK,OAAO,MAAM;AACtB,MAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM;AACnC,SAAO;AAAA,IACL,GAAG,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI;AAAA,IAC5B,GAAG,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC;AAAA,IAC7B,GAAG,KAAK,KAAK,EAAE;AAAA,EACjB;AACF;AAEA,SAAS,sBAAsB,OAAOA,WAAU;AAC9C,MAAI,OAAO,MAAM,QAAQA,UAAS,CAAC,CAAC,IAAIA,UAAS,CAAC,IAAIA,UAAS,CAAC,EAAE,QAAQ,GACtE,QAAQ,eAAe,MAAM,CAAC,GAC9B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,QAAQ,MAAM,QAAQA,UAAS,CAAC,CAAC,IAAIA,UAAS,CAAC,IAAIA,UAAS,CAAC,EAAE,QAAQ,GACvE,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,QAAQ,MAAM,QAAQA,UAAS,CAAC,CAAC,IAAIA,UAAS,CAAC,IAAIA,UAAS,CAAC,EAAE,QAAQ,GACvE,QAAQ,eAAe,OAAO,CAAC,GAC/B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,UAAU,eAAe,OAAO,CAAC,GACjC,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,CAAC;AAElB,MAAI,mBAAmBA,SAAQ,EAAG,OAAM,IAAI,MAAM,wCAAwC;AAO1F,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AACjB,MAAI,QAAQ,KAAK;AAEjB,MAAI,IAAI,aAAa,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,QAAQ,QAAQ,KAAK;AAE1J,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,SAAO,CAAC,oBAAoBA,SAAQ,IAAI,IAAI,IAAI,IAAI;AACtD;AAEA,IAAI,MAAM,IAAI,QAAQ;AACtB,IAAI,MAAM,IAAI,QAAQ;AAYtB,SAAS,wBAAwB,QAAQ;AACvC,MAAI,cAAc,OAAO,IAAI,SAAUE,IAAG;AACxC,QAAI,MAAM,QAAQA,EAAC,GAAG;AACpB,aAAO,WAAW,SAAS,mBAAmBA,EAAC,CAAC;AAAA,IAClD;AAEA,WAAOA;AAAA,EACT,CAAC,GACG,eAAe,eAAe,aAAa,CAAC,GAC5C,KAAK,aAAa,CAAC,GACnB,KAAK,aAAa,CAAC,GACnB,KAAK,aAAa,CAAC;AAEvB,MAAI,mBAAmB,MAAM,EAAG,QAAO;AAEvC,MAAI,OAAO,IAAI,WAAW,IAAI,EAAE;AAEhC,MAAI,OAAO,IAAI,WAAW,IAAI,EAAE;AAChC,MAAIC,SAAQ,KAAK,MAAM,IAAI;AAC3B,SAAOA,SAAQ;AACjB;AAEA,IAAI,WAAwB,OAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACxTD,SAAS,MAAM,OAAO,KAAK,KAAK;AAC9B,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC;AAC3C;AAEA,SAAS,OAAO,GAAGC,SAAQ;AACzB,SAAO,MAAM,IAAI,KAAK,MAAM,IAAIA,OAAM,IAAIA,SAAQ,GAAGA,OAAM;AAC7D;AAGA,SAAS,WAAW,SAAS,QAAQ;AACnC,MAAI,QAAQ,OAAO,SAAS,SAAS,KAAK,KAAK,CAAC;AAChD,MAAI,QAAQ,KAAK,GAAI,UAAS,KAAK,KAAK;AACxC,SAAO;AACT;AAKA,SAAS,SAAS,SAAS;AACzB,SAAO,UAAU,MAAM,KAAK;AAC9B;AAKA,SAAS,SAAS,SAAS;AACzB,SAAO,UAAU,MAAM,KAAK;AAC9B;AAEA,SAAS,kBAAkBC,SAAQ,MAAM;AACvC,MAAI,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,IAAI;AAC1C,MAAI,UAAUA,QAAO,SAAS;AAC9B,MAAI,SAAS,IAAI;AACjB,MAAI,YAAY,KAAK,MAAM,IAAI;AAE/B,WAAS,IAAI,GAAG,IAAIA,QAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,IAAI,SAAS,IAAI,SAAS;AAClC,QAAIC,YAAW,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAC3C,QAAI,MAAM,IAAI,UAAU;AACxB,QAAI,IAAI,KAAK,IAAI,GAAG,IAAIA;AACxB,QAAIC,KAAI,KAAK,IAAI,GAAG,IAAID;AACxB,IAAAD,QAAO,CAAC,IAAI,IAAI;AAChB,IAAAA,QAAO,IAAI,CAAC,IAAI,IAAI;AACpB,IAAAA,QAAO,IAAI,CAAC,IAAIE,KAAI;AAAA,EACtB;AACF;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,OAAO;AACrF,SAAO,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,OAAO,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI;AACzF;AASA,SAAS,cAAc,GAAG,GAAG;AAC3B,MAAI,EAAE,MAAM,EAAE,GAAG;AAGf,QAAI,OAAO,EAAE,MAAM,aAAa;AAC9B,UAAI,EAAE,MAAM,EAAE,GAAG;AACf,eAAO,EAAE,IAAI,EAAE;AAAA,MACjB;AAAA,IACF;AAEA,WAAO,EAAE,IAAI,EAAE;AAAA,EACjB;AAEA,SAAO,EAAE,IAAI,EAAE;AACjB;AAWA,SAAS,WAAW,SAAS;AAC3B,MAAI,SAAS,QAAQ,KAAK,aAAa;AAGvC,MAAI,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAElC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,KAAK,OAAO,CAAC,CAAC;AAErB,WAAO,OAAO,SAAS,KAAK,wBAAwB,mBAAmB,OAAO,MAAM,EAAE,CAAC,CAAC,GAAG;AAEzF,aAAO,OAAO,OAAO,SAAS,GAAG,CAAC;AAAA,IACpC;AAAA,EACF;AAGA,MAAI,SAAS,CAAC,OAAO,OAAO,SAAS,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC;AAElE,WAAS,KAAK,OAAO,SAAS,GAAG,MAAM,GAAG,MAAM;AAE9C,WAAO,KAAK,OAAO,EAAE,CAAC;AAEtB,WAAO,OAAO,SAAS,KAAK,wBAAwB,mBAAmB,OAAO,MAAM,EAAE,CAAC,CAAC,GAAG;AAEzF,aAAO,OAAO,OAAO,SAAS,GAAG,CAAC;AAAA,IACpC;AAAA,EACF;AAGA,SAAO,OAAO,GAAG,CAAC;AAClB,SAAO,OAAO,OAAO,SAAS,GAAG,CAAC;AAElC,MAAI,IAAI,CAAC,EAAE,OAAO,QAAQ,MAAM;AAChC,SAAO;AACT;AACA,SAAS,MAAM,GAAG,OAAO,OAAO;AAC9B,MAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,OAAO,MAAM,CAAC,GACd,QAAQ,MAAM,CAAC;AAEnB,MAAI,QAAQ,eAAe,OAAO,CAAC,GAC/B,OAAO,MAAM,CAAC,GACd,QAAQ,MAAM,CAAC;AAEnB,SAAO,QAAQ,IAAI,SAAS,QAAQ,SAAS,QAAQ;AACvD;AAWA,SAAS,KAAK,GAAG;AACf,SAAO,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,MAAM;AACzC;AAWA,SAAS,KAAK,IAAI,IAAI,GAAG;AACvB,SAAO,MAAM,IAAI,KAAK,KAAK;AAC7B;AAWA,SAAS,YAAY,IAAI,IAAI,GAAG;AAC9B,UAAQ,IAAI,OAAO,KAAK;AAC1B;AAKA,SAAS,UAAU,GAAG,GAAGA,IAAG;AAC1B,MAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAIA,KAAIA,EAAC;AACvC,SAAO,CAAC,IAAI,GAAG,IAAI,GAAGA,KAAI,CAAC;AAC7B;AAKA,SAAS,2BAA2B,GAAG,GAAGA,IAAG;AAC3C,MAAI,KAAK,IAAI;AACb,MAAI,KAAK,IAAI;AACb,MAAIC,MAAKD,KAAIA;AACb,MAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAKC,OAAM,IAAI,KAAKA,MAAK,CAAC;AACtD,MAAI,KAAK,IAAI,KAAK,KAAK,KAAKA,MAAK,MAAM,IAAIA,MAAK,KAAK,CAAC;AACtD,MAAI,KAAKD,KAAI,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC;AACtD,SAAO,CAAC,IAAI,IAAI,EAAE;AACpB;AAOA,SAAS,qBAAqB,GAAG,GAAG;AAClC,MAAI,IAAI,IAAI,QAAQ,EAAE,aAAa,GAAG,CAAC;AACvC,MAAI,IAAI,EAAE,IAAI,CAAC;AACf,MAAI,IAAI,IAAI,QAAQ,EAAE,SAAS;AAG/B,MAAI,KAAK,IAAI,QAAQ,EAAE,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACnE,MAAI,YAAY,IAAI,QAAQ,EAAE,iBAAiB,IAAI,EAAE,EAAE,eAAe,KAAK,IAAI,EAAE;AAEjF,MAAI,SAAS,WAAW,WAAW,GAAG,EAAE,GAAG,SAAS;AAEpD,SAAO;AACT;AAEA,SAAS,kBAAkB,GAAG,GAAGA,IAAG;AAClC,MAAI,MAAM,KAAK,KAAK,CAAC;AACrB,MAAI,MAAM,KAAK,MAAM,GAAG,CAACA,EAAC;AAC1B,SAAO,CAAC,KAAK,GAAG;AAClB;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,MAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,MAAIA,KAAI,CAAC,KAAK,IAAI,GAAG,IAAI;AACzB,SAAO,CAAC,GAAG,GAAGA,EAAC;AACjB;AAKA,SAAS,yBAAyB,OAAO,SAAS;AAChD,MAAI,WAAW,eAAe,SAAS,CAAC,GACpC,IAAI,SAAS,CAAC,GACd,IAAI,SAAS,CAAC;AAElB,MAAIE,UAAS,qBAAqB,MAAM,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC;AACpE,MAAI,IAAI,YAAY,EAAE,MAAM,EAAE,aAAaA,OAAM,EAAE,GAAG,EAAE,MAAM,EAAE,aAAaA,OAAM,EAAE,GAAG,CAAC;AACzF,SAAO,IAAI,QAAQ,EAAE,YAAY,GAAG,GAAG,CAAC;AAC1C;AAKA,SAAS,qBAAqBC,IAAG,OAAO;AACtC,MAAI,IAAI,MAAM,OAAO,IAAIA,EAAC;AAE1B,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,OAAO;AACrC,MAAI,UAAU,eAAe,QAAQ,CAAC,GAClC,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,CAAC;AAElB,MAAI,SAAS,eAAe,OAAO,CAAC,GAChC,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,CAAC;AAEjB,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK;AAClC;AACA,SAAS,eAAeC,QAAO,MAAM;AACnC,MAAI,QAAQ,eAAe,MAAM,CAAC,GAC9B,KAAK,MAAM,CAAC,GACZ,KAAK,MAAM,CAAC;AAEhB,MAAI,IAAI,KAAK;AACb,MAAIJ,KAAII,SAAQ;AAChB,MAAI,IAAIA,SAAQ,IAAIJ;AACpB,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI;AACZ,SAAO,CAAC,GAAG,GAAGA,EAAC;AACjB;AACA,SAAS,eAAe,QAAQ,MAAM;AACpC,SAAO,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,OAAO,CAAC;AACvC;AACA,SAAS,eAAeI,QAAO,SAAS;AACtC,MAAI,IAAIA,SAAQ;AAChB,MAAI,IAAI,KAAK,MAAMA,SAAQ,OAAO;AAClC,SAAO,CAAC,GAAG,CAAC;AACd;AAEA,IAAI,OAAoB,OAAO,OAAO;AAAA,EACpC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACpTD,SAAS,OAAO;AACd,SAAO,CAAC,GAAG,CAAC;AACd;AACA,SAAS,MAAM;AACb,SAAO,CAAC,GAAG,CAAC;AACd;AACA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAClC;AACA,SAAS,SAAS,GAAG,GAAG;AACtB,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC5B;AACA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAClC;AACA,SAAS,SAAS,GAAG,GAAG;AACtB,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC5B;AACA,SAAS,MAAM,GAAG,GAAG;AACnB,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC5B;AACA,SAAS,IAAI,GAAG,GAAG;AACjB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC;AAMA,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjC;AAMA,SAAS,OAAO,GAAG;AACjB,SAAO,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5C;AACA,SAAS,SAAS,GAAG,GAAG;AACtB,SAAO,KAAK,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE;AAChF;AAEA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACzDD,SAASC,QAAO;AACd,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AACA,SAASC,OAAM;AACb,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AACA,SAASC,KAAI,GAAG,GAAG;AACjB,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/C;AACA,SAASC,UAAS,GAAG,GAAG;AACtB,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACtC;AACA,SAASC,KAAI,GAAG,GAAG;AACjB,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/C;AACA,SAASC,UAAS,GAAG,GAAG;AACtB,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACtC;AACA,SAASC,OAAM,GAAG,GAAG;AACnB,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACtC;AACA,SAASC,KAAI,GAAG,GAAG;AACjB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AACA,SAAS,MAAM,GAAG,GAAG;AACnB,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChC,MAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChC,MAAIC,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChC,SAAO,CAAC,GAAG,GAAGA,EAAC;AACjB;AAMA,SAASC,WAAU,GAAG;AACpB,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AAMA,SAASC,QAAO,GAAG;AACjB,SAAO,KAAK,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1D;AACA,SAASC,UAAS,GAAG,GAAG;AACtB,SAAO,KAAK,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE;AAChH;AAEA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX,MAAMX;AAAA,EACN,KAAKC;AAAA,EACL,KAAKC;AAAA,EACL,UAAUC;AAAA,EACV,KAAKC;AAAA,EACL,UAAUC;AAAA,EACV,OAAOC;AAAA,EACP,KAAKC;AAAA,EACL;AAAA,EACA,WAAWE;AAAA,EACX,QAAQC;AAAA,EACR,UAAUC;AACZ,CAAC;;;AC5DD,SAAS,QAAQC,SAAQ;AACvB,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAIC,WAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,MAAI,IAAI;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,WAAS,KAAK,GAAG,KAAKD,QAAO,QAAQ,MAAM,QAAQ;AACjD,MAAE,IAAIA,QAAO,EAAE;AACf,MAAE,IAAIA,QAAO,KAAK,CAAC;AACnB,MAAE,IAAIA,QAAO,KAAK,CAAC;AAEnB,QAAI,iBAAiBC,SAAQ,MAAM,EAAE,GACjC,kBAAkB,eAAe,gBAAgB,CAAC,GAClD,IAAI,gBAAgB,CAAC,GACrB,IAAI,gBAAgB,CAAC,GACrBC,KAAI,gBAAgB,CAAC;AAGzB,IAAAF,QAAO,EAAE,IAAI,EAAE,CAAC;AAChB,IAAAA,QAAO,KAAK,CAAC,IAAI,EAAE,CAAC;AAEpB,QAAI,WAAW,GAAG;AAChB,MAAAA,QAAO,KAAK,CAAC,IAAI,EAAEE,EAAC;AAAA,IACtB;AAAA,EACF;AAEA,SAAOF;AACT;AAOA,SAAS,QAAQA,SAAQ,MAAM;AAC7B,MAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,WAAY;AACnG,WAAO,KAAK,OAAO;AAAA,EACrB;AACA,MAAI,UAAU,OAAO;AACrB,MAAI,YAAY,IAAI,aAAaA,QAAO,SAAS,OAAO,OAAO;AAE/D,WAAS,MAAM,GAAG,MAAMA,QAAO,QAAQ,OAAO,MAAM;AAClD,QAAI,KAAK,MAAM,OAAO;AAEtB,cAAU,EAAE,IAAIA,QAAO,GAAG;AAC1B,cAAU,KAAK,CAAC,IAAIA,QAAO,MAAM,CAAC;AAElC,QAAI,SAAS,GAAG;AACd,gBAAU,KAAK,CAAC,IAAI,eAAe,EAAE;AAAA,IACvC;AAEA,QAAI,SAAS,GAAG;AACd,gBAAU,KAAK,CAAC,IAAIA,QAAO,MAAM,CAAC;AAClC,gBAAU,KAAK,CAAC,IAAI,eAAe,EAAE;AAAA,IACvC;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAASG,MAAK,SAAS,SAAS,QAAQ,GAAG;AACzC,WAAS,MAAM,GAAG,MAAM,QAAQ,QAAQ,OAAO;AAC7C,WAAO,GAAG,IAAI,KAAO,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC;AAAA,EACpD;AACF;AAYA,SAAS,UAAUH,SAAQ,mBAAmB;AAC5C,MAAI,SAAS,kBAAkB;AAE/B,WAAS,MAAM,GAAG,MAAMA,QAAO,QAAQ,OAAO,QAAQ;AACpD,IAAAA,QAAO,GAAG,KAAK,kBAAkB,CAAC;AAClC,IAAAA,QAAO,MAAM,CAAC,KAAK,kBAAkB,CAAC;AACtC,IAAAA,QAAO,MAAM,CAAC,KAAK,kBAAkB,CAAC;AAAA,EACxC;AAEA,SAAOA;AACT;AAGA,SAAS,OAAOA,SAAQ,UAAU;AAChC,MAAI,kBAAkB;AAAA,IACpB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,IAChB,GAAG,IAAI,WAAW,EAAE,SAAS;AAAA,EAC/B;AACA,MAAI,IAAI,IAAI,QAAQ;AAEpB,MAAI,wBAAwB,eAAe,eAAe,CAAC,GAAG,eAAe,GAAG,QAAQ,GACpF,IAAI,sBAAsB,GAC1BI,UAAS,sBAAsB;AAEnC,WAAS,MAAM,GAAG,MAAMJ,QAAO,QAAQ,OAAO,GAAG;AAC/C,MAAE,IAAIA,QAAO,GAAG,IAAII,QAAO,CAAC,GAAGJ,QAAO,MAAM,CAAC,IAAII,QAAO,CAAC,GAAGJ,QAAO,MAAM,CAAC,IAAII,QAAO,CAAC,CAAC;AACvF,MAAE,gBAAgB,CAAC;AACnB,IAAAJ,QAAO,GAAG,IAAI,EAAE,IAAII,QAAO,CAAC;AAC5B,IAAAJ,QAAO,MAAM,CAAC,IAAI,EAAE,IAAII,QAAO,CAAC;AAChC,IAAAJ,QAAO,MAAM,CAAC,IAAI,EAAE,IAAII,QAAO,CAAC;AAAA,EAClC;AAEA,SAAOJ;AACT;AACA,SAAS,IAAIA,SAAQ,QAAQ,UAAU;AACrC,WAAS,MAAM,GAAG,MAAM,GAAG,MAAMA,QAAO,QAAQ,OAAO,QAAQ,OAAO;AACpE,QAAI,WAAW,GAAG;AAChB,UAAI,MAAM,SAAS,CAACA,QAAO,GAAG,GAAGA,QAAO,MAAM,CAAC,GAAGA,QAAO,MAAM,CAAC,CAAC,GAAG,GAAG;AACvE,MAAAA,QAAO,IAAI,KAAK,GAAG;AAAA,IACrB,OAAO;AACL,MAAAA,QAAO,IAAI,SAAS,CAACA,QAAO,GAAG,GAAGA,QAAO,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;AAAA,IAC/D;AAAA,EACF;AAEA,SAAOA;AACT;AAKA,SAAS,OAAO,GAAG,QAAQ,UAAU,KAAK;AACxC,WAAS,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO,QAAQ,OAAO;AAC/D,QAAI,WAAW,GAAG;AAChB,YAAM,SAAS,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,GAAG;AAAA,IAC/C,OAAO;AACL,YAAM,SAAS,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,GAAG;AAAA,IAC3D;AAAA,EACF;AAEA,SAAO;AACT;AACA,SAAS,OAAO,GAAG,QAAQ,MAAM;AAC/B,MAAI,uBAAuB;AAAA,IACzB,QAAQ,CAAC,GAAG,GAAG,CAAC;AAAA,EAClB;AAEA,MAAI,wBAAwB,eAAe,eAAe,CAAC,GAAG,oBAAoB,GAAG,IAAI,GACrFI,UAAS,sBAAsB,QAC/BC,YAAW,sBAAsB;AAErC,WAAS,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO,QAAQ;AAM/C,MAAE,GAAG,KAAK,EAAE,GAAG,IAAID,QAAO,CAAC,MAAM,IAAIC,aAAYD,QAAO,CAAC;AACzD,MAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAIA,QAAO,CAAC,MAAM,IAAIC,aAAYD,QAAO,CAAC;AAEjE,QAAI,WAAW,GAAG;AAChB,QAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAIA,QAAO,CAAC,MAAM,IAAIC,aAAYD,QAAO,CAAC;AAAA,IACnE;AAAA,EACF;AAEA,SAAO;AACT;AACA,SAAS,OAAO,UAAU,QAAQ;AAChC,SAAO,OAAO,UAAU,QAAQ,SAAU,KAAK,OAAO;AACpD,QAAI,WAAW,GAAG;AAGhB,YAAME,KAAI,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,YAAM,IAAM,KAAK,KAAK;AAAA,IACxB;AAEA,WAAO;AAAA,EACT,GAAG,KAAK,CAAC;AACX;AACA,SAAS,KAAK,UAAU,QAAQ,UAAU;AAExC,MAAI,UAAU,WAAW,KAAK;AAAA,IAC5B,QAAQ,SAAS,SAAS;AAAA,EAC5B,GAAG,SAAU,GAAG,GAAG;AACjB,WAAO;AAAA,EACT,CAAC;AAED,UAAQ,KAAK,SAAU,GAAG,GAAG;AAC3B,QAAI,KAAK,SAAS,MAAM,IAAI,QAAQ,IAAI,SAAS,MAAM;AACvD,QAAI,KAAK,SAAS,MAAM,IAAI,QAAQ,IAAI,SAAS,MAAM;AACvD,WAAO,SAAS,IAAI,EAAE;AAAA,EACxB,CAAC;AAED,MAAI,aAAa,SAAS,MAAM,CAAC;AAEjC,WAAS,MAAM,GAAG,MAAM,QAAQ,QAAQ,OAAO;AAC7C,QAAI,MAAM,QAAQ,GAAG;AACrB,aAAS,IAAI,WAAW,MAAM,MAAM,QAAQ,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC;AAAA,EAC7E;AAEA,SAAO;AACT;AAEA,IAAI,SAAsB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA,MAAMH;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACrOD,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;;;ACgBA,IAAI,OAAO,SAASI,MAAK,GAAG,GAAGC,IAAG;AAChC,MAAI,QAAQ;AAEZ,kBAAgB,MAAMD,KAAI;AAE1B,kBAAgB,MAAM,QAAQ,SAAUE,IAAGC,IAAG;AAC5C,WAAO,MAAM,IAAID,KAAI,MAAM,IAAIC;AAAA,EACjC,CAAC;AAED,kBAAgB,MAAM,QAAQ,SAAUD,IAAGC,IAAGF,IAAG;AAC/C,WAAO,MAAM,IAAIC,KAAI,MAAM,IAAIC,KAAI,MAAM,IAAIF;AAAA,EAC/C,CAAC;AAED,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAIA;AACX;AAEA,IAAI,QAAQ,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;AAC3P,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG;AAEzpC,IAAI,OAAO,IAAI,MAAM,GAAG;AACxB,IAAI,QAAQ,IAAI,MAAM,GAAG;AAGzB,IAAI,OAAO,SAASG,MAAK,OAAO;AAC9B,MAAI,QAAQ,KAAK,QAAQ,GAAG;AAE1B,aAAS;AAAA,EACX;AAEA,UAAQ,KAAK,MAAM,KAAK;AAExB,MAAI,QAAQ,KAAK;AACf,aAAS,SAAS;AAAA,EACpB;AAEA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI;AAEJ,QAAI,IAAI,GAAG;AACT,UAAI,EAAE,CAAC,IAAI,QAAQ;AAAA,IACrB,OAAO;AACL,UAAI,EAAE,CAAC,IAAI,SAAS,IAAI;AAAA,IAC1B;AAEA,SAAK,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAM,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,EAAE;AAAA,EAC1C;AACF;AACA,KAAK,CAAC;AAQN,IAAI,KAAK,OAAO,KAAK,KAAK,CAAC,IAAI;AAC/B,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK;AAC9B,IAAI,KAAK,IAAI;AACb,IAAI,KAAK,IAAI;AAEb,IAAI,WAAW,SAASC,UAAS,KAAK,KAAK;AACzC,MAAI,IAAI,IAAI;AAGZ,MAAI,KAAK,MAAM,OAAO;AAEtB,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAIC,KAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,KAAK,IAAIA,MAAK;AAClB,MAAI,KAAK,MAAM,IAAI;AAEnB,MAAI,KAAK,MAAMA,KAAI;AAGnB,MAAI,IAAI;AAER,MAAI,KAAK,IAAI;AAEX,SAAK;AACL,SAAK;AAAA,EACP,OAAO;AAEL,SAAK;AACL,SAAK;AAAA,EACP;AAKA,MAAI,KAAK,KAAK,KAAK;AAEnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,OAAK;AACL,EAAAA,MAAK;AACL,MAAI,MAAM,MAAM,IAAI,KAAKA,EAAC,CAAC;AAC3B,MAAI,MAAM,MAAM,IAAI,KAAK,KAAKA,KAAI,EAAE,CAAC;AACrC,MAAI,MAAM,MAAM,IAAI,IAAI,KAAKA,KAAI,CAAC,CAAC;AAEnC,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAE9B,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EAChC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAE9B,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EAChC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK;AAE9B,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;AAAA,EAChC;AAIA,SAAO,MAAM,KAAK,KAAK;AACzB;AAEA,IAAI,WAAW,SAASC,UAAS,KAAK,KAAK,KAAK;AAC9C,MAAI,IAAI,IAAI,IAAI;AAGhB,MAAI,KAAK,MAAM,MAAM,OAAO;AAE5B,MAAI,IAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAID,KAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAIE,KAAI,KAAK,MAAM,MAAM,CAAC;AAC1B,MAAI,KAAK,IAAIF,KAAIE,MAAK;AACtB,MAAI,KAAK,MAAM,IAAI;AAEnB,MAAI,KAAK,MAAMF,KAAI;AACnB,MAAI,KAAK,MAAME,KAAI;AAGnB,MAAI,IAAI,IAAI;AAEZ,MAAI,IAAIC,KAAIC;AAEZ,MAAI,MAAM,IAAI;AACZ,QAAI,MAAM,IAAI;AACZ,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,MAAAD,MAAK;AACL,MAAAC,MAAK;AAAA,IACP,WAAW,MAAM,IAAI;AACnB,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,MAAAD,MAAK;AACL,MAAAC,MAAK;AAAA,IACP,OAAO;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,MAAAD,MAAK;AACL,MAAAC,MAAK;AAAA,IACP;AAAA,EACF,OAAO;AACL,QAAI,KAAK,IAAI;AACX,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,MAAAD,MAAK;AACL,MAAAC,MAAK;AAAA,IACP,WAAW,KAAK,IAAI;AAClB,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,MAAAD,MAAK;AACL,MAAAC,MAAK;AAAA,IACP,OAAO;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,WAAK;AACL,MAAAD,MAAK;AACL,MAAAC,MAAK;AAAA,IACP;AAAA,EACF;AAMA,MAAI,KAAK,KAAK,KAAK;AAEnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK;AACnB,MAAI,KAAK,KAAK,KAAK,IAAI;AAEvB,MAAI,KAAK,KAAKD,MAAK,IAAI;AACvB,MAAI,KAAK,KAAKC,MAAK,IAAI;AACvB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,MAAI,KAAK,KAAK,IAAI,IAAI;AACtB,MAAI,KAAK,KAAK,IAAI,IAAI;AAEtB,OAAK;AACL,EAAAJ,MAAK;AACL,EAAAE,MAAK;AACL,MAAI,MAAM,MAAM,IAAI,KAAKF,KAAI,KAAKE,EAAC,CAAC,CAAC;AACrC,MAAI,MAAM,MAAM,IAAI,KAAK,KAAKF,KAAI,KAAK,KAAKE,KAAI,EAAE,CAAC,CAAC;AACpD,MAAI,MAAM,MAAM,IAAI,KAAK,KAAKF,KAAIG,MAAK,KAAKD,KAAIE,GAAE,CAAC,CAAC;AACpD,MAAI,MAAM,MAAM,IAAI,IAAI,KAAKJ,KAAI,IAAI,KAAKE,KAAI,CAAC,CAAC,CAAC;AAEjD,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAEA,MAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAExC,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP,OAAO;AACL,UAAM;AACN,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE;AAAA,EACpC;AAIA,SAAO,MAAM,KAAK,KAAK,KAAK;AAC9B;AAGA,IAAI,UAAU,SAASG,SAAQ,GAAG,GAAG;AAEnC,MAAI,IAAI,KAAK,MAAM,CAAC,GAChB,IAAI,KAAK,MAAM,CAAC;AAEpB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AACtC,MAAI,MAAM,MAAM,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;AAC9C,MAAI,MAAM,MAAM,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC;AAC9C,MAAI,MAAM,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;AAEtD,MAAI,IAAI,KAAK,CAAC;AAEd,SAAO,KAAK,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC3D;AAEA,IAAI,UAAU,SAASC,SAAQ,GAAG,GAAGX,IAAG;AAEtC,MAAI,IAAI,KAAK,MAAM,CAAC,GAChB,IAAI,KAAK,MAAM,CAAC,GAChBY,KAAI,KAAK,MAAMZ,EAAC;AAEpB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,EAAAA,KAAIA,KAAIY;AAER,MAAI,IAAI;AACR,MAAI,IAAI;AACR,EAAAA,KAAIA,KAAI;AAER,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,KAAKA,EAAC,CAAC,CAAC,EAAE,KAAK,GAAG,GAAGZ,EAAC;AACpD,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,KAAKY,KAAI,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,GAAGZ,KAAI,CAAC;AAC5D,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,KAAKY,EAAC,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,GAAGZ,EAAC;AAC5D,MAAI,OAAO,MAAM,IAAI,KAAK,IAAI,IAAI,KAAKY,KAAI,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,GAAGZ,KAAI,CAAC;AACpE,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,KAAKY,EAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,GAAGZ,EAAC;AAC5D,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,KAAKY,KAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,GAAGZ,KAAI,CAAC;AACpE,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAKY,EAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,IAAI,GAAGZ,EAAC;AACpE,MAAI,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,KAAKY,KAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,IAAI,GAAGZ,KAAI,CAAC;AAE5E,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AACd,MAAIa,KAAI,KAAKb,EAAC;AAEd,SAAO,KAAK,KAAK,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,GAAGa,EAAC,GAAG,KAAK,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,GAAGA,EAAC,GAAG,CAAC;AACrH;AAEA,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAI,MAAM,KAAK,KAAK;AAEpB,SAAS,cAAcV,OAAM;AAC3B,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAAA,QAAO,KAAK,IAAIA,KAAI;AAAA,EACtB,WAAW,OAAOA,UAAS,UAAU;AACnC,QAAI,SAASA;AACb,IAAAA,QAAO;AAEP,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,MAAAA,SAAQA,SAAQ,IAAI,MAAM,OAAO,WAAW,CAAC,IAAI,OAAO;AAAA,IAC1D;AAAA,EACF;AAEA,MAAIA,UAAS,GAAG;AACd,IAAAA,QAAO;AAAA,EACT;AAEA,SAAOA;AACT;AAEA,SAAS,UAAUA,OAAM;AACvB,MAAI,QAAQ,cAAcA,KAAI;AAC9B,SAAO,WAAY;AACjB,QAAI,SAAS,QAAQ,QAAQ;AAC7B,YAAQ;AACR,WAAO,SAAS;AAAA,EAClB;AACF;AAEA,IAAI,YAAY,SAASW,WAAU,OAAO;AACxC,MAAI,QAAQ;AAEZ,kBAAgB,MAAMA,UAAS;AAE/B,kBAAgB,MAAM,QAAQ,CAAC;AAE/B,kBAAgB,MAAM,QAAQ,SAAUX,OAAM;AAC5C,UAAM,OAAOA;AACb,UAAM,QAAQ,UAAUA,KAAI;AAAA,EAC9B,CAAC;AAED,kBAAgB,MAAM,SAAS,UAAU,KAAK,IAAI,CAAC;AAEnD,OAAK,KAAK,KAAK;AACjB;AACA,IAAI,aAAa,IAAI,UAAU,KAAK,OAAO,CAAC;AAK5C,IAAI,gBAAgB;AAAA,EAClB,QAAQ;AAAA,EACR,QAAQ,CAAC,GAAG,GAAG,CAAC;AAClB;AAIA,SAAS,SAASY,SAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,wBAAwB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GAChF,SAAS,sBAAsB,QAC/BC,UAAS,sBAAsB;AAEnC,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,IAAI,MAAM;AAClB,QAAI,IAAI,IAAI,MAAM;AAClB,QAAI,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC;AAC/B,QAAI,MAAM,MAAM;AAChB,IAAAA,QAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,SAASC,QAAO,CAAC;AAC/D,IAAAD,QAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,SAASC,QAAO,CAAC;AACnE,IAAAD,QAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,SAASC,QAAO,CAAC;AAAA,EACrD;AAEA,SAAOD;AACT;AAEA,SAAS,SAASA,SAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,yBAAyB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GACjF,SAAS,uBAAuB,QAChCC,UAAS,uBAAuB;AAEpC,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACnC,QAAI,IAAI,IAAI,MAAM,IAAI,IAAI;AAC1B,QAAI,IAAI,IAAI,MAAM,IAAI,IAAI;AAC1B,QAAIf,KAAI,IAAI,MAAM,IAAI,IAAI;AAC1B,QAAI,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,IAAIA,KAAIA,EAAC;AACzC,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,IAAAA,KAAI,IAAIA,KAAI;AACZ,IAAAe,QAAO,CAAC,IAAI,IAAI,SAASC,QAAO,CAAC;AACjC,IAAAD,QAAO,IAAI,CAAC,IAAI,IAAI,SAASC,QAAO,CAAC;AACrC,IAAAD,QAAO,IAAI,CAAC,IAAIf,KAAI,SAASgB,QAAO,CAAC;AAAA,EACvC;AAEA,SAAOD;AACT;AAKA,IAAI,gBAAgB;AAAA,EAClB,QAAQ;AAAA,EACR,QAAQ,CAAC,GAAG,CAAC;AACf;AAEA,SAAS,SAASA,SAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,wBAAwB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GAChF,SAAS,sBAAsB,QAC/BC,UAAS,sBAAsB;AAEnC,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,IAAI,SAAS,KAAK,KAAK,IAAI,MAAM,CAAC;AACtC,QAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,IAAAA,QAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAIC,QAAO,CAAC;AAC1C,IAAAD,QAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAIC,QAAO,CAAC;AAAA,EAChD;AAEA,SAAOD;AACT;AACA,SAAS,SAASA,SAAQ,QAAQ;AAChC,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,yBAAyB,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM,GACjF,SAAS,uBAAuB,QAChCC,UAAS,uBAAuB;AAEpC,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,QAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,IAAAA,QAAO,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,SAASC,QAAO,CAAC;AAC/C,IAAAD,QAAO,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,SAASC,QAAO,CAAC;AAAA,EACrD;AAEA,SAAOD;AACT;AAKA,IAAI,cAAc;AAAA,EAChB,OAAO;AAAA,EACP,QAAQ,CAAC,GAAG,CAAC;AACf;AACA,SAAS,OAAOA,SAAQ,MAAM;AAC5B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,oBAAoB,eAAe,eAAe,CAAC,GAAG,WAAW,GAAG,IAAI,GACxE,QAAQ,kBAAkB,OAC1BC,UAAS,kBAAkB;AAE/B,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,IAAAA,QAAO,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AAClD,IAAAD,QAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AAAA,EACxD;AAEA,SAAOD;AACT;AACA,SAAS,OAAOA,SAAQ,MAAM;AAC5B,SAAOA;AACT;AAKA,SAAS,MAAMA,SAAQ,KAAK;AAC1B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,kBAAkB,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,GAAG,GACpE,QAAQ,gBAAgB,OACxBC,UAAS,gBAAgB;AAE7B,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,IAAAA,QAAO,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AAClD,IAAAD,QAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AACtD,IAAAD,QAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AAAA,EACxD;AAEA,SAAOD;AACT;AACA,IAAI,aAAa;AAAA,EACf,OAAO;AAAA,EACP,QAAQ,CAAC,GAAG,GAAG,CAAC;AAClB;AACA,SAAS,MAAMA,SAAQ,KAAK;AAC1B,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE9E,MAAI,mBAAmB,eAAe,eAAe,CAAC,GAAG,UAAU,GAAG,GAAG,GACrE,QAAQ,iBAAiB,OACzBC,UAAS,iBAAiB;AAE9B,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AACvD,MAAI,QAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAEvD,WAAS,IAAI,GAAG,IAAID,QAAO,QAAQ,KAAK,GAAG;AACzC,IAAAA,QAAO,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AAClD,IAAAD,QAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AACtD,IAAAD,QAAO,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQC,QAAO,CAAC;AAAA,EACxD;AAEA,SAAOD;AACT;AAEA,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC5jBD,IAAI,OAAO,SAASE,MAAK,GAAG;AAC1B,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC5E,MAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,KAAK,IAAI,KAAK;AAC1F,SAAO,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK;AACnF;AAKA,IAAI,MAAM,SAASC,KAAI,GAAG;AACxB,SAAO,KAAK,IAAI,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;AACrD;AAMA,SAAS,KAET,SAEA,MAEA,QAAQ;AACN,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACrF,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACnF,MAAIC,UAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC9E,MAAI,MAAM,cAAc;AACxB,MAAI,QAAQ,WAAW,OAAW,SAAQ,SAAS,CAAC;AACpD,MAAI,QAAQ,OAAO,GAAG,MAAM,OAAW,SAAQ,OAAO,GAAG,IAAI;AAE7D,MAAI,KAAK,IAAI,QAAQ,IAAI,IAAI,MAAM,KAAK,KAAK;AAC3C,YAAQ,IAAI,IAAI;AAChB,WAAO;AAAA,EACT;AAEA,eAAa,KAAK,IAAI,MAAQ,UAAU;AACxC,MAAI,QAAQ,IAAI;AAChB,MAAI,IAAIA,QAAO,QAAQ,KAAK;AAC5B,MAAI,SAAS,QAAQ,IAAI,IAAI;AAC7B,MAAI,aAAa;AAEjB,MAAI,YAAY,WAAW;AAC3B,WAAS,KAAK,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS,GAAG,SAAS;AACzD,WAAS,QAAQ,IAAI,IAAI;AACzB,MAAI,QAAQ,QAAQ,OAAO,GAAG,IAAI,QAAQ,UAAU;AACpD,UAAQ,OAAO,GAAG,KAAK,QAAQ,OAAO,GAAG,IAAI,QAAQ,QAAQ;AAC7D,MAAI,SAAS,UAAU,SAAS,QAAQ;AAExC,MAAI,aAAa,QAAQ,IAAI,IAAI,MAAQ,SAAS,YAAY;AAC5D,aAAS;AACT,YAAQ,OAAO,GAAG,KAAK,SAAS,cAAc;AAAA,EAChD;AAEA,UAAQ,IAAI,IAAI;AAChB,SAAO;AACT;AAKA,SAAS,UAAU,SAAS,MAAM,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AAClF,SAAO,KAAK,SAAS,MAAM,QAAQ,IAAI,IAAI,WAAW,QAAQ,IAAI,GAAG,MAAM,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACxH;AAKA,IAAI,MAAmB,IAAI,QAAQ;AACnC,IAAI;AAAJ,IAAQ;AACR,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,OAAO,WAAW,SAAU,KAAI,UAAU,MAAM;AAAA,WAAW,MAAM,QAAQ,MAAM,EAAG,KAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,KAAI,KAAK,MAAM;AACxI,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,SAAO,MAAM;AACf;AAKA,IAAI,MAAmB,IAAI,QAAQ;AACnC,IAAI;AAAJ,IAAQ;AAAR,IAAY;AACZ,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,OAAO,WAAW,SAAU,KAAI,UAAU,MAAM;AAAA,WAAW,MAAM,QAAQ,MAAM,EAAG,KAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,KAAI,KAAK,MAAM;AACnJ,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,SAAO,MAAM,MAAM;AACrB;AAKA,IAAI,MAAmB,IAAI,QAAQ;AACnC,IAAI;AAAJ,IAAQ;AAAR,IAAY;AAAZ,IAAgB;AAChB,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,OAAO,WAAW,SAAU,KAAI,UAAU,MAAM;AAAA,WAAW,MAAM,QAAQ,MAAM,EAAG,KAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,KAAI,KAAK,MAAM;AAC9J,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,SAAO,MAAM,MAAM,MAAM;AAC3B;AAKA,IAAI,MAAmB,IAAI,MAAM;AACjC,IAAI;AAAJ,IAAQ;AAAR,IAAY;AACZ,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,MAAM,QAAQ,MAAM,EAAG,KAAI,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,KAAI,KAAK,MAAM;AACnG,OAAK,UAAU,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC5E,OAAK,UAAU,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC5E,OAAK,UAAU,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC5E,SAAO,MAAM,MAAM;AACrB;AAKA,IAAI,MAAmB,IAAI,MAAM;AACjC,IAAI;AAAJ,IAAQ;AAAR,IAAY;AACZ,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,kBAAkB,MAAO,KAAI,KAAK,MAAM;AAAA,WAAW,MAAM,QAAQ,MAAM,EAAG,KAAI,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,KAAI,IAAI,MAAM;AAC7I,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,OAAK,KAAK,SAAS,KAAK,IAAI,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvE,SAAO,MAAM,MAAM;AACrB;AAUA,IAAI,KAAkB,IAAI,WAAW;AACrC,IAAI,WAAwB,IAAI,QAAQ;AACxC,IAAI,aAA0B,IAAI,QAAQ;AAC1C,IAAI,UAAuB,IAAI,QAAQ;AACvC,IAAI;AAAJ,IAAQ;AAAR,IAAY;AAAZ,IAAgB;AAChB,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,MAAM;AACV,MAAI,MAAM,QAAQ,MAAM,EAAG,IAAG,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,IAAG,KAAK,MAAM;AACjG,MAAI,QAAQ,QAAQ,IAAI,EAAE,IAAI,IAAI,IAAI;AACtC,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACR,OAAK,KAAK,SAAS,KAAK,GAAG,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACtE,OAAK,KAAK,SAAS,KAAK,GAAG,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACtE,OAAK,KAAK,SAAS,KAAK,GAAG,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACtE,OAAK,KAAK,SAAS,KAAK,GAAG,GAAG,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAEtE,WAAS,IAAI,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,EAAE,UAAU;AACnE,aAAW,IAAI,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY,IAAI,OAAO,UAAU;AAEzG,UAAQ,KAAK,QAAQ,EAAE,eAAe,WAAW,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,CAAC;AACvF,MAAI,OAAO,cAAc,QAAQ;AACjC,MAAI,OAAO,cAAc,QAAQ;AACjC,MAAI,OAAO,cAAc,QAAQ;AACjC,MAAI,OAAO,cAAc,QAAQ;AACjC,UAAQ,IAAI,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AAC1D,SAAO,MAAM,MAAM,MAAM;AAC3B;AAKA,IAAI,YAAyB,IAAI,UAAU;AAC3C,IAAI;AAAJ,IAAQ;AAAR,IAAY;AACZ,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,MAAM,QAAQ,MAAM,EAAG,WAAU,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,MAAO,WAAU,KAAK,MAAM;AACpG,OAAK,KAAK,SAAS,UAAU,UAAU,QAAQ,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACvF,OAAK,UAAU,SAAS,OAAO,UAAU,KAAK,YAAY,OAAO,UAAUA,SAAQ,GAAG;AACtF,OAAK,UAAU,SAAS,SAAS,UAAU,OAAO,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC1F,SAAO,MAAM,MAAM;AACrB;AAKA,IAAI,MAAmB,IAAI,QAAQ;AACnC,IAAI,OAAoB,IAAI,QAAQ;AACpC,IAAI,OAAoB,IAAI,WAAW;AACvC,IAAI,OAAoB,IAAI,QAAQ;AACpC,IAAI;AAAJ,IAAQ;AAAR,IAAY;AACZ,SAAS,MAAM,SAAS,QAAQ,YAAY,OAAO,UAAUA,SAAQ,KAAK;AACxE,MAAI,MAAM;AAEV,MAAI,IAAI,WAAW,QAAW;AAC5B,QAAI,SAAS;AAAA,MACX,UAAU,IAAI,QAAQ;AAAA,MACtB,UAAU,IAAI,WAAW;AAAA,MACzB,OAAO,IAAI,QAAQ;AAAA,IACrB;AACA,YAAQ,UAAU,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,IAAI,OAAO,KAAK;AAAA,EAC9E;AAEA,MAAI,MAAM,QAAQ,MAAM,EAAG,KAAI,IAAI,MAAM,KAAK,mBAAmB,MAAM,CAAC;AAAA,MAAO,KAAI,KAAK,MAAM;AAC9F,MAAI,UAAU,MAAM,MAAM,IAAI;AAC9B,OAAK,MAAM,IAAI,OAAO,UAAU,MAAM,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC9E,OAAK,MAAM,IAAI,OAAO,UAAU,MAAM,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC9E,OAAK,MAAM,IAAI,OAAO,OAAO,MAAM,YAAY,OAAO,UAAUA,SAAQ,GAAG;AAC3E,UAAQ,QAAQ,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,IAAI,OAAO,KAAK;AAC1E,SAAO,MAAM,MAAM;AACrB;AAEA,IAAI,SAAsB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACvOD,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,WAAY,iBAAgB,UAAU,UAAU;AACtD;AAEA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASC,iBAAgBC,IAAG;AAC5F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AAEA,SAAS,uBAAuB,MAAM;AACpC,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAO;AACT;AAEA,SAAS,2BAA2B,MAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAChF;AAEA,SAAO,uBAAuB,IAAI;AACpC;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAC1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GAC/B;AAEJ,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AACtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AAEA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AAEA,IAAI,uBAAoC,SAAU,uBAAuB;AACvE,YAAUC,uBAAsB,qBAAqB;AAErD,MAAI,SAAS,aAAaA,qBAAoB;AAE9C,WAASA,wBAAuB;AAC9B,QAAI;AAEJ,QAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,QAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEnF,oBAAgB,MAAMA,qBAAoB;AAE1C,YAAQ,OAAO,KAAK,IAAI;AAExB,QAAI,KAAK,QAAQ,IAAI;AAErB,QAAI,KAAK,SAAS,IAAI;AAEtB,QAAI,KAAK,SAAS;AAElB,QAAI,MAAM,QAAQ,UAAU;AAE5B,QAAI,KAAK,SAAS;AAElB,QAAI,MAAM,SAAS,UAAU;AAE7B,QAAI,YAAY,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC/D,QAAI,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AACzC,QAAI,IAAI,CAAC,KAAK,WAAW,KAAK,GAAG,KAAK,WAAW,KAAK,GAAG,WAAW,GAAG,WAAW,GAAG,KAAK,WAAW,KAAK,GAAG,GAAG,GAAG,KAAK,WAAW,KAAK,GAAG,GAAG,KAAK,WAAW,KAAK,GAAG,GAAG,CAAC;AAC1K,QAAI,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC5H,QAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI;AAEnC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAK,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC;AAC5B,WAAK,IAAI,IAAI,KAAK,CAAC;AACnB,WAAK,IAAI,KAAK,IAAI,IAAI,KAAK;AAC3B,WAAK,IAAI,IAAI,KAAK;AAElB,eAASC,KAAI,GAAGA,MAAK,UAAUA,MAAK;AAClC,cAAM,KAAK,KAAK,KAAK,IAAIA,KAAI;AAC7B,cAAM,KAAK,IAAI,GAAG;AAClB,cAAM,KAAK,IAAI,GAAG;AAClB,kBAAU,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,KAAK,CAAC;AACtD,YAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAErC,YAAIA,KAAI,UAAU;AAChB,iBAAO,WAAW,KAAK,IAAIA,KAAI;AAC/B,kBAAQ,KAAK,GAAG,KAAK,MAAM,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAEA,UAAM,SAAS,IAAU,gBAAgB,IAAI,YAAY,OAAO,GAAG,CAAC,CAAC;AAErE,UAAM,aAAa,YAAY,IAAU,gBAAgB,IAAI,aAAa,SAAS,GAAG,CAAC,CAAC;AAExF,UAAM,aAAa,MAAM,IAAU,gBAAgB,IAAI,aAAa,GAAG,GAAG,CAAC,CAAC;AAE5E,WAAO;AAAA,EACT;AAEA,SAAOD;AACT,EAAQ,cAAc;AAGtB,SAAS,cAAc,gBAAgB;AACrC,MAAI,MAAM,CAAC;AACX,MAAI,WAAW,CAAC;AAEhB,WAAS,IAAI,GAAG,IAAI,eAAe,WAAW,SAAS,MAAM,SAAS,GAAG,KAAK;AAC5E,QAAI,IAAI,eAAe,WAAW,SAAS,MAAM,IAAI,IAAI,CAAC;AAC1D,QAAI,IAAI,eAAe,WAAW,SAAS,MAAM,IAAI,IAAI,CAAC;AAC1D,QAAIE,KAAI,eAAe,WAAW,SAAS,MAAM,IAAI,IAAI,CAAC;AAC1D,aAAS,KAAK,IAAU,QAAQ,GAAG,GAAGA,EAAC,CAAC;AAAA,EAC1C;AAEA,MAAI,gBAAgB,SAAS,IAAI,eAAe;AAEhD,WAAS,KAAK,GAAG,KAAK,cAAc,SAAS,GAAG,MAAM;AACpD,QAAI,MAAM,IAAU,SAAS,SAAS,KAAK,IAAI,CAAC,GAAG,SAAS,KAAK,IAAI,CAAC,GAAG,SAAS,KAAK,IAAI,CAAC,CAAC;AAC7F,QAAI,SAAS,IAAI,UAAU,IAAU,QAAQ,CAAC;AAE9C,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,SAAS,cAAc,KAAK,IAAI,CAAC;AAErC,UAAI,OAAO,UAAU,MAAM,OAAO,QAAQ,KAAK,OAAO,QAAQ,KAAK,KAAK;AACtE,YAAI,iBAAiB,OAAO,QAAQ,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAC9D,iBAAS;AAAA,UACP,GAAG,OAAO;AAAA,UACV,KAAK,OAAO;AAAA,UACZ,OAAO,cAAc,cAAc,EAAE;AAAA,QACvC;AAAA,MACF;AAEA,UAAI,OAAO,UAAU,KAAK,MAAM,gBAAgB,MAAM,EAAE,QAAQ,KAAK,KAAK,GAAG;AAC3E,eAAO,QAAQ,CAAC,KAAK;AAAA,MACvB;AAEA,UAAI,cAAc,aAAa,MAAM;AACrC,UAAI,KAAK,IAAI,YAAY,GAAG,IAAI,YAAY,CAAC;AAAA,IAC/C;AAAA,EACF;AAEA,MAAI,eAAe,WAAW,GAAI,QAAO,eAAe,WAAW;AACnE,iBAAe,aAAa,MAAM,IAAU,uBAAuB,KAAK,CAAC,CAAC;AAC1E,iBAAe,WAAW,GAAG,cAAc;AAC3C,SAAO;AACT;AAEA,SAAS,gBAAgB,UAAU;AACjC,MAAI,IAAI,KAAK,KAAK,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,CAAC;AAC7F,SAAO;AAAA,IACL;AAAA,IACA,KAAK,KAAK,KAAK,SAAS,IAAI,CAAC;AAAA,IAC7B,OAAO,KAAK,MAAM,SAAS,GAAG,SAAS,CAAC;AAAA,EAC1C;AACF;AAEA,SAAS,aAAa,YAAY;AAChC,SAAO;AAAA,IACL,GAAG,WAAW,MAAM,KAAK;AAAA,IACzB,IAAI,WAAW,QAAQ,KAAK,OAAO,IAAI,KAAK;AAAA,EAC9C;AACF;AAIA,SAAS,WAAW,gBAAgB;AAClC,iBAAe,mBAAmB;AAClC,MAAI,WAAW,eAAe,YAAY,QAAQ,IAAU,QAAQ,CAAC;AACrE,MAAI,UAAU,KAAK,IAAI,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AACzD,MAAI,cAAc,IAAU,YAAY,SAAS,SAAS,OAAO;AACjE,MAAI,OAAO,IAAU,KAAK,WAAW;AACrC,OAAK,SAAS,IAAI,GAAG,GAAG,CAAC;AACzB,OAAK,kBAAkB,MAAM,KAAK;AAClC,MAAI,kBAAkB,KAAK,OAAO,MAAM,EAAE,OAAO;AACjD,MAAI,SAAS,IAAU,KAAK,IAAU,QAAQ,CAAC,UAAU,GAAG,CAAC,UAAU,GAAG,CAAC,UAAU,CAAC,GAAG,IAAU,QAAQ,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC,CAAC;AAEjJ,cAAY,gBAAgB,iBAAiB,QAAQ,OAAO;AAE5D,iBAAe,WAAW,GAAG,cAAc;AAC3C,SAAO;AACT;AAEA,SAAS,YAAY,MAAM,iBAAiB,MAAM,eAAe;AAC/D,MAAI,SAAS,CAAC;AACd,SAAO,SAAS,IAAI,KAAK,WAAW,SAAS,MAAM,SAAS;AAG5D,MAAI,UAAU,SAASC,SAAQC,KAAIC,KAAIC,KAAI;AAEzC,IAAAF,IAAG,aAAa,eAAe;AAC/B,IAAAC,IAAG,aAAa,eAAe;AAC/B,IAAAC,IAAG,aAAa,eAAe;AAE/B,QAAI,IAAI,IAAU,QAAQ;AAC1B,MAAE,aAAaD,IAAG,MAAM,EAAE,IAAID,GAAE,GAAGC,IAAG,MAAM,EAAE,IAAIC,GAAE,CAAC,EAAE,UAAU;AACjE,MAAE,IAAI,KAAK,IAAI,EAAE,CAAC;AAClB,MAAE,IAAI,KAAK,IAAI,EAAE,CAAC;AAClB,MAAE,IAAI,KAAK,IAAI,EAAE,CAAC;AAClB,QAAI,MAAM,IAAU,QAAQ;AAC5B,QAAI,MAAM,IAAU,QAAQ;AAC5B,QAAI,MAAM,IAAU,QAAQ;AAE5B,QAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;AAC1B,UAAI,KAAKF,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAK,KAAK,IAAI,IAAIA,IAAG,KAAK;AAC9B,UAAI,KAAKC,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAK,KAAK,IAAI,IAAIA,IAAG,KAAK;AAC9B,UAAI,KAAKC,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAK,KAAK,IAAI,IAAIA,IAAG,KAAK;AAAA,IAChC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;AACjC,UAAI,KAAKF,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKA,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKC,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKA,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKC,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKA,IAAG,IAAI,KAAK,IAAI,KAAK;AAAA,IAChC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;AACjC,UAAI,KAAKF,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKA,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKC,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKA,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKC,IAAG,IAAI,KAAK,IAAI,KAAK;AAC9B,UAAI,KAAKA,IAAG,IAAI,KAAK,IAAI,KAAK;AAAA,IAChC;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAK,OAAO;AAEd,aAAS,KAAK,GAAG,KAAK,KAAK,MAAM,MAAM,QAAQ,MAAM,GAAG;AACtD,UAAI,OAAO,KAAK,MAAM,MAAM,EAAE;AAC9B,UAAI,OAAO,KAAK,MAAM,MAAM,KAAK,CAAC;AAClC,UAAI,OAAO,KAAK,MAAM,MAAM,KAAK,CAAC;AAClC,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,IAAI;AACjD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,OAAO,CAAC;AACrD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,OAAO,CAAC;AACrD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,IAAI;AACjD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,OAAO,CAAC;AACrD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,OAAO,CAAC;AACrD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,IAAI;AACjD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,OAAO,CAAC;AACrD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,IAAI,OAAO,CAAC;AACrD,UAAI,KAAK,IAAU,QAAQ,KAAK,KAAK,GAAG;AACxC,UAAI,KAAK,IAAU,QAAQ,KAAK,KAAK,GAAG;AACxC,UAAI,KAAK,IAAU,QAAQ,KAAK,KAAK,GAAG;AACxC,UAAI,MAAM,QAAQ,IAAI,IAAI,EAAE;AAC5B,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3B,aAAO,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI;AAC/B,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3B,aAAO,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI;AAC/B,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3B,aAAO,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI;AAAA,IACjC;AAAA,EACF,OAAO;AACL,aAAS,MAAM,GAAG,MAAM,KAAK,WAAW,SAAS,MAAM,QAAQ,OAAO,GAAG;AACvE,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,GAAG;AAC5C,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AAChD,UAAI,MAAM,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AAChD,UAAI,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AACjD,UAAI,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AACjD,UAAI,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AACjD,UAAI,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AACjD,UAAI,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AACjD,UAAI,OAAO,KAAK,WAAW,SAAS,MAAM,MAAM,CAAC;AAEjD,UAAI,KAAK,IAAU,QAAQ,KAAK,KAAK,GAAG;AAExC,UAAI,MAAM,IAAU,QAAQ,MAAM,MAAM,IAAI;AAE5C,UAAI,MAAM,IAAU,QAAQ,MAAM,MAAM,IAAI;AAE5C,UAAI,OAAO,QAAQ,IAAI,KAAK,GAAG;AAE/B,UAAI,OAAO,MAAM;AAEjB,UAAI,QAAQ,OAAO;AAEnB,UAAI,QAAQ,OAAO;AAEnB,aAAO,IAAI,IAAI,IAAI,KAAK,IAAI;AAC5B,aAAO,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI;AAChC,aAAO,IAAI,KAAK,IAAI,KAAK,IAAI;AAC7B,aAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI;AACjC,aAAO,IAAI,KAAK,IAAI,KAAK,IAAI;AAC7B,aAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI;AAAA,IACnC;AAAA,EACF;AAEA,MAAI,KAAK,WAAW,GAAI,QAAO,KAAK,WAAW;AAC/C,OAAK,aAAa,MAAM,IAAU,uBAAuB,QAAQ,CAAC,CAAC;AACrE;AAEA,IAAI,WAAwB,OAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC3TD,SAAS,gBAAgBC,SAAQ;AAC/B,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,MAAIC,KAAI,CAAC;AAET,WAAS,IAAI,GAAGC,KAAI,GAAG,IAAIF,QAAO,QAAQ,KAAK,QAAQE,MAAK;AAC1D,QAAI,WAAW,GAAG;AAChB,MAAAD,GAAEC,EAAC,IAAI,IAAI,QAAQF,QAAO,CAAC,GAAGA,QAAO,IAAI,CAAC,GAAGA,QAAO,IAAI,CAAC,CAAC;AAAA,IAC5D,OAAO;AACL,MAAAC,GAAEC,EAAC,IAAI,IAAI,QAAQF,QAAO,CAAC,GAAGA,QAAO,IAAI,CAAC,CAAC;AAAA,IAC7C;AAAA,EACF;AAEA,SAAOC;AACT;AAQA,SAAS,gBAAgB,aAAa;AACpC,MAAI,IAAI,YAAY;AACpB,MAAI,SAAS,YAAY,CAAC,EAAE,eAAe,GAAG,IAAI,IAAI;AACtD,MAAID,UAAS,IAAI,aAAa,IAAI,MAAM;AAExC,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAIE,KAAI,IAAI;AACZ,IAAAF,QAAOE,EAAC,IAAI,YAAY,CAAC,EAAE;AAC3B,IAAAF,QAAOE,KAAI,CAAC,IAAI,YAAY,CAAC,EAAE;AAE/B,QAAI,WAAW,GAAG;AAChB,MAAAF,QAAOE,KAAI,CAAC,IAAI,YAAY,CAAC,EAAE;AAAA,IACjC;AAAA,EACF;AAEA,SAAOF;AACT;AAEA,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AACF,CAAC;;;ACfD,IAAM,UAAU,IAAI,mBAAoB,IAAK,GAAG,GAAG,IAAK,GAAG,CAAE;AAI7D,IAAM,6BAAN,cAAyC,eAAe;AAAA,EAEvD,cAAc;AAEb,UAAM;AAEN,SAAK,aAAc,YAAY,IAAI,uBAAwB,CAAE,IAAK,GAAG,GAAG,IAAK,IAAK,GAAG,GAAG,IAAK,CAAE,GAAG,CAAE,CAAE;AACtG,SAAK,aAAc,MAAM,IAAI,uBAAwB,CAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,GAAG,CAAE,CAAE;AAAA,EAEhF;AAED;AAEA,IAAM,YAAY,IAAI,2BAA2B;;;AExDjD,IAAM,mDAAN,cAAyC,eAAA;EAGvC,cAAc;AACZ,UAAK;AAHP,0CAAiB,IAAI,OAAA;AAInB,SAAK,aAAa,YAAY,IAAI,gBAAsB,IAAI,aAAa;MAAC;MAAI;MAAI;MAAG;MAAI;MAAI;KAAE,GAAG,CAAA,CAAA;AAClG,SAAK,aAAa,MAAM,IAAI,gBAAsB,IAAI,aAAa;MAAC;MAAG;MAAG;MAAG;MAAG;MAAG;KAAE,GAAG,CAAA,CAAA;EAC1F;EAEA,wBAAwB;EAAC;AAC3B;AAEA,IAAM,kCAA4B,IAAI,iDAAA;AACtC,IAAM,gCAA0B,IAAI,mBAAA;AAE7B,IAAM,4CAAN,MAAM;EACX,YAAY,UAAU;AACpB,SAAK,QAAQ,IAAI,KAAW,iCAAW,QAAA;AACvC,SAAK,MAAM,gBAAgB;EAC7B;EAEA,OAAO,UAAU;AACf,aAAS,OAAO,KAAK,OAAO,6BAAA;EAC9B;EAEA,IAAI,WAAW;AACb,WAAO,KAAK,MAAM;EACpB;EAEA,IAAI,SAAS,OAAO;AAClB,SAAK,MAAM,WAAW;EACxB;EAEA,UAAU;AACR,SAAK,MAAM,SAAS,QAAO;AAC3B,SAAK,MAAM,SAAS,QAAO;EAC7B;AACF;ACtCA,IAAM,4CAAe;EAEjB,UAAU;IAEN,gBAAgB;MAAE,OAAO;IAAK;IAC9B,cAAc;MAAE,OAAO;IAAK;IAC5B,eAAe;MAAE,OAAO;IAAK;IAC7B,WAAW;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACxD,WAAW;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACxD,eAAe;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC5D,uBAAuB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACpE,iBAAiB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC9D,aAAa;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC1D,cAAc;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC3D,kBAAkB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC/D,QAAQ;MAAE,OAAO;IAAI;IACrB,WAAW;MAAE,OAAO,CAAA;IAAG;IACvB,aAAa;MAAE,OAAO;IAAK;IAC3B,mBAAmB;MAAE,OAAO;IAAI;IAChC,UAAU;MAAE,OAAO;IAAI;IACvB,QAAQ;MAAE,OAAO;IAAI;IACrB,OAAO;MAAE,OAAO;IAAO;IACvB,SAAS;MAAE,OAAO;IAAM;IACxB,qBAAqB;MAAE,OAAO;IAAM;IACpC,SAAS;MAAE,OAAO;IAAI;EAC1B;EACA,YAAY;EACZ,WAAW;EACX;;IAAyB;;;;;;;EAOzB;;IAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2O/B;AC9QA,IAAM,4CAAmB;EACrB,UAAU;IAEN,gBAAgB;MAAE,OAAO;IAAK;IAC9B,cAAc;MAAE,OAAO;IAAK;IAC5B,YAAY;MAAE,OAAO;IAAK;IAC1B,uBAAuB;MAAE,OAAO;IAAK;IACrC,sBAAsB;MAAE,OAAO;IAAK;IACpC,2BAA2B;MAAE,OAAO;IAAK;IACzC,qBAAqB;MAAE,OAAO;IAAM;IACpC,WAAW;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACxD,WAAW;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACxD,uBAAuB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACpE,iBAAiB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC9D,aAAa;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC1D,cAAc;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC3D,SAAS;MAAE,OAAuB,IAAI,QAAc,GAAG,GAAG,CAAA;IAAG;IAC7D,aAAa;MAAE,OAAO;IAAK;IAC3B,oBAAoB;MAAE,OAAO;IAAK;IAClC,QAAQ;MAAE,OAAO;IAAI;IACrB,aAAa;MAAE,OAAO;IAAK;IAC3B,cAAc;MAAE,OAAO;IAAI;IAC3B,mBAAmB;MAAE,OAAO;IAAM;IAClC,SAAS;MAAE,OAAO;IAAM;IACxB,QAAQ;MAAE,OAAO;IAAI;IACrB,OAAO;MAAE,OAAO;IAAO;IACvB,qBAAqB;MAAE,OAAO;IAAM;IACpC,UAAU;MAAE,OAAO;IAAI;IACvB,mBAAmB;MAAE,OAAO;IAAI;IAChC,OAAO;MAAE,OAAO;IAAM;IACtB,UAAU;MAAE,OAAO;IAAM;IACzB,cAAc;MAAE,OAAO;IAAI;IAC3B,WAAW;MAAE,OAAO;IAAS;IAC7B,UAAU;MAAE,OAAO;IAAS;IAC5B,iBAAiB;MAAE,OAAO;IAAK;IAC/B,WAAW;MAAE,OAAO;IAAI;EAE5B;EACA,YAAY;EACZ,WAAW;EAEX;;IAAyB;;;;;;;EAMzB;;IAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsP/B;ACrSA,IAAM,4CAAe;EACjB,UAAU;IAEN,gBAAgB;MAAE,OAAO;IAAK;IAC9B,cAAc;MAAE,OAAO;IAAK;IAC5B,YAAY;MAAE,OAAO;IAAK;IAC1B,WAAW;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACxD,WAAW;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACxD,uBAAuB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACpE,iBAAiB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC9D,aAAa;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC1D,cAAc;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC3D,QAAQ;MAAE,OAAO;IAAI;IACrB,KAAK;MAAE,OAAO;IAAI;IAClB,aAAa;MAAE,OAAO;IAAK;IAC3B,UAAU;MAAE,OAAO;IAAK;IACxB,eAAe;MAAE,OAAO;IAAI;IAC5B,SAAS;MAAE,OAAO;IAAI;IACtB,eAAe;MAAE,OAAO,CAAA;IAAG;IAC3B,mBAAmB;MAAE,OAAO;IAAI;IAChC,QAAQ;MAAE,OAAO;IAAI;IACrB,OAAO;MAAE,OAAO;IAAO;IACvB,qBAAqB;MAAE,OAAO;IAAM;EACxC;EACA,YAAY;EACZ,WAAW;EAEX;;IAAyB;;;;;;;EAMzB;;IAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6H/B;AC7JA,IAAM,4CAAkB;EACpB,UAAU;IACN,cAAc;MAAE,OAAO;IAAK;IAC5B,cAAc;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC3D,QAAQ;MAAE,OAAO;IAAI;IACrB,OAAO;MAAE,OAAO;IAAO;IACvB,iBAAiB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IAC9D,uBAAuB;MAAE,OAAuB,IAAI,QAAA;IAAgB;IACpE,YAAY;MAAE,OAAO;IAAM;IAC3B,SAAS;MAAE,OAAO;IAAM;EAC5B;EACA,YAAY;EACZ,WAAW;EAEX;;IAAyB;;;;;;;EAMzB;;IAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsH/B;AE5IA,IAAM,kCAAY;AAElB,IAAM,uCAAiC,MAAM,WAAW,KAAK,KAAK,+BAAA,GAAY,CAAA,MAAK,EAAE,WAAW,CAAA,CAAA,GAAE;IAElG,2CAAe;ACHf,IAAM,iCAA2B,MAC/B,SAAS,SAAe,QAAQ,QAAQ,EAAA,CAAA,GAAG;AAItC,IAAM,4CACX,iCAAW,MACP,cAAc,kBAAA;EACZ,YAAY,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,UAAU,CAAC,GAAG;AAC1D,UAAM,OAAO,QAAQ;MAAE,GAAG;;IAAe,CAAA;AACzC,SAAK,+BAA+B;EACtC;EACA,IAAI,UAAU;AACZ,WAAO,KAAK;EACd;AACF,IACA,cAAc,kBAAA;EACZ,YAAY,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,UAAU,CAAC,GAAG;AAC1D,UAAM,OAAO,QAAQ,OAAA;AACrB,SAAK,+BAA+B;AACpC,UAAM,UAAU,KAAK;AACrB,SAAK,UAAU,CAAA;AACf,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,WAAK,QAAQ,CAAA,IAAK,QAAQ,MAAK;AAC/B,WAAK,QAAQ,CAAA,EAAG,wBAAwB;IAC1C;EACF;EACA,QAAQ,OAAO,QAAQ,QAAQ,GAAG;AAChC,QACE,KAAK,UAAU,SACf,KAAK,WAAW,UAChB,KAAK,UAAU,OACf;AACA,WAAK,QAAQ;AACb,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACrD,aAAK,QAAQ,CAAA,EAAG,MAAM,QAAQ;AAC9B,aAAK,QAAQ,CAAA,EAAG,MAAM,SAAS;AAC/B,aAAK,QAAQ,CAAA,EAAG,MAAM,QAAQ;MAChC;AACA,WAAK,QAAO;IACd;AACA,SAAK,SAAS,IAAI,GAAG,GAAG,OAAO,MAAA;AAC/B,SAAK,QAAQ,IAAI,GAAG,GAAG,OAAO,MAAA;EAChC;EACA,KAAK,QAAQ;AACX,SAAK,QAAO;AACZ,SAAK,QAAQ,OAAO;AACpB,SAAK,SAAS,OAAO;AACrB,SAAK,QAAQ,OAAO;AACpB,SAAK,QAAQ,KAAK,OAAO,OAAO;AAChC,SAAK,cAAc,OAAO;AAC1B,SAAK,SAAS,KAAK,OAAO,QAAQ;AAClC,SAAK,cAAc,OAAO;AAC1B,SAAK,gBAAgB,OAAO;AAC5B,QAAI,OAAO,iBAAiB,KAC1B,MAAK,eAAe,OAAO,aAAa,MAAK;AAC/C,SAAK,QAAQ,SAAS;AACtB,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,QAAQ,IAAI,IAAI,KAAK;AACvD,WAAK,QAAQ,CAAA,IAAK,OAAO,QAAQ,CAAA,EAAG,MAAK;AACzC,WAAK,QAAQ,CAAA,EAAG,wBAAwB;IAC1C;AACA,WAAO;EACT;AACF;AFjDN,SAAS,sCAAgB,YAAY,IAAI,MAAM;AAC3C,QAAM,YAAY,GAAG,kBAAkB,YAAY,GAAG,sBAAsB;AAC5E,MAAI,WAAW;AACX,UAAM,kBAAkB,GAAG,kBAAkB,YAAY,GAAG,YAAY;AACxE,UAAM,kBAAkB,kBAAkB;AAC1C,SAAK,WAAW,KAAK,aAAa,IAAI,kBAAkB,KAAK,qBAAqB,KAAK,YAAY,IAAI,KAAK,sBAAsB;EACtI;AAEI,eAAW,MAAM;AACb,4CAAgB,YAAY,IAAI,IAAA;IACpC,GAAG,CAAA;AAEX;AACA,IAAM,4CAAN,eAA2B,GAAA,MAAG;;;;;;;;;;;;;EAa1B,YAAY,OAAO,QAAQ,QAAQ,KAAK,SAAS,KAAK;AAClD,UAAK;AACL,SAAK,QAAQ;AACb,SAAK,SAAS;AAEd,SAAK,QAAQ;AAEb,SAAK,SAAS;AACd,SAAK,QAAQ;AAoBb,SAAK,eAAe;AACpB,SAAK,gBAAgB,IAAI,MAAM;MAC3B,WAAW;MACX,UAAU;MACV,SAAS;MACT,gBAAgB;MAChB,eAAe;MACf,iBAAiB;MACjB,WAAW;MACX,mBAAmB;MACnB,YAAY;MACZ,YAAY;MACZ,gBAAgB;MAChB,OAAO,IAAI,MAAY,GAAG,GAAG,CAAA;MAC7B,iBAAiB;MACjB,kBAAiB,GAAA,2CAAU;MAC3B,mBAAmB;MACnB,SAAS;MACT,sBAAsB;MACtB,eAAe;MACf,mBAAmB;MACnB,YAAY;IAChB,GAAG;MACC,KAAK,CAAC,QAAQ,UAAU,UAAU;AAC9B,cAAM,UAAU,OAAO,QAAA;AACvB,eAAO,QAAA,IAAY;AACnB,YAAI,MAAM,QACN;AAAA,cAAI,CAAC,MAAM,OAAO,OAAA,EACd,MAAK,WAAU;QACnB,WAEI,YAAY,MACZ,MAAK,WAAU;AAGvB,YAAI,aAAa,eAAe,YAAY,MACxC,MAAK,gBAAgB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AAE7F,YAAI,aAAa,oBAAoB,YAAY,MAC7C,MAAK,qBAAqB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AAElG,YAAI,aAAa,aAAa,YAAY,OAAO;AAC7C,eAAK,gBAAgB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AACzF,eAAK,wBAAuB;AAC5B,eAAK,0BAA0B,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AACnG,eAAK,QAAQ,KAAK,OAAO,KAAK,MAAM;QACxC;AACA,YAAI,aAAa,0BAA0B,YAAY,MACnD,MAAK,0BAA0B,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AAEvG,YAAI,aAAa,kBACb,MAAK,eAAe;AAExB,YAAI,aAAa,uBAAuB,YAAY,OAAO;AACvD,eAAK,yBAAyB;AAC9B,eAAK,4BAA2B;QACpC;AACA,eAAO;MACX;IACJ,CAAA;AAEA,SAAK,UAAU,CAAA;AAEf,SAAK,iBAAiB,CAAA;AACtB,SAAK,yBAAyB;AAC9B,SAAK,SAAS;AACd,SAAK,iBAAiB,IAAI,QAAA;AAC1B,SAAK,uBAAuB,IAAI,QAAA;AAChC,SAAK,0BAA0B,KAAK,cAAc,eAAe;AACjE,SAAK,+BAA8B;AACnC,SAAK,wBAAuB;AAC5B,SAAK,mBAAkB;AACvB,SAAK,4BAA2B;AAGhC,SAAK,WAAW,KAAI,GAAA,2CAAmB,IAAI,eAAqB;MAC5D,UAAU;QACN,UAAU;UACN,OAAO;QACX;MACJ;MACA,YAAY;MACZ,cAAc;;;;;;;MAOd,gBAAgB;;;;;;;IAOpB,CAAA,CAAA;AACA,SAAK,sBAAsB,IAAI,kBAAwB,KAAK,OAAO,KAAK,QAAQ;MAC5E,WAAW;MACX,WAAW;MACX,aAAa;MACb,QAAQ;IACZ,CAAA;AACA,SAAK,qBAAqB,IAAI,kBAAwB,KAAK,OAAO,KAAK,QAAQ;MAC3E,WAAW;MACX,WAAW;MACX,aAAa;MACb,QAAQ;IACZ,CAAA;AACA,SAAK,uBAAuB,IAAI,kBAAwB,KAAK,OAAO,KAAK,QAAQ;MAC7E,WAAW;MACX,WAAW;MACX,aAAa;IACjB,CAAA;AACA,SAAK,2BAA2B,IAAI,kBAAwB,KAAK,OAAO,KAAK,QAAQ;MACjF,WAAW;MACX,WAAW;MACX,aAAa;MACb,QAAQ;MACR,MAAM;MACN,eAAe;MACf,aAAa;MACb,OAAO;IACX,CAAA;AACA,SAAK,mBAAmB,KAAI,GAAA,2CAAmB,IAAI,eAAqB;MACpE,UAAU;QACN,OAAO;UAAE,OAAO;QAAE;QAClB,UAAU;UAAE,OAAO;QAAK;MAC5B;MACA,aAAa;MACb,SAAS;MACT,cAAc;;;;;;MAMd,gBAAgB;;;;;;;;;IASpB,CAAA,CAAA;AAIA,SAAK,YACD,IAAI,aACA,GAAA,2CACA,KACA,GAAA;AAER,SAAK,UAAU,aAAa;AAC5B,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,YAAY;AAC3B,SAAK,UAAU,YAAY;AAC3B,SAAK,UAAU,cAAc;AAC7B,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,KAAK,IAAI,QAAA;AACd,SAAK,KAAK,IAAI,MAAA;EAIlB;EACA,0BAA0B;AACtB,SAAK,WAAU;AAEf,QAAI,KAAK,cAAc,SAAS;AAC5B,WAAK,wBAAwB,KAAI,GAAA,2CAC7B,KAAK,QAAQ,GACb,KAAK,SAAS,GACd,CAAA;AAGJ,UAAI,YAAkB,IAClB,MAAK,sBAAsB,WAAW,KAAK,sBAAsB;AAGrE,WAAK,sBAAsB,SAAS,CAAA,EAAG,SAAS;AAChD,WAAK,sBAAsB,SAAS,CAAA,EAAG,OAAO;AAC9C,WAAK,sBAAsB,SAAS,CAAA,EAAG,YAAY;AACnD,WAAK,sBAAsB,SAAS,CAAA,EAAG,YAAY;AACnD,WAAK,sBAAsB,SAAS,CAAA,EAAG,cAAc;AACrD,WAAK,sBAAsB,SAAS,CAAA,EAAG,SAAS;AAChD,WAAK,sBAAsB,SAAS,CAAA,EAAG,OAAO;AAC9C,WAAK,sBAAsB,SAAS,CAAA,EAAG,YAAY;AACnD,WAAK,sBAAsB,SAAS,CAAA,EAAG,YAAY;AACnD,WAAK,sBAAsB,SAAS,CAAA,EAAG,cAAc;AAGrD,WAAK,sBAAsB,KAAI,GAAA,2CAAmB,IAAI,gBAAqB,GAAA,0CAAc,CAAA;IAC7F,OAAO;AACH,UAAI,KAAK,uBAAuB;AAC5B,aAAK,sBAAsB,QAAO;AAClC,aAAK,wBAAwB;MACjC;AACA,UAAI,KAAK,qBAAqB;AAC1B,aAAK,oBAAoB,QAAO;AAChC,aAAK,sBAAsB;MAC/B;IACJ;EACJ;EACA,qBAAqB;AACjB,QAAI,KAAK,wBAAwB;AAC7B,UAAI,iBAAiB;AACrB,WAAK,MAAM,SAAS,CAAC,QAAQ;AACzB,YAAI,IAAI,YAAY,IAAI,SAAS,YAC7B,kBAAiB;MAEzB,CAAA;AACA,UAAI,eACA,MAAK,cAAc,oBAAoB;IAE/C;EACJ;EACA,8BAA8B;AAC1B,QAAI,KAAK,cAAc,mBAAmB;AACtC,WAAK,kCAAkC,IAAI,kBAAwB,KAAK,OAAO,KAAK,QAAQ;QACxF,WAAW;QACX,WAAW;QACX,MAAM;QACN,QAAQ;MACZ,CAAA;AACA,WAAK,iCAAiC,IAAI,kBAAwB,KAAK,OAAO,KAAK,QAAQ;QACvF,WAAW;QACX,WAAW;QACX,MAAM;QACN,QAAQ;MACZ,CAAA;AACA,WAAK,+BAA+B,eAAe,IAAI,aAAmB,KAAK,OAAO,KAAK,QAAQ,eAAA;AACnG,WAAK,gBAAgB,KAAI,GAAA,2CAAmB,IAAI,eAAqB;QACjE,UAAU;UACN,cAAc;YAAE,OAAO,KAAK;UAAa;UACzC,oBAAoB;YAAE,OAAO,KAAK,cAAc,qBAAoB,GAAA,2CAAU;UAAQ;QAC1F;QACA;;UAAyB;;;;;;;QAMzB;;UAA2B;;;;;;;;;;;;;;;;;;;MAmB/B,CAAA,CAAA;IACJ,OAAO;AACH,UAAI,KAAK,iCAAiC;AACtC,aAAK,gCAAgC,QAAO;AAC5C,aAAK,kCAAkC;MAC3C;AACA,UAAI,KAAK,gCAAgC;AACrC,aAAK,+BAA+B,QAAO;AAC3C,aAAK,iCAAiC;MAC1C;AACA,UAAI,KAAK,eAAe;AACpB,aAAK,cAAc,QAAO;AAC1B,aAAK,gBAAgB;MACzB;IACJ;EACJ;EACA,mBAAmB,UAAU;AACzB,UAAM,gBAAgB,KAAK,MAAM;AACjC,UAAM,gBAAgB,SAAS,cAAc,IAAI,MAAA,CAAA;AACjD,UAAM,gBAAgB,SAAS,cAAa;AAC5C,UAAM,gBAAgB,oBAAI,IAAA;AAC1B,UAAM,oBAAoB,SAAS;AACnC,SAAK,MAAM,SAAS,CAAC,QAAQ;AACzB,oBAAc,IAAI,KAAK,IAAI,OAAO;IACtC,CAAA;AAGA,SAAK,MAAM,aAAa;AACxB,aAAS,iBAAiB;AAC1B,aAAS,cAAc,IAAI,MAAY,GAAG,GAAG,CAAA,GAAI,CAAA;AAEjD,SAAK,cAAc,SAAS,SAAS,aAAa,QAAQ,KAAK;AAC/D,SAAK,cAAc,SAAS,SAAS,mBAAmB,QAAQ,KAAK,cAAc,qBAAoB,GAAA,2CAAU;AAEjH,aAAS,gBAAgB,KAAK,+BAA+B;AAC7D,SAAK,MAAM,SAAS,CAAC,QAAQ;AACzB,UAAI,IAAI,SACJ,KAAI,UAAU,cAAc,IAAI,GAAA,MAAU,IAAI,SAAS,eAAe,CAAC,IAAI,SAAS,cAAc,CAAC,IAAI,SAAS,iBAAkB,CAAC,CAAC,IAAI,SAAS;IAEzJ,CAAA;AACA,aAAS,MAAM,MAAM,MAAM,IAAI;AAC/B,SAAK,cAAc,OAAO,QAAA;AAC1B,aAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAIvC,aAAS,gBAAgB,KAAK,8BAA8B;AAC5D,SAAK,MAAM,SAAS,CAAC,QAAQ;AACzB,UAAI,IAAI,SACJ,KAAI,UAAU,cAAc,IAAI,GAAA,KAAQ,IAAI,SAAS,eAAe,IAAI,SAAS,cAAc,CAAC,IAAI,SAAS;IAErH,CAAA;AACA,aAAS,MAAM,MAAM,MAAM,IAAI;AAC/B,SAAK,cAAc,OAAO,QAAA;AAC1B,aAAS,OAAO,KAAK,OAAO,KAAK,MAAM;AAGvC,SAAK,MAAM,SAAS,CAAC,QAAQ;AACzB,UAAI,UAAU,cAAc,IAAI,GAAA;IACpC,CAAA;AACA,aAAS,cAAc,eAAe,aAAA;AACtC,SAAK,MAAM,aAAa;AACxB,aAAS,iBAAiB;EAC9B;EACA,iCAAiC;AAC7B,SAAK,gBAAgB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AACzF,SAAK,qBAAqB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;EAClG;EACA,gBAAgB,mBAAkB,GAAA,2CAAU,SAAS,QAAQ,OAAO;AAChE,SAAK,WAAU;AACf,SAAK,UAAU,KAAK,0BAA0B,KAAK,cAAc,SAAS;AAC1E,UAAM,IAAI;MAAC,IAAG,GAAA;IAAa;AAC3B,MAAE,iBAAiB,EAAE,eAAe,QAAQ,MAAM,KAAK,cAAc,SAAS,EAAE,QAAQ,QAAQ,KAAK,cAAc,YAAY,IAAA;AAC/H,QAAI,qBAAoB,GAAA,2CAAU,IAC9B,GAAE,iBAAiB,uBAAuB,EAAE;aACrC,qBAAoB,GAAA,2CAAU,QACrC,GAAE,iBAAiB,2BAA2B,EAAE;AAEpD,QAAI,MACA,GAAE,iBAAiB,oBAAoB,EAAE;AAE7C,QAAI,KAAK,cAAc,QACnB,GAAE,iBAAiB,sBAAsB,EAAE;AAE/C,QAAI,KAAK,kBAAkB;AACvB,WAAK,iBAAiB,SAAS,QAAO;AACtC,WAAK,iBAAiB,WAAW,IAAI,eAAqB,CAAA;IAC9D,MACI,MAAK,mBAAmB,KAAI,GAAA,2CAAmB,IAAI,eAAqB,CAAA,CAAA;EAEhF;EACA,qBAAqB,mBAAkB,GAAA,2CAAU,SAAS,QAAQ,OAAO;AACrE,SAAK,WAAU;AACf,SAAK,iBAAiB,KAAK,uBAAuB,KAAK,cAAc,gBAAgB,EAAA;AACrF,UAAMG,KAAI;MAAC,IAAG,GAAA;IAAa;AAC3B,IAAAA,GAAE,iBAAiBA,GAAE,eAAe,QAAQ,MAAM,KAAK,cAAc,cAAc;AACnF,QAAI,qBAAoB,GAAA,2CAAU,IAC9B,CAAAA,GAAE,iBAAiB,uBAAuBA,GAAE;aACrC,qBAAoB,GAAA,2CAAU,QACrC,CAAAA,GAAE,iBAAiB,2BAA2BA,GAAE;AAEpD,QAAI,MACA,CAAAA,GAAE,iBAAiB,oBAAoBA,GAAE;AAE7C,QAAI,KAAK,iBAAiB;AACtB,WAAK,gBAAgB,SAAS,QAAO;AACrC,WAAK,gBAAgB,WAAW,IAAI,eAAqBA,EAAA;IAC7D,MACI,MAAK,kBAAkB,KAAI,GAAA,2CAAmB,IAAI,eAAqBA,EAAA,CAAA;EAE/E;EACA,0BAA0B,mBAAkB,GAAA,2CAAU,SAAS,QAAQ,OAAO;AACtE,SAAK,WAAU;AAEf,UAAM,IAAI;MAAC,IAAG,GAAA;IAAiB;AAC/B,QAAI,qBAAoB,GAAA,2CAAU,IAC9B,GAAE,iBAAiB,uBAAuB,EAAE;aACrC,qBAAoB,GAAA,2CAAU,QACrC,GAAE,iBAAiB,2BAA2B,EAAE;AAEpD,QAAI,MACA,GAAE,iBAAiB,oBAAoB,EAAE;AAE7C,QAAI,KAAK,cAAc,WAAW,KAAK,cAAc,qBACjD,GAAE,iBAAiB,sBAAsB,EAAE;AAE/C,QAAI,KAAK,sBAAsB;AAC3B,WAAK,qBAAqB,SAAS,QAAO;AAC1C,WAAK,qBAAqB,WAAW,IAAI,eAAqB,CAAA;IAClE,MACI,MAAK,uBAAuB,KAAI,GAAA,2CAAmB,IAAI,eAAqB,CAAA,CAAA;EAEpF;;;;;;EAMJ,0BAA0B,GAAG;AACrB,UAAM,SAAS,CAAA;AACf,aAASC,KAAI,GAAGA,KAAI,GAAGA,MAAK;AACxB,YAAM,QAAQ,WAAWA;AACzB,YAAM,IAAK,KAAK,KAAKA,KAAI,GAAA,IAAO,KAAK,KAAK,CAAA;AAC1C,YAAM,IAAI,IAAI,KAAK,IAAI,KAAA;AACvB,YAAM,IAAI,IAAI,KAAK,IAAI,KAAA;AAEvB,YAAMC,KAAI,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,EAAA;AACrC,aAAO,KAAK,IAAI,QAAc,GAAG,GAAGA,EAAA,CAAA;IAExC;AACA,WAAO;EACX;;;;;;;EAOJ,uBAAuB,YAAY,UAAU;AACzC,UAAM,YAAY,IAAI,KAAK,KAAK,WAAW;AAC3C,UAAM,gBAAgB,IAAM;AAC5B,UAAM,aAAa;AACnB,UAAM,UAAU,CAAA;AAChB,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,cAAQ,KAAK,IAAI,QAAc,KAAK,IAAI,KAAA,GAAQ,KAAK,IAAI,KAAA,CAAA,EAAQ,eAAe,KAAK,IAAI,QAAQ,IAAA,CAAA,CAAA;AACjG,gBAAU;AACV,eAAS;IACb;AACA,WAAO;EACX;EACA,QAAQ,OAAO,QAAQ;AACnB,SAAK,WAAU;AACf,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,UAAM,IAAI,KAAK,cAAc,UAAU,MAAM;AAC7C,SAAK,oBAAoB,QAAQ,QAC7B,GAAG,SACH,CAAA;AACJ,SAAK,mBAAmB,QAAQ,QAC5B,GAAG,SACH,CAAA;AACJ,SAAK,yBAAyB,QAAQ,QAAQ,GAAG,SAAS,CAAA;AAC1D,QAAI,KAAK,cAAc,QACnB,MAAK,sBAAsB,QAAQ,QAAQ,GAAG,SAAS,CAAA;AAE3D,QAAI,KAAK,cAAc,mBAAmB;AACtC,WAAK,gCAAgC,QAAQ,OAAO,MAAA;AACpD,WAAK,+BAA+B,QAAQ,OAAO,MAAA;IACvD;AACA,SAAK,qBAAqB,QAAQ,OAAO,MAAA;EAC7C;EACA,gBAAgB,cAAc;AAC1B,SAAK,eAAe;EACxB;EACA,aAAa;AACT,SAAK,aAAa;EACtB;EACA,OAAO,UAAU,aAAa,cAAc;AACpC,UAAM,YAAY,SAAS,GAAG;AAC9B,aAAS,GAAG,UAAU;AAOtB,QAAI,SAAS,aAAa,0BAA0B,KAAK,cAAc,qBAAoB,GAAA,2CAAU,OAAO,SAAS,aAAa,sBAAsB,KAAK,cAAc,qBAAoB,GAAA,2CAAU,SAAS;AAC9M,WAAK,cAAc,kBAAkB,SAAS,aAAa,0BAAyB,GAAA,2CAAU,MAAM,SAAS,aAAa,sBAAqB,GAAA,2CAAU,WAAU,GAAA,2CAAU;AAC7K,WAAK,gBAAgB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AACzF,WAAK,qBAAqB,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;AAC9F,WAAK,0BAA0B,KAAK,cAAc,iBAAiB,KAAK,OAAO,oBAAoB;IACvG;AACA,SAAK,mBAAkB;AACvB,QAAI,YAAY,QAAQ,SAAS,KAAK,qBAAqB,QAAQ,QAC/D,YAAY,QAAQ,WAAW,KAAK,qBAAqB,QAAQ,QACnE;AACE,WAAK,qBAAqB,QAAQ,OAAO,YAAY,QAAQ;AAC7D,WAAK,qBAAqB,QAAQ,SAAS,YAAY,QAAQ;AAC/D,WAAK,qBAAqB,QAAQ,cAAc;IACpD;AACA,SAAK,OAAO,kBAAiB;AAC7B,QAAI,KAAK,eAAe,OAAO,KAAK,OAAO,kBAAkB,KAAK,KAAK,qBAAqB,OAAO,KAAK,OAAO,gBAAgB,KAAK,KAAK,cAAc,cAAc,CAAC,KAAK,WACvK,MAAK;SACF;AACH,UAAI,KAAK,cAAc,YAAY;AAC/B,iBAAS,gBAAgB,KAAK,wBAAwB;AACtD,iBAAS,MAAM,MAAM,MAAM,IAAI;MACnC;AACA,WAAK,QAAQ;AACb,WAAK,aAAa;IACtB;AACA,SAAK,eAAe,KAAK,KAAK,OAAO,kBAAkB;AACvD,SAAK,qBAAqB,KAAK,KAAK,OAAO,gBAAgB;AAC3D,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW;AAChB,WAAK,SAAS,WAAU;AACxB,YAAM,GAAG,aAAa,iCAAA;AACtB,UAAI,QAAQ,MAAM;AACd,gBAAQ,MAAM,sEAAA;AACd,aAAK,YAAY;MACrB;IACJ;AACA,QAAI,KAAK,WAAW;AAChB,mBAAa,GAAG,YAAW;AAC3B,SAAG,WAAW,IAAI,kBAAkB,UAAA;IACxC;AACA,QAAI,KAAK,cAAc,kBACnB,MAAK,mBAAmB,QAAA;AAE5B,SAAK,GAAG,IAAI,KAAK,OAAO,KAAK,MAAM;AACnC,QAAI,aAAa,KAAK,cAAc;AACpC,QAAI,KAAK,cAAc,WAAW,KAAK,cAAc,kBACjD,eAAc;AAElB,QAAI,KAAK,QAAQ,OAAO,KAAK,cAAc,WAAW;AAClD,UAAI,KAAK,cAAc,SAAS;AAE5B,iBAAS,gBAAgB,KAAK,qBAAqB;AACnD,aAAK,oBAAoB,SAAS,SAAS,WAAW,QAAQ,KAAK;AACnE,aAAK,oBAAoB,SAAS,SAAS,WAAW,QAAQ,KAAK;AACnE,aAAK,oBAAoB,SAAS,SAAS,MAAA,EAAQ,QAAQ,KAAK,OAAO;AACvE,aAAK,oBAAoB,SAAS,SAAS,KAAA,EAAO,QAAQ,KAAK,OAAO;AACtE,aAAK,oBAAoB,SAAS,SAAS,qBAAA,EAAuB,QAAQ,KAAK,OAAO;AACtF,aAAK,oBAAoB,SAAS,SAAS,eAAA,EAAiB,QAAQ,KAAK,OAAO;AAChF,aAAK,oBAAoB,SAAS,SAAS,UAAA,EAAY,QAAQ,KAAK,cAAc;AAClF,aAAK,oBAAoB,SAAS,SAAS,OAAA,EAAS,QAAQ,KAAK,OAAO;AACxE,aAAK,oBAAoB,OAAO,QAAA;MACpC;AACA,WAAK,iBAAiB,SAAS,SAAS,cAAA,EAAgB,QAAQ,YAAY;AAC5E,WAAK,iBAAiB,SAAS,SAAS,YAAA,EAAc,QAAQ,KAAK,cAAc,UAAU,KAAK,sBAAsB,SAAS,CAAA,IAAK,KAAK;AACzI,WAAK,iBAAiB,SAAS,SAAS,aAAA,EAAe,QAAQ,KAAK,cAAc,UAAU,KAAK,sBAAsB,SAAS,CAAA,IAAK;AACrI,WAAK,iBAAiB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK,OAAO;AACvE,WAAK,iBAAiB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK,OAAO;AACvE,WAAK,iBAAiB,SAAS,SAAS,aAAA,EAAe,QAAQ,KAAK,OAAO,iBAAiB,MAAK,EAAG,SAAS,KAAK,OAAO,mBAAmB,MAAK,CAAA;AACjJ,WAAK,iBAAiB,SAAS,SAAS,qBAAA,EAAuB,QAAQ,KAAK,OAAO;AACnF,WAAK,iBAAiB,SAAS,SAAS,eAAA,EAAiB,QAAQ,KAAK,OAAO;AAC7E,WAAK,iBAAiB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK,OAAO,iBAAiB,IAAI,QAAA,CAAA;AAC9F,WAAK,iBAAiB,SAAS,SAAS,gBAAA,EAAkB,QAAQ,IAAI,QAAc,KAAK,cAAc,YAAY,KAAK,cAAc,cAAc;AACpJ,WAAK,iBAAiB,SAAS,SAAS,YAAA,EAAc,QAAS,KAAK,cAAc,UAAU,KAAK,GAAG,MAAK,EAAG,eAAe,GAAA,EAAO,MAAK,IAAK,KAAK;AACjJ,WAAK,iBAAiB,SAAS,SAAS,MAAA,EAAQ,QAAQ,YAAY,IAAG,IAAK;AAC5E,WAAK,iBAAiB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK;AAChE,WAAK,iBAAiB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK;AAClE,WAAK,iBAAiB,SAAS,SAAS,QAAA,EAAU,QAAQ;AAC1D,WAAK,iBAAiB,SAAS,SAAS,iBAAA,EAAmB,QAAQ,KAAK,cAAc;AACtF,WAAK,iBAAiB,SAAS,SAAS,MAAA,EAAQ,QAAQ,KAAK,OAAO;AACpE,WAAK,iBAAiB,SAAS,SAAS,KAAA,EAAO,QAAQ,KAAK,OAAO;AACnE,WAAK,iBAAiB,SAAS,SAAS,OAAA,EAAS,QAAQ,KAAK,OAAO;AACrE,WAAK,iBAAiB,SAAS,SAAS,mBAAA,EAAqB,QAAQ,KAAK,cAAc;AACxF,WAAK,iBAAiB,SAAS,SAAS,OAAA,EAAS,QAAQ,KAAK;AAE9D,eAAS,gBAAgB,KAAK,mBAAmB;AACjD,WAAK,iBAAiB,OAAO,QAAA;AAG7B,eAAS,IAAI,GAAG,IAAI,KAAK,cAAc,mBAAmB,KAAK;AAC3D,SAAC,KAAK,qBAAqB,KAAK,kBAAkB,IAAI;UAAC,KAAK;UAAoB,KAAK;;AACrF,aAAK,gBAAgB,SAAS,SAAS,UAAA,EAAY,QAAQ,KAAK,mBAAmB;AACnF,aAAK,gBAAgB,SAAS,SAAS,YAAA,EAAc,QAAQ,KAAK,cAAc,UAAU,KAAK,sBAAsB,SAAS,CAAA,IAAK,KAAK;AACxI,aAAK,gBAAgB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK,OAAO;AACtE,aAAK,gBAAgB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK,OAAO;AACtE,aAAK,gBAAgB,SAAS,SAAS,qBAAA,EAAuB,QAAQ,KAAK,OAAO;AAClF,aAAK,gBAAgB,SAAS,SAAS,eAAA,EAAiB,QAAQ,KAAK,OAAO;AAC5E,aAAK,gBAAgB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK,OAAO,iBAAiB,IAAI,QAAA,CAAA;AAC7F,aAAK,gBAAgB,SAAS,SAAS,YAAA,EAAc,QAAS,KAAK,cAAc,UAAU,KAAK,GAAG,MAAK,EAAG,eAAe,GAAA,EAAO,MAAK,IAAK,KAAK;AAChJ,aAAK,gBAAgB,SAAS,SAAS,MAAA,EAAQ,QAAQ,YAAY,IAAG,IAAK;AAC3E,aAAK,gBAAgB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK;AACjE,aAAK,gBAAgB,SAAS,SAAS,QAAA,EAAU,QAAQ,KAAK,cAAc,iBACxE,KAAK,cAAc,UAAU,MAAQ;AAEzC,aAAK,gBAAgB,SAAS,SAAS,aAAA,EAAe,QAAQ;AAC9D,aAAK,gBAAgB,SAAS,SAAS,iBAAA,EAAmB,QAAQ,KAAK,cAAc;AACrF,aAAK,gBAAgB,SAAS,SAAS,OAAA,EAAS,QAAQ;AACxD,aAAK,gBAAgB,SAAS,SAAS,aAAA,EAAe,QAAQ,KAAK;AACnE,aAAK,gBAAgB,SAAS,SAAS,MAAA,EAAQ,QAAQ,KAAK,OAAO;AACnE,aAAK,gBAAgB,SAAS,SAAS,KAAA,EAAO,QAAQ,KAAK,OAAO;AAClE,aAAK,gBAAgB,SAAS,SAAS,mBAAA,EAAqB,QAAQ,KAAK,cAAc;AACvF,iBAAS,gBAAgB,KAAK,mBAAmB;AACjD,aAAK,gBAAgB,OAAO,QAAA;MAEhC;AACA,eAAS,gBAAgB,KAAK,wBAAwB;AACtD,YAAM,eAAe,SAAS;AAC9B,eAAS,YAAY;AACrB,WAAK,iBAAiB,SAAS,SAAS,UAAA,EAAY,QAAQ,KAAK,oBAAoB;AACrF,WAAK,iBAAiB,SAAS,SAAS,OAAA,EAAS,QAAQ,KAAK;AAC9D,WAAK,iBAAiB,OAAO,QAAA;AAC7B,eAAS,YAAY;IACzB;AAIA,QAAI,KAAK,cAAc,mBAAmB;AACtC,WAAK,qBAAqB,SAAS,SAAS,qBAAA,EAAuB,QAAQ,KAAK,gCAAgC;AAChH,WAAK,qBAAqB,SAAS,SAAS,oBAAA,EAAsB,QAAQ,KAAK,+BAA+B;AAC9G,WAAK,qBAAqB,SAAS,SAAS,yBAAA,EAA2B,QAAQ,KAAK,+BAA+B;AACnH,WAAK,qBAAqB,SAAS,SAAS,mBAAA,EAAqB,QAAQ;IAC7E;AACA,SAAK,qBAAqB,SAAS,SAAS,cAAA,EAAgB,QAAQ,YAAY;AAChF,SAAK,qBAAqB,SAAS,SAAS,YAAA,EAAc,QAAQ,KAAK;AACvE,SAAK,qBAAqB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK,cAAc;AAClF,SAAK,qBAAqB,SAAS,SAAS,MAAA,EAAQ,QAAQ,KAAK,OAAO;AACxE,SAAK,qBAAqB,SAAS,SAAS,KAAA,EAAO,QAAQ,KAAK,OAAO;AACvE,SAAK,qBAAqB,SAAS,SAAS,qBAAA,EAAuB,QAAQ,KAAK,OAAO;AACvF,SAAK,qBAAqB,SAAS,SAAS,eAAA,EAAiB,QAAQ,KAAK,OAAO;AACjF,SAAK,qBAAqB,SAAS,SAAS,OAAA,EAAS,QAAQ,KAAK,OAAO;AACzE,SAAK,qBAAqB,SAAS,SAAS,kBAAA,EAAoB,QAAQ,KAAK,cAAc,UAAU,KAAK,sBAAsB,SAAS,CAAA,IAAK,KAAK;AACnJ,SAAK,qBAAqB,SAAS,SAAS,YAAA,EAAc,QAAQ,KAAK;AACvE,SAAK,qBAAqB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK;AACtE,SAAK,qBAAqB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK,cAAc;AACpF,SAAK,qBAAqB,SAAS,SAAS,YAAA,EAAc,QAAQ,KAAK,cAAc;AACrF,SAAK,qBAAqB,SAAS,SAAS,mBAAA,EAAqB,QAAQ,KAAK,cAAc;AAC5F,SAAK,qBAAqB,SAAS,SAAS,QAAA,EAAU,QAAQ;AAC9D,SAAK,qBAAqB,SAAS,SAAS,iBAAA,EAAmB,QAAQ,KAAK,cAAc;AAC1F,SAAK,qBAAqB,SAAS,SAAS,iBAAA,EAAmB,QAAQ,KAAK,eACxE,KAAK,iBACL,KAAK,cAAc;AACvB,SAAK,qBAAqB,SAAS,SAAS,UAAA,EAAY,QAAQ,KAAK,yBAAyB;AAC9F,SAAK,qBAAqB,SAAS,SAAS,OAAA,EAAS,QACjD,KAAK,GAAG,KACJ,KAAK,cAAc,KAAK,EAC1B,oBAAmB;AACzB,SAAK,qBAAqB,SAAS,SAAS,eAAA,EAAiB,QAAQ,KAAK,cAAc;AACxF,SAAK,qBAAqB,SAAS,SAAS,WAAA,EAAa,QAAQ,KAAK,OAAO,iBAAiB,IAAI,QAAA,CAAA;AAClG,SAAK,qBAAqB,SAAS,SAAS,KAAA,EAAO,QAAQ,CAAC,CAAC,KAAK,MAAM;AACxE,QAAI,KAAK,MAAM,KAAK;AAChB,UACI,KAAK,MAAM,IAAI,OACjB;AACE,aAAK,qBAAqB,SAAS,SAAS,QAAA,EAAU,QAAQ;AAC9D,aAAK,qBAAqB,SAAS,SAAS,SAAA,EAAW,QAAQ,KAAK,MAAM,IAAI;AAC9E,aAAK,qBAAqB,SAAS,SAAS,QAAA,EAAU,QAAQ,KAAK,MAAM,IAAI;MACjF,WACI,KAAK,MAAM,IAAI,WACjB;AACE,aAAK,qBAAqB,SAAS,SAAS,QAAA,EAAU,QAAQ;AAC9D,aAAK,qBAAqB,SAAS,SAAS,YAAA,EAAc,QAAQ,KAAK,MAAM,IAAI;MACrF,MACI,SAAQ,MAAM,wBAAwB,KAAK,MAAM,IAAI,YAAY,IAAI,eAAe;IAI5F;AACA,aAAS;;;MAGL,KAAK;IAAoB;AAE7B,SAAK,qBAAqB,OAAO,QAAA;AACjC,aAAS,gBACL,KAAK,iBAAiB,OACtB,YAAY;AAEhB,SAAK,SAAS,SAAS,SAAS,UAAA,EAAY,QAAQ,KAAK,qBAAqB;AAC9E,SAAK,SAAS,OAAO,QAAA;AACrB,QAAI,KAAK,WAAW;AAChB,SAAG,SAAS,IAAI,gBAAgB;AAChC,4CAAgB,YAAY,IAAI,IAAI;IACxC;AAEA,aAAS,GAAG,UAAU;EAC1B;;;;EAIJ,kBAAkB;AACV,SAAK,YAAY;EACrB;;;;EAIJ,mBAAmB;AACX,SAAK,YAAY;EACrB;;;;;EAKJ,eAAe,MAAM;AACb,SAAK,cAAc,aAAa;MAAC;MAAY;MAAM;MAAS;MAAS;MAAY,QAAQ,IAAA;EAC7F;;;;;EAKJ,eAAe,MAAM;AACjB,QAAI,SAAS,eAAe;AACxB,WAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,iBAAiB;AACpC,WAAK,cAAc,gBAAgB;IACvC,WAAW,SAAS,OAAO;AACvB,WAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,iBAAiB;AACpC,WAAK,cAAc,gBAAgB;IACvC,WAAW,SAAS,UAAU;AAC1B,WAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,iBAAiB;AACpC,WAAK,cAAc,gBAAgB;IACvC,WAAW,SAAS,QAAQ;AACxB,WAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,iBAAiB;AACpC,WAAK,cAAc,gBAAgB;IACvC,WAAW,SAAS,SAAS;AACzB,WAAK,cAAc,YAAY;AAC/B,WAAK,cAAc,iBAAiB;AACpC,WAAK,cAAc,gBAAgB;IACvC;EAEJ;AACJ;ANxvBO,IAAM,4CAAY;EACrB,SAAS;EACT,KAAK;EACL,SAAS;AACb;;;AStBa,IAAAC,QAAAA,aAAAA,eAAAA,IAEN;AAAA,SAAmB,GAAE,EAAA,UAAU,GAAA,SAAU,IAAA,KACxC,GAAA;AAAA,QAAWC,CAAM,GAAIC,CAAAA,QAA6B,aAAAC,UAAA,CAClDC,CAAAA,GAAQC,QAAQ,aAAAC,SAAA,OAAS,EAAA,UAAU,GAAA,QAAQ,GAAA,SAAQ,EAAI,IAAWL,CAAAA,GAAe,GAAC,CAAA,CAAA;AAAA,aAAA,mBAAAM,KAAA,EAAA,UAC/D,EAASH,OAAAA,GAAAA,UAG7B,EAAA,CAAA;AAAA;AAAA,SAAkB,GAAA,EAAA,SAAiB,IAAA,OAAA,UAAaI,GAAoB,GACnE,EAAA,GAAA;AAAAC,QAA4B,QAAK,aAAAC,QACjCC,IAAiBX,GAAgB,QACvC,aAAAY,YAAA,CAAA;AAAA,aAAgB,aAAAC,WACd,MAAIF;AAAOG,QACT,KAAA,GAAIC;AAAU,UACd,IAAA;AAAMC,YAKN,IAJMP,CAAAA;AAAA,UAAA,EAAA,QAAQ,SACV,OAAA;AAAA,UAAA,SAAS,UAAkB,EAAM,KAC3B,CAAA,GAAA,EAAA,SAAS,QAAe,CAAA,MAAcM,OAC/C,IACGA;MACEJ,CAAAA,GAAAA,EAAAA,QAAA,EAAA,OAAmB,OAAU,CAAA,GAAU,GAAC,GACrC,CAAA,CAAM,GACXA,MAAI;AAAA,UAAA,OAAwB,OAAA,EAAA,OAA8B,OAAA,CAAA,EAAA,SAI/D,CAAA,CAAA,CAACG;MAAAA;IAASG;EAAAA,GAAa,CAAC,GAAA,GAAA,CAAA,CAAA,OAAA,mBAAAV,KAExB,SAAWE,EAAQS,KAAGV,GAAAA,GAAAA,GACpBW,UCjBM,EAAAC,CAAAA;AAAAA;AAAAA,IAAAA,QAyBSC,aAAAA,eAAAA,IACnBC;AA1BUF,IA0BVE,KAAAA,QAAAA,EAAAA,cAAyBC,IAAAA,OAAAA;AA1BfH,IA0BeG,SAIxB,aAAAC,UAGI,aAAAC,YAAA,CAAA,EAAQC,UACDC,GACP,QAAA,GAAA,OAAAC,GACA,iBACA,GAAA,SAAA,IAAAC,MAAiB,gBACL,IACZ,GAAA,WAAAC,IACA,MAAA,aAAA,GAAAC,kBAEA,GAAA,eAAgBC,IAChB,eAAA,IAAAC,GAAkBC,iBAIZ,IAAAC,cAAI,GAAA,MAAOC;AAAAA,QAAc,EAAA,IAAQC,GAAe,OAAAC,GAASC,QACnDZ,GAAUS,MACTV,EAAWW,IAEpB,SAACG,GAAUC,IAA4B,KAAIpC,GAAQ,IAAA,KAEjDqC,GAAAA,CAAAA,GAAAA,GAAiB,CAAA,QAAA,aAAApC,SAAIqC,MAAuB;AAChD,UAAA,IAAA,IAAAb,eACA,GAAA,EAAA,aACA,GAAA,eACAE,IAAA,eAAAC,GACD,iBAG0BW,EAAAA,CAAAA;AAAWC,MAAa,QAG/CC,IAAmB,WACnBL,GAAa,CAAA,CAAA;AACjB,QAAA,IAAA,MACeM,IAAA;AAAIC,WACjBP,MAAW,IAAA,IAAU,WACrBC,GAAe,CAAA,GAAA,EAAA,UACXd,OAAoB,EAAA,QACH,CAAA,GAAA,MAA0B,WAAE,IAAA,IAAca,sBAAW,EAAA,cAAS,EAAA,SAAiB,iBAElGC,EAAe,CAAA,GAAA,EAAA,UAIZ,OAAiBD,EAAYK,QAGpCX,CACAL,KAEAmB,CAAAA,GACAhB,GACAY,CACAd;EAAAA,GACA,CACD,GAESmB,GAAA,GAAAlB,IAAMQ,GAAAA,GAAU,GAAA,GAAA,CAAA,CAAA;AAAQF,mBAAAA,WAAK,MAAA,uBAAY,QAAS,EAACE,OAC7DW,EACGC,SACC,CAAA,GAAItC,CAAS,CACX,GAAA,SAAA,CAAA,GAAMuC,MAAsB;AAAA,QAAA,GAAA;AAAA,YACzB,IAAA,EAAA;AACmBC,QAAAA,YAAc,GAAAtB,MAAA,CAAa,KACjDQ,EAAS,aACN,GAAA,EAAA,OAEP,CACA1B,GAAUe,EAAiB,YAGvBpB;IAAQ8C;EAAAA,GAAc,IAAA,IAAK,CACjCC;AAAgB,QACd,QAAA,aAAA9C,QAAA,IAAuB;AAGjB+C,mBAAAA,iBAAiBhD,MAAM;;AAAA,UAA+C,IAAA,CAAA,GAE5E,IAAA,EAAIgD,QACF;AAAiBA,QAAc,KAAA,GAAA;AAAA,YAE/B,IAAA,EAASC;AAAoB,eAAQA,IAC7BC,GAAAA,IAAAA,EAAAA,QAAoB,KAAA;AAAA,cAEtBA,IAAAA,EAAAA,CAAAA,EAAAA;AAAyB,YACrBC,aAEF,QAACC;AAAAA,gBACH,IAAA,CAAIC,CAAAA;AAAgB,cAAA,CACZA,GAAAA,CAAAA,GAAAA;AAAAA,gBAAgBJ,IAAK;AAAG,oBAAA,KAAA,OAAA,IAAA,CAAA,MAAA,mBAAmBK,mBAEpCD,UACbJ,CAAAA,GAIJ,CAAA,IAAA,GAAMM,KAAO,CAAIC,GAAAA;UAAmB;AAAA,gBACpCC,IAAO,IAAKF,WAAI,GAAA,GACPL,CAAAA;AAAAA,YAAAA,KAAAA,CAAAA;QAAAA,MACTO,cAIJ,QAAA,EAAWF,KAAQE,CAAAA;MAAQ1B;AAAAA,iBAAkBwB,KAEzCvB,EAAYA,wBAAW,QAAU;AACjCK,YAAkBA,EAAiB,UAAU,OAGnD,MAAA,EAAO,UACL;IAAA;AAAA,WAAWkB,MAAgBxB;AAAAA,iBAAU,KAAe,EAChDC,wBAAYA,WAAqB;AACjCK,YAAkBA,EAAiB,UAAU,QACnD,MACEN,EAAUvB,UAA+C;IAGnD;EAAA,GAAA,CAAM,GACd,GAAA,GAAA,GAAMkD,CAAqBhC,CAAAA,OAAG,aAAAtB,WAAA,MAAA;AAAA,UAC9BsB,IAAAA,EAAAA;AAAG,WACI,EAAA,cACF,eAAA,MACL;AACC,QAAG,cAGQ9B;IACZ;EAAA,GAAA,CAAA,CAAO,CAAE;AAAA,QAAA,QAAAmC,aAAAA,SAAU,OAAA,EAAA,UAAY,GAAA,YAAA,GAAAM,kBAAkBlB,GAAiB,iBAAQiB,GAAAA,QACnDC,GAAkBlB,OAA8B,EAIzEwC,IAAAA,CAAAA,GAAAA,GAAAA,GAAoBC,GAAK,GAAA,CAAA,CAAM7B;AAAU,aAGtC,aAAA8B,qBAAA,GAAA,MAAAC,GAAAC,CAAApD,CAAAA,CAAAA,OAAA,mBAAAb,KAAsCkE,EAAAA,UACrC,EAAA,OAACC,GAAAA,cAAWjE,mBAAAA,KAAQQ,SAK9B,ECjMa0D,KAAkBN,GAC7B,UAAe,EAAA,CAAA,EAAA,CAAA;AAAA,CAAA,CAAA;ADkBJjD,IClBI,IAAYiD,OAAO,OAAQ,KAAA,YAAmBA,KAAI,QAAUA,aAW7E,IAAA,EAAA,UAAMO;AAAAA,IAAAA,KAAAA;AAAAA,IAAAA,KAEkDtD,oBACtB;AAH5BsD,IAG4B,IAAA,CAAA,GAAA,MAAkBC,SAAAA,EAAAA,eAAyBC,IAAAA,uBAAUD,eAAmB,SACtFD,IAAW,uBAAA,SACtBG,GACGC,EAAAA,GAAAA;AAAAA,MAAM,IAAA,GAAA,IAAA,CAAA;AAAA,MAAA,CAAA,GAAA;AAAA,UAAA,IAAA,+BACY,EACbJ,IAAA,IAAItD,IAAyB;AAGpC2D,WAAAA,EAAAA,CAAAA,CAAAA,GAAS1C,EAAUkC,CAAAA,GAAUA,GAAM,IAAA,GAAM,IACzCS,CAAOC;EAAAA;AAAAA,QAAM,IAAA,SACjB,OAAM,EAAC,MAAc,GAAA,IAAA,aAAAC,QAAa,QAAU,MAAQ,CAAC,IAAE,uBAAa,SAAY,CAAA,GAE/E,GAAA,EAAK,QAAA,CAAA,EAAA,GAAe,GAAC,GAItB,EAAA,CAAA,CAAA,GAAA,CAAA,KAAA,UACE,CAAA,CAAA,CAAA;AAAA,aACA,mBAAA7E,KAAA,GAAA,EAAA,QACA,GAAA,2BACC,GAAA,2BAODH,GAAAA,GAAQI,GAAMwE,MACbG,EAAAA,CAAAA;AAAAA;AA/BHP,IA+BGO,IAAAA,CAAM,GAAA,MAAA;AAAA,QACP,IAAA,EAAA,CAAA;AAAO/E,SAAU,aAAAgF,QAAA,QAA2B,MAAA,OAAoB,KAC7C,WAAU,IAAQ,QAC7B,GAAIC,CAAM,IAAA,IAAA,IACf,QCnC4C,GAAA,CAAA,IAAA,IAAAC,WACrD,CACE,CAAA,CAAA;AAAA;ADFEV,ICEF,SACA,aAAAnD,YAAA,SAAA,EAAA,eACA,GAAA,oBACA,GAAA8D,iBAEA,GAAA,eACA,GAAA,YACA,GAAA,aACA,GAAA,YAAAC,GACA,iBACA,GAAA,aAEA,GAAA,aACAxD,IAAA,OAAAyD,GACA,QAEFpB,GAEA,QAAQ,GAAA,cACFqB,GAAYC,GAAU,EAAA,GAAA,GACtBrE;AAASjB,QAAQ,EAAM,QACrBiB,EAAAA,QAAS,aAAAV,YAAA,CAAIgF,GAAAA,IAAmBX,KACpC,MAAA,QAAA,aAAA3E,SAAA,MAAAuF;AAAAA,UACA,IAAA,IAAA,mBAAA,GAAA,EAAA,eACA,GAAA,oBACA,GAAAN,iBAEA,GAAA,eACA,GAAA,YACA,GAAA,aACA,GAAA,YAAAC,GACA,iBACA,GAAA,aAED,GAEGE,aAAkB1D,IAAS,OAAI8D,GAE/BL,QAAqB,EAAA,CAAA;AAAA,UAAA,EAAA,SAA6B,IAAA,YAAsB,KAAA,EAAA,gBAE3C,EAAA,SACjCM,EAAAA,OAAS;AAAA,UAAA,IAAeC,EAAAA;AAAa,WAAA,EAAA,eAIrCH,aAEAI,wBAWA;EACD,GAED/C,CAAAA,GAAAA,GAAAA,GAAAA,GAAU,GAAA,GACD,GAAA,GAAM,GACX5B,GAAOU,IAAA,GAAA,GAAA,GAAQ,CAAA,CAEhB;AAAO,aAAC,aAAAnB,WAAA,MAAA,MAAA;AAAA,MAAA,QAEH;EAAA,GAAW,CAAA,CAAGL,CAAAA,OAAyBc,mBAAAA,KAAAA,aClDpC4E,EAA4B,GAAA,GAAA,KAAA,GAAAZ,QAEnC,GAAA,QAAS,EAAA,CAAA;AAAW,CAAA;AFrBpBV,IEqBoB,SAAoC,aAAAnD,YAAA,CAAA,EAAA,QAAW,IAAA,QAAgB,OAAA,IAAA,OAAA0E,OAAmB,IAAG3F,QAExG,QACG4F,IAAS7C,OAA2B,YACH,IAAI,MACzBA,GAAmB,EAAA,GAAI,MAE3BhB;AAAAA,QAAY,QAAA,aAAA7B,QAAAmC,IAAYA,GAAK,QACrCwD,aAAAA,QAAU9D,IAAY,GAAAP,SAAA,aAAAtB,QAAA,IAAA2F,GAAcA,IAAO,SACzC,CAAA,EAAA,OAAA,EAAA7D,MAAU,CAAA,GAAA,IAAAyC,SAAO,CAAIqB,EAAAA,SAGFpG,EAAAA,MAAS,CAAM,GAAA,EAAA,UACvBA,GAAS,QAAM,EAAIqG,QAAU,aAAA3F,YAChDsC,CAAAA,GAAU,CAAA,CAAA,QACRV,aAAAA,UAAS,MAAA,IAAQgE,kBAAgB,GACjChE,CAAS,CAAA,QAAA,aAAArC,UAAA,MAAgB,IAClB,UAAM;AACXqC,mBAAAA,WAAS,OAAA,EAAA,QACA,CAAA,GAAA,EAAA,QAAmB,CAAA,GAE7B,MAAWgE;AAAkBC,MAAS,WAGhC,CAAM,GACXD,EAAiB,WACR,CAAA;EAAA,IAAA,CAAA,GAAQ,GAAA,CAElB,CAACA,OAAkBC,aAAAA,WAAS,MAEzB,MAACC;AAAQ,MAAIxG,QAAe,GAAImF,EAAM,QAAQ;EAAA,GAAG,CAAG,GAAE,CAAA,CAEtD;AAAI,QAAa,CAAA,CAAA,QAAM,aAAAlF,UAAA,MAAU,IAAA,QAAgB,GACjDwG,GAASC,CAAAA,CACb,GAAA,CAAA,CAAA,QAAOC,aAAAA,UAAWC,MAAAA,IACRD,QAERE,GAAI,GAAI,CAAA,CAAA,GAAA,QAAMP,aAAAA,aAAiB,OAAA,GAAUO,OACrC,EAAIA,IAAI,GAAI,EAAM,IACV,GAAIA,EAAI,IAAI,MACXA,EAAI,UAAgB,CAAA,GAAI,EAAA,IAEtCA,EAAKP,IAAkBvB,IAAM,GAG1B+B,IAASJ,EAAAA,IACb,OAAOK,EAAeC,UAEhBvB,CAAAA,IACOe,QAAA,CAAA,GAAI,GAAIf,CAAmC,CAAA,GAAA,QAAA,aAAAwB,aAE9C,OAAE,GAAAN,IAAG,SAAMO;;AAAcf,QAAY,EAAG,GAAG,IAAG,GACxC,CAAA;SAAMM;AAAOE,YACrBQ,EAAAA,GAAKX,GAAS,GAAA,EAAA,IAAKW,IAAG,IAIxBH,EAAAA,GAAgBd,GAAO,GAAA,EAAA,GAAA,IAAA,MAAS,EAAA,GAC9BD,CAAAA;AAAa,WAAKc,EAAQ,KACrB,CAAA;IAAA;AAAA,WAAMb,OAAO,YAAPA,mBAAe,YAA8Ba,IAAK,KAExD,IAAA,IAAA,OAAA,MAAQ,EAAO,QAG5B,QACmBG,GAAaT,GAAQR,CAAAA,IAAmB,EAAA,QAGpD,OAAoB,KAEzBa,CAAOC;EAAK,GAEVK,CAAY,GAAA,GAAA,GAAA,GAAA,GACFA,CAAA,CAAA;AAAA,WAAA,OAAQ,GAAA,MAAS;;AAAA,SAAA,EAAa,CAAA,GAExCC,EAAU,WAAWnB,EAAO,QAAS,SACvCmB,KAAU,CAAA,GAAAvF,GAAQ,aAAS,OAAA,YAAA,mBAAY,WAAQA,GACjD,QAIF,SACE,KACE,EAAA,QACA,MAAA0E;EACA,CAAA;AAAA,QAAA,QAEF,aAAApG,SAACoG,OAEH,EAAA,QAAAtC,GAAoBoD,UAAkB7G,GAAI,QAIrC,EAAA,IAAA,CAAA8G,GAAAA,CAAAA,CAAAC;AAAA,aAAA,aAAApD,qBACGqD,GAAAC,MAEI,GAAA,CAAA,CAAA,CAAA,OAAA,mBAAAC,MAAA,mBAAAC,UAAC,EAAA,UAAA,CAAA,IAAA,iBAAUR,mBAAAA,MAAAA,mBAAAA,UAAAA,EACT,UAAA,KAAA,mBAAAO,MAAA,QAAC,EAAA,KAAe3G,GAAOyG,UACvB,KAAAjD,mBAAAA,KAAAA,kBAAAA,EAAAA,MAAyB,CAAA,GAAA,IAAA,EAAA,EAAA,CAAA,OAAmB,mBAAAnE,KAAA,qBAA2B,EAAA,OAExE,WAAA,SAAA,GAAA,aAAUgH,MACT,YAAAhD,MAAA,CAAA,CAAA,EAAA,CAAA,OAAC,mBAAAsD,MAAA,QAA8B,EAAA,KAAG7F,IAAM,UACxC,KAAA0C,mBAAAA,KAAAA,kBAAyB,EAAA,MAAA,CAAA,IAAA,GAAA,IAAA,EAAA,EAAmB,CAAA,OAAgB,mBAAAnE,KAAA,qBAEhE,EAAA,OAGF,WAAA,SAEHwH,KAAAA,aAA8BvH,MAAAA,YCxIjCwH,MAAkB,CACtB,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,IAAA,UAA2B,mBAAAzH,KAAA,IAAA,EAAA,KAAA,GAAA,GAAA,GAAA,QAAA,EAAA,CAAA,CAAA,EAAA,CAAA;AAAA,CAAA;AHKvBqE,IGLuB,KAAA,EAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiY7B;AA2CO,IAAMqD,KAAN,cAA8BlE,OAAO;EAC1C,YAAY,EACV,eAAA8B,GACA,SAAA/E,GACA,WAAAoH,GACA,cAAAC,GACA,WAAAC,GACA,YAAAC,GACA,WAAAC,GACA,YAAAC,GACA,YAAAC,IACA,UAAAC,GACA,YAAAC,GACA,WAAAC,GACA,iBAAAC,GACA,WAAAC,GACA,iBAAAC,GACA,kBAAAC,GACA,YAAAC,GACA,SAAAlE,GACA,WAAAmE,EAAA,GACyB;AACnB,UAAA,mBAAmBjB,GAAgB,gBAAgB,EACvD,eAAAnC,GACA,UAAA,oBAAA,IACG,CAAA,CAAA,WAAW,IAAU,QAAgB,CACtC,CAAC,GAAA,CAAA,aAAa,IAAU,QAAkB,CAC1C,CAAC,GAAA,CAAA,gBAAgB,IAAU,QAAqB,CAChD,CAAC,GAAA,CAAA,QAAQ,IAAU,QAAU,CAC7B,CAAC,GAAA,CAAA,aAAa,IAAU,QAAkB,CAC1C,CAAC,GAAA,CAAA,cAAc,IAAU,QAAmB,CAC5C,CAAC,GAAA,CAAA,aAAa,IAAU,QAAkB,CAC1C,CAAC,GAAA,CAAA,cAAc,IAAU,QAAmB,CAC5C,CAAC,GAAA,CAAA,cAAc,IAAU,QAAmB7D,EAC5C,CAAC,GAAA,CAAA,YAAY,IAAU,QAAiB,CACxC,CAAC,GAAA,CAAA,cAAc,IAAU,QAAmB,CAC5C,CAAC,GAAA,CAAA,aAAa,IAAU,QAAkB,CAC1C,CAAC,GAAA,CAAA,mBAAmB,IAAU,QAAwB,CACtD,CAAC,GAAA,CAAA,aAAa,IAAU,QAAkB,CAC1C,CAAC,GAAA,CAAA,mBAAmB,IAAU,QAAwB,CACtD,CAAC,GAAA,CAAA,oBAAoB,IAAU,QAAyB,CACxD,CAAC,GAAA,CAAA,cAAc,IAAU,QAAmB,CAC5C,CAAC,GAAA,CAAA,aAAa,IAAU,QAAkB,CAC1C,CAAC,GAAA,CAAA,WAAW,IAAU,QAAgB,CACvC,CAAA,CACF,CACH,EAEA,CAAA;EAAA;EAAA,OAAuBkH,GAAmBC,GACxC,GAAA;AAAA,UAAa,IAAA,KAAK,SAAS,IAAI,MAC3BC;AAAAA,UACG,EAAA,SAGX;EASA;AAAA;AAAA,IAAMC,KAA6DC,EAAAA,EAAAA;AAAnE,IAGE,KAAA,CAAA,EAAA,YAEA,IAAA,MAAA,eACA,IAAA,IAAA,SACA,IAAA,MAAA,WACA,IAAA,KAAA,cAAmBjE,IAAM,IAAA,QAAgB,KAAG,GAC5C,GAAA,GAAA,WAAgBA,IAAM,IAAA,QACtB,GAAA,CAAA,GAAA,YACA,IAAA,GAAA,WACA,IAAA,MAAA,YACA,IAAA,MAAA,YACArD,KAAA,MAAA,UACA,IAAA,MAAA,YACA,IAAA,OAAA,WAAgBqD,IAAM,IAAM,MAAQ,IAAE,IACtC,EAAA,GAAA,iBAAkB,IAClB,MAAA,WACA,IAAA,KAAA,iBACA,IAAA,MAAA,kBACA,IAAA,MAAA,YACA,IAAA,GAAA,SACA,IAAA,GAAA,WACF,IAAsB,MACdkE,MAAAA;AAAAA,QAAuB,IAAA,SAAA,CAAA,EAAA,UAAuB,EAAA,MAClChH,CAAS,GAAG,IAAA,SAAA,CAAA,EAAA,WAAyB,EAAA,MAC/C,CAAA,GAAA,EAAAM,OAAO,GAAA,QAA2C,EAAA,QACnD2G,aAAAA,YAAY,CAAItJ,GAAS,CAAA,CAAA,QAAM,aAAAC,UAAA,MAAU,IAAA,SACpBD,GAAS,CAAA,CAAA,QAAM,aAAAC,UAAA,MAAU,IAAA,SAEjB,GAAA,QAAI,aAAAO,QAE/ByC,IAAAA;AAAAA,SAAI8D,SAAAA,CAAU,GACrB,MAAK5C;;AAAAA,QAAK,EAAA,uBAAA,SAAS;AACboF,UAAoB,IAAA,EAAA,QAAQ,SAAa,IAAA,cAC1B,GAAA,IAAA,EAAA,QAAQ,SAAa,IAAA,SACrCA;AAAAA,QAAkBC,CAAU,KAAA,CAAA,EAAA;AAEpB,QAEbC,IAAAA;AAAkB,QAAA,EAAiB,KAAE,CAAA,EAAA,QACf,CAAI,GAAG,EAAA,IAAA,EAAA;AAEf,MAAM,MACNF,IAAA,EAAA,GAAA,EAAM,MACpBD,IAAa,EAAIG,GAAkB,EACnCH,IAAa,EAAIG,GAAkB,EACzBC,IAAA,EAAA,GAAA,EAAA,cAEV,GAAA,CAAA;AAAMC,UAAuB,KAAA,EAAA,iBAAuB,EAAA,UAC5C,IAAA,GAAA,EAAA,QAAsB,EAAC,IAAA,GAAK,CAChCC,KAAAA,CACEA;AAAO,YAAA,OAAA,aAAA,mBAAU,eAAc,iBAExBA,IAAAA,IAAAA,aAAwB,WACtB,aAAA,SAAS,aAAT,mBAAmB,kBAAnB,mBAA0C,SAGnC,OAAA,EAAA,SAAS,iBAAwB,EAAA,SAAS,gBADjD,MAIAA,IAAO,MAAA,EAAA,SAAS,gBAET,IAAA,EAAA,SAAS,YAKxB,OAAKJ,KAAU,GAAA,SAAkC,GACzD,GAEDxG,CAAU;EAAA,CAAA,OAAM,aAAArC,WACd,MAAKwD;AAAAA,QAAK,EAAA,uBAAA,SAAS;AAEb+D,UAAgB,IAAA,EAAA,QAAQ,SAAa,IAAA,WAE/B2B;AAAA,UAAA,EAAM,MAAa,IAAA,EACnBA,OAAA,EAAM,MAAa,IAAA,EAAA;EAEpB,GAGX,CAAA,CAAA,CAAA,OACE,mBAAAxJ,KAAA8D,IACA,EAAA,KAAA,GAAA,eACA,GAAAvD,SACA,GAAA,WACA,GAAA,cACA,GAAA,WACA,GAAA,YACA,GAAA,WACA,GAAA,YACA,GAAA,YACAkB,IAAAyG,UACA,GAAA,YACA,GAAA,WACA,GAAA,iBACA,GAAA,WACA,GAAA,iBACA,GAAA,kBACA,GAAA,YACA,GAAA3D,SACA,GAAA,WC5lBOkF,EAAAA,CAAAA;AAAAA;ADqfb,ICrfaA,KACX,EAAA,aAAA,EAAA,eCDWC,EAAAA,CAAAA;AFqfb,IErfaA,KCCAC,EAAAA,wBAAAA;AHofb,IGpfaA,KAAiDC,EAAAA,yBAAAA;AHof9D,IGpf8DA,SCI5D,aAAA1I,YAAA,SAAkB,EAAA,eAIK,IAAM,GAAA,GAAI2I,GAAAA;AAAmBvE,QAA+B,QAAA,aAAAvF,SAAA,MAAA,IAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,aACjE+D,mBAAAA,KAAAA,aAAmC,EAAA,KAAA,GACtD,QCXYgG,GAAAA,SAAAA,KAAwDC,CAAAA;AAAAA,CCAxDC;ANqfb,IMrfaA,KAAAA,EAA8C,gBAAA;ANqf3D,IMrf2D,KCAPC,EAAAA,WAAAA;APqfpD,IOrfoDA,KCawBlF,EAAAA,eAAAA;ARwe5E,IQveE,SAGMmF,aAAAA,YAAAA,SAAiChG,EAAM,QAAA,IAAA,MACvCiG,GAAQC,EAAWnK,GAAO,GAAA;AAAA,QAC1BoK,IAAWD,SAAWnK,CAAAA,OAAOwB,GAAA,UAC7B6I,GAAWF,IAAWnK,EAAO,GAAA,OAAU,GACvCsK,IAA4BH,EAAWnK,GAAO,UAAA,GAAA,IAAA,EAAA,GAAA,UAA2B,GACzEc,IAASjB,EACb,GAAA,2BAA0C,GAAA,QAAAuK,aAAAA,SAAU,MAAA,IAAA,aAAU,EAAA,GAAA,GAAA,OAAA,GAAA,UAAA,GAAA,UACtDA,GAAUpK,2BAGX,EAAA,CAAOuK,GAASvK,CAAAA,GAAM,GAAA,GAAA,GAAQwK,CAAAA,CAAW;AAAA,aAAWA,aAAAA,iBAAAA,MAAW;AAAA,MAAA,OAC3DP,IAAA,EACTM,QAA4BvK,WAAM,WAC5B,WACD,UACE,EAAA;EAAA,GAAA,CAAA,GAAU,GAAA,GAEjBc,EAAO,IAAA,CAAA,OAAA,aAAAT,WAAA,MAAA,MAAA;;AAAA,YAAA,YAAA;EACO,GAAAwD,CAAAA,CAAAA,CAAAA,OAA0B,mBAAA9D,KAAA,aC5BP,EAAA,KAAA,GAAA0K,QAAA,GAAA,SAC/B,KAAA,CAAE;AAAA,CAAA;AT6eV,IS7eU,SACe,aAAAxJ,YAAA,SAAwBwD,GAAQN,GAAWnE;AAAM,QAAMA,EAAK,QAAkB,EAAA,QACrGgD,aAAAA,YAAAA,CAAAA,GAAAA,QAAAA,aAAAA,SAAgB,MAAM,IAAMlC,cAAO,GAAA,EAAA,EAAA,GAAA,GAAcqD,CAAAA,GAAWnE,CAAM,GAAA,CAAG,CAAA;AAAI,aAAe,aAAA0K,iBAAI,MAAA,MAAA,EAAA,cAAA,EAAA,EAC1E,GAAA,IAAA7G,CAAAA,GAAAA,EAAAA,GAAkB/C,CAAAA,OACrC,mBAAAf,KCHY4K,aAAAA,EAAAA,KAAAA,GAAAA,QAAkC,GAAA,SAAsB,KAAuB9G,CAC1F;AAAA,CAAA;AV2eF,IU3eE,SAA6C,aAAA5C,YAAA,SACtB,EAAA,MAAM,GAAI2J,GAAW5K,EAAK,GAAG,GAAM;AAAC,QAC3D,IAAA,SAAAgD,OAAgB,EAAM,UACH,GAAA,QAAQlB,aAAAA,SAAK,MAAA,IAAY,WAAA,CAAA,GAAM,CAAA,CACrCmI,CAAAA;AACV,aAAeA,aAAAA,iBAAW,MAAA;AAAA,SAAA,EAAA,QAAA,EAAA,OACrB,EAAA,MAAUpG,GAAAA,EAAAA;EAAAA,GAAAA,CAAkB/C,GAAAA,GAAAA,CAAAA,CAAAA,OChBzB+J,mBAAAA,KAAAA,aAAAA,EAA8DC,KAAAA,GAAAA,QCA3BC,GAAAA,SAAAC,KAAAA,CAAAA;AAAAA,CAAAA;AZqfhD,IYrfgDA,KAA8BC,ECa5BR,mBAAA;AbwelD,IaxekD,KAE9C,EAAAS,aAAa,EAAA,eACb,EAAA,CAAAC;AbqeJ,IareqB,SACjB,aAAAlK,YAAAoE,SACA,EAAA,WACA,IAAA,CAAA,GAAA,gBACA,IAAA,IAAA+F,eACA,GAAA,gBACA,GAAA,cAEA,GAAA,YACA,GAAA,kBAEA,GAAAC,iBAKF,GAAMpB,OAAiChG,GAAM,QAAAzC,IAAA,YACrCa,GAAO,MAAA,GAAO,MAAoC,GAEpDvB,GAASjB,EACb,GAAA,GACE;AAAA,QAAkBwC,IAAOoC,SAAQ,OAC/B,EAAA,UAAAY,GACA,EAAA,OAAA,GAAA,QAAAiG,EACA,QAAA,aAAAlL,YAAA,CAAA,GAAA,QAAA,aAAAN,SAAA,MACA,IAAA,cAAA,GAAA,GAAAsL,EAAAA,eACA,GAAA,gBACA,GAAA,cAEA,GAAA,YACA,GAAA,kBAEA,GAAAC,iBASFE,GACAC,OAGAF,GACAF,QAGAK,IAEF,YAKF,GAAA,MAAA/I,GAAU,MAGJ,GAAA,GAACvC,EAAO+K,CAAAA,GACV,CAAA,GAAA,GAAA,GAAApK,GAAOU,IAAA,GAAA,GAAA,GAAA,GAAU,GAAA,GACf,GAAA,CAAA,CAAA,GAAM,QAAA,aAAApB,YAAA,CAAA;AAAQ8K,aAAuC,aAAA7K,WAAI8D,MAAeA;AAAAA,QAAiC,CAAA,KAEhG8F,EAAAA,QAETnJ,EAAO,UAAU,IAAA,MACN,QAGMX,CAAAA,IAAe,EAAC,IAE7B,CAAA,IAAM,CACdW,EAAO,CAAA,CAAA,CAAA,GAAA,EAAA,GAAA,MAAA;AAAA,QAAiBqK,UAEdlB,MAEAlH,GAAsB,EAAA;IAAA;EAAA,GAAS,CAC3CL,GAAU,GAAA,GAAM,CACV,CAAA,OAAAvC,aAAAA,WAAAA,MAAW;AAAA,MAAA,iBACL,GAAU,EAAA;EAAA,GAAA,CAAA,GACTW,GAAAA,CAAAA,CAAAA,OAAAA,aAAAA,QAAA,MAAA,OAAA,aAAAT,WAAU,MAAIF;;AAAAA,QAAI,KAAA,EAAA,aAElB,OAAM,aAAN,mBACE,QAAU,QACN,EAAA,UAIH,IAAA,EAAW8J,QAEjB,GACD,EAAA,GAAM,MACJ;AAAA,QAAA,UAEA,MAAA,GAAA,EAAA;IAAA;EAAA,GAAA,CAAA,GAAA,EAAA,WAEH,CAAA,CAAU,OAAKnF,aAAAA,WAAAA,MAAAA,MACzB;AAAC,MC7GY4G,QAAAA;EAAAA,GAAAA,CAAAA,CAAAA,CAAAA,OAA2E,mBAAA3L,KAAA,aACpF4L,EACF9H,KAGM/C,GAAAA,QAAiB,EAAA,CAAM;AAAA,CAAA;Ad4e/B,Ic5emC8K,SAA4C,aAAA3K,YAAA,SAAA,EAAA,aAAA,IACrE,EAAA,GAAA,GAAU;AAAA,QAAA4C,QAAkB/C,aAAAA,SAAAA,MAAAA,IAAiB,iBAAA,CAAA,GACvD,CAAC,CAAA,CCXY+K;AAAAA,aACX,mBAAA9L,KAAA,aACA,EAAA,KAAA,GAAA,QACD,GCcK+L,SAA6BhL,KAAuC,CAAA;AAAA,CAAA;AhBoe1E,IgBpe0E,KAAqB,EAAA,gBAAU,EAAA,eACnEA,IAAuC,SAAO,KAAA,CAAA;AhBmepF,IgBne4FA,KAAO,CAAA,GAAA,MAAA,EAAU,OAEhGiL,OAA4C,EAAA,UAAAjH,KACvD;AhBgeF,IgB/dI,KAAA,CAAA,GAAA,MAAAoG,EAAa,OACb,QAAA,EAAAC,UACA,KAAU;AhB6dd,IgB5dI,SACA,aAAAlK,YAAA,SAAA,EAAA,WACA,IAAA,CAAA,GAAA,gBACA,IAAA,IAAA,QAAA,IAAA,CAAA,GAAA,UACA,IAAA,OAAA+K,kBAEAR,IACA,OAAA,oBACA,GAAAS,oBAMS,GAAW,WACpB,GAAQ,OAAK,GAAA,QAAAzK,IAAA,YAAA,GAAA,YAAA,GAAA,GAAyC,EAGxD,GAAA,GAAA;AAAA,IAAMyI,WAAuC,KAAA,QACvC,KAAE,yCAEaiC;AAAqB7J,QACtC,IAAA,SAAA,OAAA,EAAA,UACA,GAAA,EAAA,OAAA,GAAA,QACA,EAAA,QAAA,aAAAjC,YAAA,CAAA,GAAA,QAAA,aAAAN,SAAA,MAAA;AAAA,UACA,IAAA,IAAA,qBACA,GAAA,GAAA,EAAA,eAEA,GAAA,oBACA,GAAAmM,oBAGK,GAAA,WACA,GAAA,OAAA,GAAA,QAAmBE,IACnBrL,YAMPkL,GACAP,YAKAU,GACA,GAGIhM,EAAAA,CAAM2F;AAAWtG,WAEvB,EAAA,WAGM,GAAA,EAACW,mBACI,GAAA;EAAA,GAAA,CAAU,GAAA,GACf,GAAA,GAAA,GAAM,GAAAqB,IAAA,GAAA,GAAQ0J,GAAS,GAAKA,CAAyB,CAAA,GAAA,QAAc,aAAA9K,YAAI,CAAC+D;AAAW+G,aAE1EjB,aAAAA,WACJ,MAAM;AACXnJ,QAAO,CAAA,KAAA,EAAA,QAAU,EAAA,UAInBA,IAAmBX,MAEvBuC,QACS,CAAA,IAAA,EAAA,IAAA,CAAU,IAAA,CAAA,EAAA,CAAQyI,CAAAA,CACdlB,GAAAA,EACV,GAACnJ,MAAoBqK;AAAc,QAAC,UAGjCiB,MAAiB,GAAA,EAAA;IAAA;EAAA,GAAS,CACrB,GAAA,GAAA,GAAA,CAAAA,CAAAA,OAAA,aAAA/L,WAAA,MAAA;AAASgM,MAAAA,UAA4C,QACjDpC,GACJ,EAAA;EAAM,GACJmC,CAAA,GAAA,GAAA,CAAA,CAAA,OAASC,aAAAA,WAAAA,MAAsBlI;AAAAA,QAAgB,KAAU,EACrD8F,SAGbnJ,EAAQmJ,QAAmC,EAE/CvH,QACM,OAAAvC,GAAW,EAAA,CAAA,GAAA,CAAA,CAAA,GACTA,EAAI,GAAA,MAAA;AAAA,QAAA,QACCW,OAAAA,GAAAA,EAAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA;IAAA;EAAU,GAAA,CAAA,GAAIX,GAAI,GAAA,CAAA,CAAA,OAAA,aAAAE,WAAQ,MACtB4J;;AACJ,QAAA,KACLnJ,EAAO,aAAU,OAAA,aAAA,mBACN,QAIH,QAAA,EAAA,UAAsB,IAAA,EAAA,QAAA,GAAA,EAAA,GAE9B,MAAegE;AAAAA,QAAAA,UAAoBhE,MAAQ,GAAA,EAAA;IAAA;EACrD,GAAC,CC1HYwL,GAAAA,EAAAA,WAAAA,CAAAA,CAAAA,OCIAC,mBAAAA,KAAuBzH,aAClC9E,EAAAA,KAGA,GAAA,QAAQ,GAAAyE,SAAQ,KAAA,CAAAxC;AAAAA,CAAY;AlB6e9B,IkB7e8B,KAAAK,EAAkB,WAAA;AlB6ehD,IkB7egD,SAA+B1B,aAAAA,YAAqB,SAE5FqB,GAAAA,GAAe;AAAA,QAAQK,EAAAA,QAAqB,GAC9C,YAAQ,GAAA,kBAAM,GAAA,iBAAA,EAAA,QAAA,aAAAlC,YAAA,CAAA,GAAA,QAAA,aAAAN,SAAA,MAAA,MAAA,QAAA,MAAA,QAAA,QACP,MAEE0M,0EAIT,GAAA,CAAA,KAAA,IACA,WAAA,GAAA,KAAA,CAAA,IAAA,EAAA,UACA,MAAA,EAAA,eACA,IAAA,SACA,IAAA,OAAA,GAAA,mBACQ,GACR,iBACA,GAAW,gBAGX,KAAA,cAAA,KAAmBlK,oBACnB,KAAA,QAAA,IAAiBlB,MACjB,KAAA,WAAA,GAAA,OAAsB,QACnB,mBAImD,IAAA,EAAA,UAAA,MAAA,iBACxC,KAAAyC,GAAAA,sBACnB,MCrCY4I,GAAAA,EAAAA,CAAAA,GAAAA,CAAAA,GAAAA,GAAAA,GAAAA,CAAkCC,CAAAA;AAAUC,aCA5CC,mBAAAA,KCEX,aAAA,EAAA,KAA2B,GAAA,QAAA,GAAA,SAAA,KAAA,CAAA;AAAA,CAAA;ArBmf7B,IqBnf6B,KAAA,EAAA,UAAA;ArBmf7B,IqBnf6B,KAAA,EAAA,UAAA;ArBmf7B,IqBnf6B,KAAA,EAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6D7B;AAEY,IAAAC,MAAAA,QAAAA,EACVC,EAAAA,SAAA,CAAA,IAAA,UACAD,EAAAA,EAAAA,SAAA,CAAA,IAAA,UAAAhM,EACAiM,EAAAA,iBAAA,CAAA,IAAA,kBAHUD,IAAAA,MAAA,CAML,CAAA;AAAA,IAAME,KAAN,cACL,OAAA;EAAA,YAIE,EAAA,UAAW,IAMX,GAAA,WAAa,IAAA,CAAK,KAAG,GAMrB,GAAA,SAAW,IAAG,CAAC,GAMf,CAAA,GAAA,YAAc,IAAG,CAAA,GAAG,GAAI,GAMxB,CAAA,GAAA,UAAY,IAAG,CAAA,GAAG,GAAI,GAMtB,CAAA,GAAA,UAAW,IAMX,KAAA,UAAW,IAMX,KAAA,UAAW,IAQX,OAAA,YAAavL,KACb,OAAGwL,GACD,EAAA,IAAI,CACA,GAAA;AAAA,UAAA,cAAyB,GAAA,gBAC7B,EAAGA,GACH,GAAA,UAAA,oBACG,IAAA,CAAA,CAAA,YAAwBC,IACzB,QAAC,CAAA,CAAA,GAAA,CAAA,aAAyBC,IAC1B,QAAC,CAAA,CAAA,GAAA,CAAA,WAAuBC,IACxB,QAAC,CAAA,CAAA,GAAA,CAAA,cAA0BC,IAC3B,QAAC,CAAA,CAAA,GAAA,CAAA,YAAwBC,IACzB,QAAC,CAAA,CAAA,GAAA,CAAA,YAAwBC,IACzB,QAAC,CAAA,CAAA,GAAA,CAAA,YAAwBC,IACzB,QAAC,CAAA,CAAA,GAAA,CAAA,YAAwBC,IACzB,QAAC,CAAA,CAAA,GAAA,CAAA,cAA0BC,IAC5B,QACFjM,EACH,CACF,CAEa,CAAA,EAAA,CAAA;EAAA;AAAA;AAAAkM,IAAAA,KC1I0B,EAAA,EAAA;AD0I1BA,IC1I0B,SACrC,aAAAzM,YAAA,SAAA,EAAA,YAAc,GAAA,SAASqD,GAAAA,SAAsB,IAGvC5D,GAAAA,GAAAA,EAAAA,GAAIiN,GAAUC;AAAAA,QACpB5K,IAAgB,UAAM,eAClB,CAAA;AAAA,mBAAA0H,iBAAA,MAAA;AAAA,MAAamD,aACLnN,gBAAE,EAAA,QACVA,EAAE,QACN;EAAA,GAAMI,CAASjB,CAAAA,CAAQ;AAAA,QAAUiO,QAAAA,aAAAA,SAAgB,MAAG9N,IAAO,cAAA,EAAA,GAASU,GAAc,SAAcqN,KAAQ,EACjG,CAAA,GAAA,CAAA,GAAA,GAAA,CAAA,CAAA;AAAA,aAAA7J,mBAAAA,KAAAA,aAA6BpD,EAAAA,KAAAA,GAAAA,QAAAA,GAAAA,2BAAmD,GACxF,SCjBYkN,KAAAA,CAAAA;AAAAA,CAAAA;AFgJAN,IEhJAM,KAA0DC,EAAAA,iBAAAA;AFgJ1DP,IEhJ0DO,MEF1DC,EAAAA,cAAAA;AJkJAR,IIlJAQ,KCQiC,EAAA,eAAA;AL0IjCR,IK1IiC,SAAA,aAAAzM,YAAA,SAAA,EAAA,KACrC,GAAA,0BAGsB,GAAIkN,GAAYC,EAAAA,GAAU,GAAG;AAACA,QACrDnE,QAAalI,aAAAA,SAAUkC,MAAUA,IAAM,YAAA,GAAA,CAAA,GAAU,CAEvD,GAAA,CAAA,CAAA,GAAA,IAAAjB,SAAgB,OAAM,EAChBqL,UAAiC;AAAA,aAAA,aAAA3D,iBAAA,MAAA;AAAA,UAA2B2D,EAAAA,2BAG1CD,IAA8B,MAAA,EAAA,MAAA,IAAA,EAAA;EAAA,GAAA,CAAA,GAAA,GAAA,GAAA,CAE9C,CAAA,OAA4BtN,mBAAAA,KAAAA,aACtC,EAAC,KCtBYwN,GAAuC,QAAA,GAAAjO,SAAAkO,KAAAA,CAAAA;AAAAA,CAAAA;ANkJvCb,IMlJuCa,KCElD,EAAA,iBAAA,EAAA,eAAgB,EAAA,CAAA;APgJLb,IOhJK,KAAA,EAAA,gBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAyDlB;AAEO,IAAMa,KAAN,cAA8BhL,OAAO;EAC1C,YAAY,EACV,eAAA8B,IAAgB,IAChB,MAAAmJ,IAAO,MACP,OAAAC,IAAQ,KACR,OAAAC,IAAQ,CAAC,KAAK,CAAG,GACjB,KAAAC,IAAM,CAAC,KAAK,CAAG,GACf,SAAAC,IAAU,IACV,WAAAC,IAAY,CAAC,GAAG,CAAC,EACnB,IAAI,CAAA,GAAI;AACA,UAAA,mBAAmBC,GAAgB,gBAAgB,EACvD,eAAAzJ,GACA,YAAY,GACZ,UAAA,oBAAA,IACG,CAAA,CAAA,QAAQ,IAAgB,QAAC,CAC1B,CAAC,GAAA,CAAA,SAAS,IAAiB,QAAC,CAC5B,CAAC,GAAA,CAAA,SAAS,IAAiB,QAAC,CAC5B,CAAC,GAAA,CAAA,OAAO,IAAe,QAAC,CACxB,CAAC,GAAA,CAAA,WAAW,IAAmB,QAAC,CAChC,CAAC,GAAA,CAAA,aAAa,IAAqB,QAAC,CACrC,CAAA,CACF,CACH,EACF,CAEO;EAAA;AAAA;AAAA,IAA8C,KAAAkJ,EAAAA,IAAAA,EAAAA,eAAkCQ,GClFtE,CAAA;ADkFV,IClFU,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDjB,IAAMC,KAAN,cAA0BzL,OAAO;EAC/B,YAAY,EACV,MAAA0L,IAAO,SACP,YAAAC,IAAa,wBACb,UAAAC,IAAW,IACX,UAAAC,IAAW,IACX,OAAAC,IAAQ,WACR,QAAAC,IAAS,MACX,IAAuB,CAAA,GAAI;AACnBC,UAAAA,IAAAA,oBAAAA,IACH,CAAA,CAAA,eAAe,IAAY,QAAA,IAAc,SAC1C,CAAC,GAAA,CAAA,aAAa,IAAoB,QAAC,CACnC,CAAC,GAAA,CAAA,oBAAoB,IAAYL,QAAW,EAAA,MAC5C,CAAC,GAAA,CAAA,UAAU,IAAY,QAAA,IAAUG,MAAO,CAAA,CACxC,CAAC,GAAA,CAAA,WAAW,IAAkB,QAAC,CAChC,CAAA,CAED,CAAA;AAAA,UAAM,eAAyB,IAAE,EAAA,UAAU,EAE3C,CAAA;AAAA,UAAiC,IAAA,KAAK,SAAS,IAAI,aAE/CG;AAAAA,UACuB,EAAA,QAAQ,KAAK,wBAAoCP,GAAc,GAE5F,CAGO;EAAA;EAAA,wBAA4CA,GAAcE,GACzDM,GAAAA;AAAAA,UAAS,IAAA,SAAS,cAAc,QAChCC,GAAO,IAAA,MACO,IACdC,IAAOD,IAAOE,IAEbH;AAAA,MAAA,QAAe,EAAA,SAChB;AAAA,UAAU,IAAA,IAAkBA,cAAQ,GAAA,QAA2BI,gBAAgBC,gBAAeA,eAAa,aAC3GC,GAAUN,KAAO,EAAA,WAAW,IAElC;AAAI,QACI,CAAAjO,GAAA,OAAA,IAAI,MAAM,uBAGlBuO;AAAQ,IAAAvO,GAAA,UAAa,GAAGkO,GAAU,GAC1BK,CAAAA,GAAAvO,GAAA,OAAU2N,GAAAA,CAAAA,MAAcF,CAChCc,IAAQvO,GAAA,YAAY,UACZA,GAAA,eAAe,UACfA,GAAA,YAAY;AAEpB,aAAa,IAAG0B,GAAIgM,IAAW,EAAA,QAAQhM,KAC/B8M;AAAAA,YAAOd,IAAY,EACnB7I,CAAAA,GAAInD,IAAI0M,IACRtJ,GAAI,IAAA,KAAK,MAAqB,IAC5ByJ,CAAAA;AAAA,MAAAvO,GAAA,SAAe6E,GAAIsJ,IAAOA,IAAO,IAAGrJ,GAAIqJ,IAAOA,IAAQ,IAGjE5B,CAAAA;IAAAA;AAAAA,WAAQ,EAAA,cACDA,MAIJ;EAAA;AAAA;AAAA,IAA8B,SAG/B,aAAA9M,YAAA,CAAAgO,EAAAA,MAAO,IAAA,SACP,YAAa,IAAA,wBACb,UACA,IAAA,IAAA,UACA,IAAA,IAAAI,OAAQ,IAAA,WACR,QAEFrI,IAAAA,MACG,GACH,MAAA;AAAMlG,QACJ,QAAM,aAAAhB,SAAA,MAAIkP,IAAc,GAAA,EAAA,YAAY,GAAAC,MAAM,GAAA,UAAU,GAAA,UAAU,GAAAI,OAAO,GAAA,QACpEH,EAAYC,CAAAA,GAAUC,CAAUC,GAAOC,GAAQL,GAAI,GAAA,GAAA,CAAA,CAAA;AAAA,aAAA,mBAAAlP,KAAA,aAE/BiH,EAAAA,KAAMiJ,GAAQnP,QChIrB,EAClB,CAAA;AAAA,CAAA;AD+GK,IC/GL,KAAA,EAAA,gBAA2B;;;;;;;;;;;;;;IAe7B;AAEO,IAAMoP,KAAN,cAA8B3M,OAAO;EAC1C,YAAY,EAAE,eAAA8B,IAAgB,IAAA,QAAA8K,IAAA,EAAA,IAAA,CAAA,GAA+B;AACrD,UAAA,eAAeC,GAAY,gBAAgB,EAC/C,eAAA/K,GACA,YAAY,GACZ,UAAc,oBAAA,IAAA,CAAA,CAAA,UAAoD,IAAIgL,QAAQF,CAAM,CAAG,CACxF,CACH,EACF,CAEa;EAAA;AAAA;AAAA,IAAAG,KACX,EAAA,IAAA,EAAA,eCVWC,GAAuB,CAAA;ADSvB,ICTuB,SAG9B,aAAAtP,YAAA,CAAAuP,EAAAA,SACA,GAAA,mBACA,GAAAC,SACA,GAAA,sBACA,IAAA,MAAAC,UACA,IAAA,GAAA,WACA,IAAA,IAAA,gBACA,IAAA,GAAA,eACA,IAAA,IAAA,iBACA,IAAA,GAAA,WACAlP,KAAA6N,GACA,OAAA,GAAA,YAGC,IACH,EAAA,GAAA,MAAQ;AAAA,QAAA5K,EAAQ,QAAM,GAAI1C,OACXlC,EAAAA,IAAQ,SAAA,GAAM,QAAA,aAAAC,SAAI6Q,MAAoBlM,IAAUA,0CAAa,GAAC,CAG7E,GAAA,CAAA,GAAA,CAAA,CAAAzB;AAAgB,aACd4N,aAAAA,iBAAW9P,MAAO;AAAA,eAAA,EAAA,eAEhB,EAAA,OAAA,GAAA4P,UACA,GAAA,iBACA,GAAA,WACAlP,IAAA,WACA,GAAA,gBACA,GAAA,eACA,GAAA,mBACA,GAAA,YACA,GAAAgP,SACA,GAAA,sBAIFnB,EACAqB,CAAAA;EACAG,GACA7E,CAAAA,GACA8E,GACAC,GACAC,GACAC,IACAT,GACAU,GACA,GACD,GAEDlO,GAAgB,GAAA,CAAM,CAChByN,OAAAA,aAAAA,iBAAgB3P,MAAA;AAAA,SAAA,EAAA,eAA8B,EAAC,OAAE,CAAA,EAAA,YAAwB,IAAO,EAAC,MACpE,CAAC,CAAA;EAAA,GAAA,CAAA,GAAA,CAAA,CAAA,OAAA,mBAAAf,KAEZ,aAAU8D,EAAAA,KACpB,GACF,QAAA,EAAA,CAAA;AAAA,CAAA;", "names": ["p", "_setPrototypeOf", "o", "matrix", "col", "_construct", "Parent", "args", "Class", "triangle", "matrix", "p", "cross", "length", "buffer", "distance", "z", "z2", "matrix", "p", "index", "zero", "one", "add", "addValue", "sub", "subValue", "scale", "dot", "z", "lengthSqr", "length", "distance", "buffer", "swizzle", "z", "lerp", "center", "distance", "add", "Grad", "z", "x", "y", "seed", "simplex2", "j", "simplex3", "k", "j2", "k2", "perlin2", "perlin3", "Z", "w", "Generator", "buffer", "center", "rsqw", "exp", "easing", "_getPrototypeOf", "o", "RoundedPlaneGeometry", "j", "z", "makeUVs", "v0", "v1", "v2", "buffer", "p", "j", "p", "k", "z", "selectionContext", "select", "useState", "j", "value", "useMemo", "z", "w", "props", "group", "O", "api", "M", "A", "enabled", "changed", "current", "children", "t", "e", "EffectComposerContext", "n", "effect", "Tt", "he", "B", "_camera", "_scene", "resolutionScale", "renderPriority", "depthBuffer", "enableNormalPass", "p", "frameBufferType", "HalfFloatType", "gl", "defaultScene", "defaultCamera", "size", "useThree", "composer", "normalPass", "effectComposer", "EffectComposerImpl", "RenderPass", "scene", "downSamplingPass", "normalPass2", "NormalPass", "multisampling", "useEffect", "useFrame", "_", "currentAutoClear", "autoClear", "useRef", "useLayoutEffect", "groupInstance", "i", "child", "effects", "isConvolution", "next", "Effect", "pass", "EffectPass", "passes", "currentTonemapping", "useImperativeHandle", "ref", "oe", "S", "F", "state", "jsx", "resolveRef", "components", "defaults", "opacity", "Component", "key", "camera", "args", "React", "ee", "THREE", "forwardRef", "focusDistance", "resolutionX", "depthTexture", "autoFocus", "target", "DepthOfFieldEffect", "blendFunction", "Vector3", "<PERSON><PERSON><PERSON>", "MaskFunction", "worldFocusRange", "Autofocus", "smoothTime", "dofRef", "pointer", "useContext", "<PERSON><PERSON><PERSON><PERSON>", "depthPickingPass", "copyPass", "hitpoint", "getHit", "useCallback", "x", "y", "ndc", "update", "delta", "updateTarget", "ae", "followMouse", "hit", "hitpointRef", "targetRef", "fref", "jsxs", "Fragment", "debug", "createPortal", "K", "Q", "DepthOfField", "Lens<PERSON><PERSON><PERSON><PERSON><PERSON>", "LensFlareEffect", "glareSize", "lensPosition", "screenRes", "starPoints", "flareSize", "flareSpeed", "flareShape", "animated", "anamorphic", "colorGain", "lensDirtTexture", "haloScale", "secondaryGhosts", "aditionalStreaks", "ghostScale", "<PERSON><PERSON><PERSON><PERSON>", "_inputBuffer", "deltaTime", "time", "LensFlareWrapped", "Bt", "viewport", "<PERSON><PERSON><PERSON><PERSON>", "uLensPosition", "uOpacity", "projectedPosition", "raycaster", "intersects", "object", "screenRes2", "Bloom", "BrightnessContrast", "ChromaticAberration", "ChromaticAberrationEffect", "ColorAverageEffect", "ColorDepth", "kt", "De<PERSON><PERSON>", "DotScreenEffect", "invalidate", "delay", "useVector2", "duration", "strength", "chromaticAberrationOffset", "active", "GlitchMode", "D", "L", "Grid", "GridEffect", "HueSaturation", "Xt", "wrapEffect", "NoiseEffect", "Ht", "selection", "<PERSON><PERSON><PERSON>er", "pulseSpeed", "xRay", "patternTexture", "edgeStrength", "height", "width", "Pixelation", "granularity", "PixelationEffect", "Scanline", "addLight", "SelectiveBloom", "intensity", "mipmapBlur", "SelectiveBloomEffect", "ignoreBackground", "lights", "light", "Sepia", "SSAO", "SSAOEffect", "SMAA", "SMAAEffect", "Yt", "FXAA", "RampType", "RampType2", "RampEffect", "params", "rampType", "rampStart", "rampEnd", "startColor", "endColor", "rampBias", "<PERSON><PERSON><PERSON>", "rampMask", "rampInvert", "<PERSON><PERSON>", "useLoader", "TextureLoader", "SRGBColorSpace", "TextureEffect", "texture", "ToneMapping", "ro", "ShockWave", "LUT3DEffect", "lut", "tetrahedralInterpolation", "TiltShift", "TiltShiftEffect", "blur", "taper", "start", "end", "samples", "direction", "TiltShiftShader", "gt", "ASCIIEffect", "font", "characters", "fontSize", "cellSize", "color", "invert", "uniforms", "charactersTextureUniform", "canvas", "SIZE", "CELL", "MAX_PER_ROW", "RepeatWrapping", "NearestFilter", "context", "char", "c", "WaterEffectImpl", "factor", "WaterShader", "Uniform", "WaterEffect", "N8AO", "halfRes", "quality", "aoRadius", "N8AOPostPass", "applyProps", "<PERSON><PERSON><PERSON><PERSON>", "aoSamples", "denoiseSamples", "denoise<PERSON><PERSON><PERSON>", "renderMode", "depthAwareUpsampling"]}