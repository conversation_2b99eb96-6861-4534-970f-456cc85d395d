/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  background: #000;
  color: #00ff00;
  overflow-x: hidden;
}

/* Fullscreen Dither Background */
.dither-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  background: #000;
}

/* Terminal Overlay */
.terminal-overlay {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3); /* Subtle dark overlay for readability */
}

/* Terminal Container */
.terminal-container {
  width: 100%;
  max-width: 800px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #00ff00;
  border-radius: 0;
  box-shadow: 
    0 0 20px rgba(0, 255, 0, 0.3),
    inset 0 0 20px rgba(0, 255, 0, 0.1);
  backdrop-filter: blur(5px);
}

/* Terminal Header */
.terminal-header {
  background: rgba(0, 255, 0, 0.1);
  padding: 10px 20px;
  border-bottom: 1px solid #00ff00;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
}

.terminal-title {
  color: #00ff00;
  text-shadow: 0 0 5px #00ff00;
}

.terminal-status {
  color: #00ff00;
  animation: pulse 2s infinite;
}

/* Terminal Content */
.terminal-content {
  padding: 20px;
  line-height: 1.6;
  font-size: 16px;
}

.terminal-line {
  margin-bottom: 10px;
  display: flex;
  gap: 10px;
}

.prompt {
  color: #00ff00;
  font-weight: bold;
  text-shadow: 0 0 3px #00ff00;
}

.command {
  color: #ffffff;
  background: rgba(0, 255, 0, 0.1);
  padding: 2px 6px;
}

.terminal-output {
  margin: 20px 0;
  color: #00ff00;
}

.terminal-output p {
  margin-bottom: 8px;
  text-shadow: 0 0 2px #00ff00;
}

/* Menu Items */
.terminal-menu {
  margin-top: 30px;
  border-top: 1px solid rgba(0, 255, 0, 0.3);
  padding-top: 20px;
}

.menu-item {
  display: flex;
  gap: 15px;
  margin-bottom: 12px;
  padding: 8px;
  transition: background 0.3s ease;
  cursor: pointer;
}

.menu-item:hover {
  background: rgba(0, 255, 0, 0.1);
  box-shadow: inset 0 0 10px rgba(0, 255, 0, 0.2);
}

.menu-key {
  color: #00ff00;
  font-weight: bold;
  text-shadow: 0 0 5px #00ff00;
  min-width: 30px;
}

.menu-text {
  color: #cccccc;
}

/* Terminal Footer */
.terminal-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  border-top: 1px solid #00ff00;
  padding: 10px 20px;
  font-size: 12px;
  color: #00ff00;
  text-align: center;
  z-index: 20;
  backdrop-filter: blur(10px);
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.blinking-cursor {
  animation: blink 1s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .terminal-overlay {
    padding: 10px;
  }
  
  .terminal-container {
    max-width: 100%;
  }
  
  .terminal-content {
    padding: 15px;
    font-size: 14px;
  }
  
  .terminal-header {
    padding: 8px 15px;
    font-size: 12px;
  }
  
  .terminal-footer {
    font-size: 10px;
    padding: 8px 15px;
  }
}

/* Scanline effect for extra retro feel */
.terminal-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    transparent 50%,
    rgba(0, 255, 0, 0.03) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 1;
}

.terminal-content {
  position: relative;
  z-index: 2;
}

/* Glitch effect on hover for menu items */
.menu-item:hover .menu-text {
  animation: glitch 0.3s ease-in-out;
}

@keyframes glitch {
  0% { transform: translateX(0); }
  20% { transform: translateX(-2px); }
  40% { transform: translateX(2px); }
  60% { transform: translateX(-1px); }
  80% { transform: translateX(1px); }
  100% { transform: translateX(0); }
}
