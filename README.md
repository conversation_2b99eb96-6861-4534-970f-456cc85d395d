# Dither Component

A React component that creates interactive dithered wave effects using Three.js and custom shaders.

## Features

- **Custom Fragment Shaders**: Uses noise-based wave patterns with dithering effects
- **Mouse Interaction**: Interactive mouse effects that influence the wave patterns
- **Postprocessing Effects**: Retro-style dithering with customizable color palettes
- **Configurable Parameters**: Extensive customization options for waves, colors, and effects

## Installation

The required dependencies are already installed:

```bash
npm install three postprocessing @react-three/fiber @react-three/postprocessing
```

## Usage

```tsx
import Dither from './components/Dither';

function App() {
  return (
    <div style={{ width: '100%', height: '600px', position: 'relative' }}>
      <Dither
        waveColor={[0.5, 0.5, 0.5]}
        disableAnimation={false}
        enableMouseInteraction={true}
        mouseRadius={0.3}
        colorNum={4}
        waveAmplitude={0.3}
        waveFrequency={3}
        waveSpeed={0.05}
      />
    </div>
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `waveSpeed` | `number` | `0.05` | Speed of wave animation |
| `waveFrequency` | `number` | `3` | Frequency of wave patterns |
| `waveAmplitude` | `number` | `0.3` | Amplitude of wave effects |
| `waveColor` | `[number, number, number]` | `[0.5, 0.5, 0.5]` | RGB color values (0-1) |
| `colorNum` | `number` | `4` | Number of colors in dither palette |
| `pixelSize` | `number` | `2` | Size of dither pixels |
| `disableAnimation` | `boolean` | `false` | Disable wave animation |
| `enableMouseInteraction` | `boolean` | `true` | Enable mouse interaction effects |
| `mouseRadius` | `number` | `1` | Radius of mouse interaction effect |

## Development

Start the development server:

```bash
npm run dev
```

Build for production:

```bash
npm run build
```

## Technical Details

The component uses:
- **Three.js** for 3D rendering
- **@react-three/fiber** for React integration
- **postprocessing** for shader effects
- **Custom GLSL shaders** for wave generation and dithering
- **Perlin noise** for organic wave patterns
- **Bayer matrix dithering** for retro visual effects
