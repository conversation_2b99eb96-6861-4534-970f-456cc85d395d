import {
  Canvas,
  _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPointerEvents,
  createPortal,
  createRoot,
  dispose,
  extend,
  flushGlobalEffects,
  flushSync,
  getRootState,
  invalidate,
  reconciler,
  threeTypes,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
} from "./chunk-ZQDFDSS7.js";
import "./chunk-JNNNAK6O.js";
import "./chunk-HSUUC2QV.js";
import "./chunk-TIG2MKL5.js";
import "./chunk-DC5AMYBS.js";
export {
  Canvas,
  threeTypes as ReactThreeFiber,
  _roots,
  act,
  addAfterEffect,
  addEffect,
  addTail,
  advance,
  applyProps,
  buildGraph,
  context,
  createEvents,
  createPortal,
  createRoot,
  dispose,
  createPointerEvents as events,
  extend,
  flushGlobalEffects,
  flushSync,
  getRootState,
  invalidate,
  reconciler,
  unmountComponentAtNode,
  useFrame,
  useGraph,
  useInstanceHandle,
  useLoader,
  useStore,
  useThree
};
//# sourceMappingURL=@react-three_fiber.js.map
