import Dither from './components/Dither'
import './App.css'

function App() {
  return (
    <>
      {/* Fullscreen Dither Background */}
      <div className="dither-background">
        <Dither
          waveColor={[0.8, 0.8, 0.8]} // Light gray/white waves
          disableAnimation={false}
          enableMouseInteraction={true}
          mouseRadius={0.4}
          colorNum={3} // Reduced colors for more contrast
          waveAmplitude={0.2} // Subtle wave effect
          waveFrequency={2}
          waveSpeed={0.03} // Slower, more ambient
          pixelSize={3} // Larger pixels for retro effect
        />
      </div>

      {/* Terminal-styled Content Overlay */}
      <div className="terminal-overlay">
        <div className="terminal-container">
          <div className="terminal-header">
            <span className="terminal-title">SYSTEM_TERMINAL_v2.1</span>
            <span className="terminal-status">● ONLINE</span>
          </div>

          <div className="terminal-content">
            <div className="terminal-line">
              <span className="prompt">user@system:~$</span>
              <span className="command">welcome_to_the_matrix</span>
            </div>

            <div className="terminal-output">
              <p>Initializing neural interface...</p>
              <p>Connection established.</p>
              <p>Reality.exe has stopped working.</p>
              <br />
              <p>Welcome to the digital realm.</p>
              <p>Your consciousness has been uploaded.</p>
              <br />
              <p className="blinking-cursor">Press any key to continue_</p>
            </div>

            <div className="terminal-menu">
              <div className="menu-item">
                <span className="menu-key">[1]</span>
                <span className="menu-text">Access Database</span>
              </div>
              <div className="menu-item">
                <span className="menu-key">[2]</span>
                <span className="menu-text">Run Diagnostics</span>
              </div>
              <div className="menu-item">
                <span className="menu-key">[3]</span>
                <span className="menu-text">Exit Matrix</span>
              </div>
            </div>
          </div>
        </div>

        <div className="terminal-footer">
          <span>System Status: OPERATIONAL | Memory: 2048MB | CPU: 99.7%</span>
        </div>
      </div>
    </>
  )
}

export default App
